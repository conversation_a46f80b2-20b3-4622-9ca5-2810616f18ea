{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "noImplicitAny": false, "strictNullChecks": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "backups/**/*", "dev-resources/debug/**/*", "enhanced-cors-proxy.ts", "api-proxy-route.ts", "api-proxy-route-fixed.ts", "src/**/*", "backend/**/*", "deploy/**/*", "scripts/**/*", "utils/**/*", "reference-*.tsx", "**/*.bak", "**/*.backup", "dev-resources/mocks/**/*", "dev-resources/tests/**/*", "dev-resources/unused/**/*"]}