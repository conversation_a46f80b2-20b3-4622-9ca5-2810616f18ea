/**
 * 应用配置
 * 提供全局配置常量和环境变量访问
 */

// 全局站点配置 - 修改此处以更改所有环境的基本URL
export const SITE_CONFIG = {
  // 基本URL配置 - 在此处修改以全局生效
  baseUrl: process.env.NODE_ENV === 'production'
    ? 'https://ooaclkofmixc.sealoshzh.site'  // 生产环境URL
    : 'http://localhost:3000',               // 开发环境URL

  // API基础URL
  apiBaseUrl: 'https://services.vcrm.vip:8000',

  // 调试模式配置
  debug: {
    // 是否启用调试模式
    // 通过环境变量NEXT_DEBUG或APP_DEBUG控制
    // 设置为'true'开启，设置为'false'或不设置则关闭
    // 默认值: false (关闭)
    enabled: process.env.NEXT_DEBUG === 'true' || process.env.APP_DEBUG === 'true' || false,

    // 调试级别
    // 通过环境变量NEXT_DEBUG_LEVEL控制
    // 0: 禁用所有日志
    // 1: 只显示错误日志
    // 2: 显示错误和警告日志
    // 3: 显示错误、警告和信息日志
    // 4: 显示所有日志，包括详细调试信息
    // 默认值: 3 (显示错误、警告和信息日志)
    level: parseInt(process.env.NEXT_DEBUG_LEVEL || '3', 10),

    // 是否在浏览器控制台显示调试信息
    // 通过环境变量NEXT_DEBUG_BROWSER控制
    // 设置为'true'开启，设置为'false'关闭
    // 默认值: true (开启)
    showInBrowser: process.env.NEXT_DEBUG_BROWSER === 'true' || true,

    // 是否在服务器控制台显示调试信息
    // 通过环境变量NEXT_DEBUG_SERVER控制
    // 设置为'true'开启，设置为'false'关闭
    // 默认值: true (开启)
    showInServer: process.env.NEXT_DEBUG_SERVER === 'true' || true,
  },

  // 浏览器控制台安全配置
  console: {
    // 是否完全禁用浏览器控制台
    // 通过环境变量DISABLE_CONSOLE控制
    // 设置为'true'将禁用所有console输出，包括console.log, console.error等
    // 设置为'false'或不设置则不禁用
    // 默认值: false (不禁用)
    // 注意: 此设置会影响所有环境，包括开发环境
    disabled: process.env.DISABLE_CONSOLE === 'true' || false,

    // 是否在生产环境中禁用浏览器控制台
    // 通过环境变量DISABLE_CONSOLE_IN_PRODUCTION控制
    // 设置为'true'将仅在生产环境(NODE_ENV=production)中禁用console输出
    // 设置为'false'则在生产环境中也不禁用
    // 默认值: true (在生产环境中禁用)
    // 注意: 此设置仅在NODE_ENV=production时生效
    disabledInProduction: process.env.DISABLE_CONSOLE_IN_PRODUCTION === 'false' ? false : true,

    // 允许的最低日志级别
    // 通过环境变量CONSOLE_MIN_LEVEL控制
    // 0: 禁用所有日志
    // 1: 只允许错误日志(console.error)
    // 2: 允许错误和警告日志(console.error, console.warn)
    // 3: 允许错误、警告和信息日志(console.error, console.warn, console.log/info)
    // 4: 允许所有日志，包括调试日志(console.debug, console.trace等)
    // 默认值: 根据环境自动设置 - 生产环境为1(只允许错误日志)，开发环境为3(允许错误、警告和信息日志)
    minLevel: parseInt(
      process.env.CONSOLE_MIN_LEVEL ||
      (process.env.NODE_ENV === 'production' ? '1' : '3'),
      10
    ),

    // 是否隐藏敏感URL信息
    // 通过环境变量HIDE_SENSITIVE_URLS控制
    // 设置为'true'将在日志中隐藏敏感URL信息，如API地址等
    // 设置为'false'则不隐藏
    // 默认值: true (隐藏敏感URL信息)
    // 临时禁用敏感URL隐藏，用于调试
    hideSensitiveUrls: process.env.HIDE_SENSITIVE_URLS === 'false' ? false : true,

    // 敏感URL列表，这些URL将在日志中被隐藏或替换
    // 可以通过环境变量SENSITIVE_URLS添加更多URL，用逗号分隔
    sensitiveUrls: [
      'services.vcrm.vip',
      'ooaclkofmixc.sealoshzh.site',
      'api/admin',
      'api/refresh-permissions',
      'api/init',
      'system-initializer',
      ...(process.env.SENSITIVE_URLS ? process.env.SENSITIVE_URLS.split(',') : []),
    ],

    // 是否禁用控制台的开发者工具检测
    // 通过环境变量DISABLE_DEVTOOLS_DETECTION控制
    // 设置为'true'将尝试检测并阻止开发者工具的使用
    // 设置为'false'则不检测
    // 默认值: false (不检测)
    // 注意: 这不是100%可靠的，但可以增加一层保护
    disableDevToolsDetection: process.env.DISABLE_DEVTOOLS_DETECTION === 'true' ? true : false,

    // 是否在控制台中显示自定义警告消息
    // 通过环境变量SHOW_CONSOLE_WARNING控制
    // 设置为'true'将在控制台中显示警告消息
    // 设置为'false'则不显示
    // 默认值: true (显示警告消息)
    showWarningMessage: process.env.SHOW_CONSOLE_WARNING === 'false' ? false : true,

    // 控制台警告消息
    // 通过环境变量CONSOLE_WARNING_MESSAGE控制
    // 默认值: 警告：这是一个仅供开发人员使用的功能。请勿在此处粘贴或执行任何代码，否则可能导致您的账户被盗用。
    warningMessage: process.env.CONSOLE_WARNING_MESSAGE || '警告：这是一个仅供开发人员使用的功能。请勿在此处粘贴或执行任何代码，否则可能导致您的账户被盗用。',
  },

  // API安全配置
  api: {
    // 是否在前端隐藏API端点URL
    // 通过环境变量HIDE_API_ENDPOINTS控制
    // 设置为'true'在日志和错误中隐藏API端点，设置为'false'不隐藏
    // 默认值: true (隐藏API端点)
    hideEndpoints: process.env.HIDE_API_ENDPOINTS === 'false' ? false : true,

    // 是否启用API请求限流
    // 通过环境变量ENABLE_API_RATE_LIMITING控制
    // 设置为'true'启用限流，设置为'false'不启用
    // 默认值: true (启用限流)
    enableRateLimiting: process.env.ENABLE_API_RATE_LIMITING === 'false' ? false : true,

    // API请求超时时间（毫秒）
    // 通过环境变量API_TIMEOUT控制
    // 默认值: 30000 (30秒)
    timeout: parseInt(process.env.API_TIMEOUT || '30000', 10),

    // 是否在请求头中包含API版本
    // 通过环境变量INCLUDE_API_VERSION_HEADER控制
    // 设置为'true'包含版本头，设置为'false'不包含
    // 默认值: true (包含版本头)
    includeVersionHeader: process.env.INCLUDE_API_VERSION_HEADER === 'false' ? false : true,

    // API版本
    // 通过环境变量API_VERSION控制
    // 默认值: v1
    version: process.env.API_VERSION || 'v1',
  },

  // 错误处理配置
  error: {
    // 是否在前端显示详细错误信息
    // 通过环境变量SHOW_DETAILED_ERRORS控制
    // 设置为'true'显示详细错误，设置为'false'只显示通用错误
    // 默认值: false (生产环境不显示详细错误)
    showDetailedErrors: process.env.SHOW_DETAILED_ERRORS === 'true' ? true : false,

    // 是否记录前端错误到服务器
    // 通过环境变量LOG_FRONTEND_ERRORS控制
    // 设置为'true'记录错误，设置为'false'不记录
    // 默认值: true (记录错误)
    logFrontendErrors: process.env.LOG_FRONTEND_ERRORS === 'false' ? false : true,

    // 前端错误日志API端点
    // 通过环境变量ERROR_LOG_ENDPOINT控制
    // 默认值: /api/log/error
    errorLogEndpoint: process.env.ERROR_LOG_ENDPOINT || '/api/log/error',

    // 是否在错误日志中包含用户信息
    // 通过环境变量LOG_USER_INFO_WITH_ERRORS控制
    // 设置为'true'包含用户信息，设置为'false'不包含
    // 默认值: false (不包含用户信息)
    logUserInfoWithErrors: process.env.LOG_USER_INFO_WITH_ERRORS === 'true' ? true : false,
  },

  // 用户界面安全配置
  ui: {
    // 是否在UI中隐藏敏感信息
    // 通过环境变量HIDE_SENSITIVE_UI_INFO控制
    // 设置为'true'隐藏敏感信息，设置为'false'不隐藏
    // 默认值: true (隐藏敏感信息)
    hideSensitiveInfo: process.env.HIDE_SENSITIVE_UI_INFO === 'false' ? false : true,

    // 敏感信息掩码字符
    // 通过环境变量SENSITIVE_INFO_MASK控制
    // 默认值: ****
    sensitiveInfoMask: process.env.SENSITIVE_INFO_MASK || '****',

    // 是否启用自动登出
    // 通过环境变量ENABLE_AUTO_LOGOUT控制
    // 设置为'true'启用，设置为'false'不启用
    // 默认值: true (启用自动登出)
    enableAutoLogout: process.env.ENABLE_AUTO_LOGOUT === 'false' ? false : true,

    // 自动登出超时时间（秒）
    // 通过环境变量AUTO_LOGOUT_TIMEOUT控制
    // 默认值: 1800 (30分钟)
    autoLogoutTimeout: parseInt(process.env.AUTO_LOGOUT_TIMEOUT || '1800', 10),
  },

  // 内容安全策略配置
  csp: {
    // 是否启用内容安全策略
    // 通过环境变量ENABLE_CSP控制
    // 设置为'true'开启，设置为'false'关闭
    // 默认值: true (开启)
    enabled: process.env.ENABLE_CSP === 'false' ? false : true,

    // CSP策略严格模式
    // 通过环境变量CSP_STRICT_MODE控制
    // 设置为'true'启用严格模式，设置为'false'使用较宽松的策略
    // 默认值: true (严格模式)
    strictMode: process.env.CSP_STRICT_MODE === 'false' ? false : true,

    // 允许的脚本来源
    // 通过环境变量CSP_SCRIPT_SRC控制，用逗号分隔
    // 默认值: 'self' (只允许同源脚本)
    scriptSrc: process.env.CSP_SCRIPT_SRC?.split(',') || ["'self'"],

    // 允许的样式来源
    // 通过环境变量CSP_STYLE_SRC控制，用逗号分隔
    // 默认值: 'self' (只允许同源样式)
    styleSrc: process.env.CSP_STYLE_SRC?.split(',') || ["'self'"],

    // 允许的图片来源
    // 通过环境变量CSP_IMG_SRC控制，用逗号分隔
    // 默认值: 'self' data: (允许同源图片和data URI)
    imgSrc: process.env.CSP_IMG_SRC?.split(',') || ["'self'", "data:"],

    // 允许的连接来源
    // 通过环境变量CSP_CONNECT_SRC控制，用逗号分隔
    // 默认值: 'self' (只允许同源连接)
    // 添加services.vcrm.vip:8000，允许连接到外部API
    connectSrc: process.env.CSP_CONNECT_SRC?.split(',') || ["'self'", "https://services.vcrm.vip:8000"],

    // 是否在报告模式下运行CSP
    // 通过环境变量CSP_REPORT_ONLY控制
    // 设置为'true'只报告违规而不阻止，设置为'false'阻止违规
    // 默认值: false (阻止违规)
    reportOnly: process.env.CSP_REPORT_ONLY === 'true' ? true : false,

    // CSP违规报告URL
    // 通过环境变量CSP_REPORT_URI控制
    // 默认值: 空 (不报告违规)
    reportUri: process.env.CSP_REPORT_URI || '',
  },

  // 浏览器存储安全配置
  storage: {
    // Cookie安全配置
    cookie: {
      // 是否使用安全Cookie（只在HTTPS连接中发送）
      // 通过环境变量SECURE_COOKIES控制
      // 设置为'true'启用安全Cookie，设置为'false'不启用
      // 默认值: true (生产环境使用安全Cookie)
      secure: process.env.SECURE_COOKIES === 'false' ? false : true,

      // 是否使用HttpOnly Cookie（JavaScript无法访问）
      // 通过环境变量HTTP_ONLY_COOKIES控制
      // 设置为'true'启用HttpOnly，设置为'false'不启用
      // 默认值: true (启用HttpOnly)
      httpOnly: process.env.HTTP_ONLY_COOKIES === 'false' ? false : true,

      // Cookie的SameSite属性
      // 通过环境变量COOKIE_SAME_SITE控制
      // 可选值: 'strict', 'lax', 'none'
      // 默认值: 'lax'
      sameSite: process.env.COOKIE_SAME_SITE || 'lax',

      // Cookie的过期时间（秒）
      // 通过环境变量COOKIE_MAX_AGE控制
      // 默认值: 86400 (24小时)
      maxAge: parseInt(process.env.COOKIE_MAX_AGE || '86400', 10),
    },

    // localStorage配置
    localStorage: {
      // 是否加密localStorage中的敏感数据
      // 通过环境变量ENCRYPT_LOCAL_STORAGE控制
      // 设置为'true'加密数据，设置为'false'不加密
      // 默认值: true (加密敏感数据)
      encryptSensitiveData: process.env.ENCRYPT_LOCAL_STORAGE === 'false' ? false : true,

      // localStorage数据的过期时间（秒）
      // 通过环境变量LOCAL_STORAGE_EXPIRY控制
      // 默认值: 604800 (7天)
      defaultExpiry: parseInt(process.env.LOCAL_STORAGE_EXPIRY || '604800', 10),

      // 是否在用户登出时清除localStorage
      // 通过环境变量CLEAR_STORAGE_ON_LOGOUT控制
      // 设置为'true'清除，设置为'false'不清除
      // 默认值: true (清除)
      clearOnLogout: process.env.CLEAR_STORAGE_ON_LOGOUT === 'false' ? false : true,
    },
  },
};

// 应用URL配置 - 使用SITE_CONFIG中的baseUrl
export const APP_URL = process.env.NEXT_PUBLIC_APP_URL || SITE_CONFIG.baseUrl;

// 服务端专用的应用URL配置（用于修复localhost问题）
export const SERVER_APP_URL = typeof window === 'undefined'
  ? (process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || SITE_CONFIG.baseUrl)
  : APP_URL;

// API基础URL - 使用SITE_CONFIG中的apiBaseUrl
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || SITE_CONFIG.apiBaseUrl;

// 视频通话配置
export const VIDEO_CALL_CONFIG = {
  ORG_CODE: process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || 'XUNMENGdorg',
  LOGIN_NAME: process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || 'XUNMENG001',
  APP_ID: process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || '',
  AES_KEY: process.env.NEXT_PUBLIC_VIDEO_CALL_AES_KEY || '',
  AES_IV: process.env.NEXT_PUBLIC_VIDEO_CALL_AES_IV || '',
};

// 环境配置
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
export const IS_TEST = process.env.NODE_ENV === 'test';

// 应用版本
export const APP_VERSION = process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0';

// 默认分页配置
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE_SIZES = [10, 20, 50, 100];

// 默认日期格式
export const DEFAULT_DATE_FORMAT = 'YYYY-MM-DD';
export const DEFAULT_TIME_FORMAT = 'HH:mm:ss';
export const DEFAULT_DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

// 默认语言
export const DEFAULT_LOCALE = 'zh-CN';

// 默认主题
export const DEFAULT_THEME = 'light';

// 默认超时时间（毫秒）
export const DEFAULT_TIMEOUT = 30000;

// 默认重试次数
export const DEFAULT_RETRY_COUNT = 3;

// 默认重试延迟（毫秒）
export const DEFAULT_RETRY_DELAY = 1000;

// 默认缓存时间（秒）
export const DEFAULT_CACHE_TIME = 60 * 5; // 5分钟

// 默认会话超时时间（分钟）
export const DEFAULT_SESSION_TIMEOUT = 30;

// 默认令牌刷新间隔（毫秒）
export const DEFAULT_TOKEN_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟

// 默认最大上传文件大小（字节）
export const MAX_UPLOAD_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// 默认允许的文件类型
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv',
];

// 导出默认配置对象
export default {
  APP_URL,
  API_BASE_URL,
  VIDEO_CALL_CONFIG,
  IS_DEVELOPMENT,
  IS_PRODUCTION,
  IS_TEST,
  APP_VERSION,
  DEFAULT_PAGE_SIZE,
  DEFAULT_PAGE_SIZES,
  DEFAULT_DATE_FORMAT,
  DEFAULT_TIME_FORMAT,
  DEFAULT_DATETIME_FORMAT,
  DEFAULT_LOCALE,
  DEFAULT_THEME,
  DEFAULT_TIMEOUT,
  DEFAULT_RETRY_COUNT,
  DEFAULT_RETRY_DELAY,
  DEFAULT_CACHE_TIME,
  DEFAULT_SESSION_TIMEOUT,
  DEFAULT_TOKEN_REFRESH_INTERVAL,
  MAX_UPLOAD_FILE_SIZE,
  ALLOWED_FILE_TYPES,
};
