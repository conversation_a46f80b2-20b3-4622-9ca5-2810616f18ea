import { prisma } from "@/lib/prisma"

/**
 * Prisma适配器
 * 用于处理不同版本的Prisma客户端命名差异
 * 
 * Prisma 5.x: 使用下划线命名 (system_settings)
 * Prisma 6.x: 使用驼峰命名 (systemSettings)
 */

// 检测Prisma版本和可用的模型名称
function detectSystemSettingsModel() {
  // 检查是否有systemSettings模型（Prisma 6.x）
  if ('systemSettings' in prisma && typeof (prisma as any).systemSettings === 'object') {
    return 'systemSettings'
  }
  
  // 检查是否有system_settings模型（Prisma 5.x）
  if ('system_settings' in prisma && typeof (prisma as any).system_settings === 'object') {
    return 'system_settings'
  }
  
  // 默认尝试systemSettings
  return 'systemSettings'
}

// 获取正确的模型名称
const SYSTEM_SETTINGS_MODEL = detectSystemSettingsModel()

/**
 * 系统设置适配器
 * 提供统一的API来访问系统设置，无论使用哪个版本的Prisma
 */
export const systemSettingsAdapter = {
  /**
   * 查找第一个系统设置记录
   */
  async findFirst(args?: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.findFirst(args)
  },

  /**
   * 创建系统设置记录
   */
  async create(args: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.create(args)
  },

  /**
   * 更新系统设置记录
   */
  async update(args: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.update(args)
  },

  /**
   * 创建或更新系统设置记录
   */
  async upsert(args: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.upsert(args)
  },

  /**
   * 删除系统设置记录
   */
  async delete(args: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.delete(args)
  },

  /**
   * 查找多个系统设置记录
   */
  async findMany(args?: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.findMany(args)
  },

  /**
   * 计数系统设置记录
   */
  async count(args?: any) {
    const model = (prisma as any)[SYSTEM_SETTINGS_MODEL]
    if (!model) {
      throw new Error(`SystemSettings model not found. Tried: ${SYSTEM_SETTINGS_MODEL}`)
    }
    return await model.count(args)
  },

  /**
   * 获取当前使用的模型名称（用于调试）
   */
  getModelName() {
    return SYSTEM_SETTINGS_MODEL
  }
}

// 导出默认适配器
export default systemSettingsAdapter
