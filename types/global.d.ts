
// 全局类型声明
declare global {
  interface Window {
    process?: any;
  }
  
  var process: any;
}

// 模块声明
declare module 'crypto-js' {
  const CryptoJS: any;
  export default CryptoJS;
}

declare module '@nestjs/common' {
  export const Injectable: any;
  export const Controller: any;
  export const Get: any;
  export const Post: any;
  export const Body: any;
  export const Param: any;
  export const Query: any;
  export const HttpStatus: any;
  export const NotFoundException: any;
  export const ConflictException: any;
  export const UnauthorizedException: any;
  export const ForbiddenException: any;
  export const CanActivate: any;
  export const ExecutionContext: any;
}

declare module '@nestjs/swagger' {
  export const ApiOperation: any;
  export const ApiResponse: any;
  export const ApiQuery: any;
  export const ApiTags: any;
  export const ApiProperty: any;
  export const ApiPropertyOptional: any;
}

declare module '@nestjs/typeorm' {
  export const InjectRepository: any;
}

declare module '@nestjs/passport' {
  export const AuthGuard: any;
  export const PassportStrategy: any;
}

declare module '@nestjs/core' {
  export const Reflector: any;
}

declare module '@nestjs/config' {
  export const ConfigService: any;
}

declare module 'passport-jwt' {
  export const ExtractJwt: any;
  export const Strategy: any;
}

declare module 'class-validator' {
  export const IsString: any;
  export const IsEmail: any;
  export const IsEnum: any;
  export const IsOptional: any;
  export const IsArray: any;
  export const MinLength: any;
  export const MaxLength: any;
  export const Matches: any;
  export const IsInt: any;
  export const Min: any;
  export const Max: any;
  export const IsDateString: any;
  export const IsIn: any;
}

declare module 'typeorm' {
  export const Repository: any;
  export const Like: any;
  export const FindOptionsWhere: any;
  export const Entity: any;
  export const PrimaryGeneratedColumn: any;
  export const Column: any;
  export const CreateDateColumn: any;
  export const UpdateDateColumn: any;
  export const BeforeInsert: any;
  export const BeforeUpdate: any;
  export const ManyToOne: any;
  export const OneToMany: any;
  export const JoinColumn: any;
}

declare module 'rxjs' {
  export const Observable: any;
}

declare module 'bcrypt' {
  const bcrypt: any;
  export = bcrypt;
}

export {};
