/** @type {import('next').NextConfig} */

// DevBox 环境的 Next.js 配置
// 用于修复请求地址问题

const fs = require('fs');
const path = require('path');

// 复制 manifest.devbox.json 到 manifest.json
const manifestDevboxPath = path.join(__dirname, 'public/manifest.devbox.json');
const manifestPath = path.join(__dirname, 'public/manifest.json');

if (fs.existsSync(manifestDevboxPath)) {
  try {
    const manifestContent = fs.readFileSync(manifestDevboxPath, 'utf8');
    fs.writeFileSync(manifestPath, manifestContent);
    console.log('已将 DevBox 专用 manifest 文件复制到 manifest.json');
  } catch (error) {
    console.error('复制 manifest 文件失败:', error);
  }
}

const nextConfig = {
  // 忽略 TypeScript 错误进行构建
  typescript: {
    ignoreBuildErrors: true,
  },

  // 忽略 ESLint 错误进行构建
  eslint: {
    ignoreDuringBuilds: true,
  },

  // 生产环境配置
  productionBrowserSourceMaps: false,
  compress: true,
  poweredByHeader: false,
  generateEtags: true,
  reactStrictMode: true,

  // 环境变量配置
  env: {
    // 添加客户端可用的环境变量
    APP_ENV: 'production',
    API_BASE_URL: '',
    NEXT_PUBLIC_API_BASE_URL: 'https://services.vcrm.vip:8000',
    // DevBox 特定配置
    NEXT_PUBLIC_APP_URL: 'https://ooaclkofmixc.sealoshzh.site',
    // 服务端请求基础 URL（用于修复 localhost 问题）
    NEXTAUTH_URL: 'https://ooaclkofmixc.sealoshzh.site',
    // 性能优化相关环境变量
    NEXT_OPTIMIZE_FONTS: 'true',
    NEXT_OPTIMIZE_IMAGES: 'true',
    NEXT_OPTIMIZE_CSS: 'true',
  },

  // 跨域资源共享配置
  async rewrites() {
    return [
      {
        source: '/api/proxy/:path*',
        destination: '/api/proxy/:path*',
      },
      {
        source: '/api/auth/:path*',
        destination: '/api/auth/:path*',
      },
      // 修复服务端渲染时的 localhost 请求
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ]
  },

  // 安全头配置
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code',
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
        ],
      },
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
          },
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production'
              ? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data: https:; connect-src 'self' https://services.vcrm.vip:8000 https://ooaclkofmixc.sealoshzh.site https: wss:; frame-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self';"
              : "",
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
    ]
  },

  // 重定向配置
  async redirects() {
    return [
      // 确保 localhost 请求重定向到正确的域名
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'localhost:3000',
          },
        ],
        destination: 'https://ooaclkofmixc.sealoshzh.site/:path*',
        permanent: true,
      },
    ]
  },
}

module.exports = nextConfig
