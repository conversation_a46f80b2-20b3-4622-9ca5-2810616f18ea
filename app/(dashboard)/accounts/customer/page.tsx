"use client"

import logger from '@/lib/utils/logger';

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { CustomerTable } from "@/app/components/customer/customer-table"
import { CustomerFilters } from "@/app/components/customer/customer-filters"
import { AddUserDialog } from "@/app/components/add-user-dialog"
import { UserRechargeDialog } from "@/app/components/user-recharge-dialog"
import { UserCreditDialog } from "@/app/components/user-credit-dialog"
import { UserDisableDialog } from "@/app/components/user-disable-dialog"
import { UserEnableDialog } from "@/app/components/user-enable-dialog"
import { RejectDialog } from "@/app/components/reject-dialog"
import { VerificationViewDialog } from "@/app/components/verification-view-dialog"
import { toast } from "@/components/ui/use-toast"
import { CustomerType } from "@/app/types/customer"
import { ErrorWrapper } from "@/components/error-wrapper"
import { useAsyncError } from "@/hooks/use-async-error"

// 导出包装后的组件
export default function CustomerPage() {
  return (
    <ErrorWrapper id="customer-page">
      <CustomerPageContent />
    </ErrorWrapper>
  )
}

// 原始组件内容
function CustomerPageContent() {
  const router = useRouter()
  const { isLoading, handleAsync } = useAsyncError()
  const [customers, setCustomers] = useState<CustomerType[]>([])
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerType[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [accountTypeFilter, setAccountTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [verificationFilter, setVerificationFilter] = useState("all")
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isRechargeDialogOpen, setIsRechargeDialogOpen] = useState(false)
  const [isCreditDialogOpen, setIsCreditDialogOpen] = useState(false)
  const [isDisableDialogOpen, setIsDisableDialogOpen] = useState(false)
  const [isEnableDialogOpen, setIsEnableDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [isVerificationDialogOpen, setIsVerificationDialogOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerType | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // 加载客户数据
  const fetchCustomers = async () => {
    setLoading(true)
    await handleAsync(async () => {
      const { fetchWithCorrectUrl } = await import('@/lib/utils/url');
      const response = await fetchWithCorrectUrl('/api/customers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('获取客户列表失败')
      }

      const data = await response.json()
      if (data.success && data.data) {
        logger.debug('获取到的客户数据:', data.data.slice(0, 2).map((c: any) => ({
          id: c.id,
          name: c.name,
          rate: c.rate,
          businessType: c.businessType
        })))
        setCustomers(data.data)
      } else {
        setCustomers([])
        if (data.message) {
          throw new Error(data.message)
        }
      }
    }, {
      context: { action: '加载客户列表' }
    })
    setLoading(false)
  }

  // 首次加载数据
  useEffect(() => {
    fetchCustomers()
  }, [])

  // 过滤客户数据
  useEffect(() => {
    let filtered = [...customers]

    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        customer =>
          customer.name?.toLowerCase().includes(term) ||
          customer.username?.toLowerCase().includes(term) ||
          customer.email?.toLowerCase().includes(term) ||
          customer.phone?.includes(term)
      )
    }

    // 按账户类型过滤
    if (accountTypeFilter !== "all") {
      filtered = filtered.filter(customer => customer.accountType === accountTypeFilter)
    }

    // 按状态过滤
    if (statusFilter !== "all") {
      filtered = filtered.filter(customer => customer.status === statusFilter)
    }

    // 按认证状态过滤
    if (verificationFilter !== "all") {
      filtered = filtered.filter(customer => {
        if (verificationFilter === "pending") {
          return customer.personalVerification === "pending" || customer.enterpriseVerification === "pending"
        } else if (verificationFilter === "approved") {
          return customer.personalVerification === "approved" || customer.enterpriseVerification === "approved"
        } else if (verificationFilter === "rejected") {
          return customer.personalVerification === "rejected" || customer.enterpriseVerification === "rejected"
        } else if (verificationFilter === "none") {
          return (!customer.personalVerification && !customer.enterpriseVerification) ||
                 (customer.personalVerification === "none" && customer.enterpriseVerification === "none")
        }
        return true
      })
    }

    setFilteredCustomers(filtered)
    setTotalPages(Math.ceil(filtered.length / 10))
    setCurrentPage(1)
  }, [customers, searchTerm, accountTypeFilter, statusFilter, verificationFilter])

  // 处理查看认证资料
  const handleViewVerification = (id: string) => {
    logger.log('开始查看用户认证资料:', id);

    // 查找用户信息
    const customer = customers.find(c => c.id === id);
    logger.log('查找用户结果:', customer ? '成功' : '失败');

    if (!customer) {
      logger.error('找不到用户信息:', id);
      toast({
        title: "操作失败",
        description: "找不到用户信息",
        variant: "destructive"
      });
      return;
    }

    logger.debug('用户信息:', {
      id: customer.id,
      name: customer.name || customer.username,
      personalVerification: customer.personalVerification,
      enterpriseVerification: customer.enterpriseVerification
    });

    // 设置选中的客户并打开认证资料对话框
    setSelectedCustomer(customer);
    setIsVerificationDialogOpen(true);
    logger.log('已打开认证资料对话框');
  }

  // 处理审批
  const handleApprove = async (id: string) => {
    try {
      // 使用正确的API路径，确保发送邮件和通知
      const response = await fetch(`/api/customers/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('审批失败')
      }

      const data = await response.json()
      if (data.success) {
        toast({
          title: "审批成功",
          description: "已成功审批通过该用户，系统已发送通知邮件",
          variant: "success"
        })

        // 更新本地状态
        setCustomers(prev =>
          prev.map(customer =>
            customer.id === id
              ? { ...customer, status: "active" }
              : customer
          )
        )
      } else {
        throw new Error(data.message || '审批失败')
      }
    } catch (error) {
      logger.error('审批失败:', error)
      toast({
        title: "操作失败",
        description: error instanceof Error ? error.message : "审批失败，请重试",
        variant: "destructive"
      })
    }
  }

  // 处理拒绝
  const handleReject = (id: string) => {
    // 查找用户信息
    const customer = customers.find(c => c.id === id);
    if (!customer) {
      toast({
        title: "操作失败",
        description: "找不到用户信息",
        variant: "destructive"
      });
      return;
    }

    // 设置选中的客户并打开拒绝对话框
    setSelectedCustomer(customer);
    setIsRejectDialogOpen(true);
  }

  // 确认拒绝并提交原因
  const handleConfirmReject = async (reason: string) => {
    if (!selectedCustomer) return;

    try {
      // 使用正确的API路径，确保发送邮件和通知
      // 先尝试使用原来的API路径
      const response = await fetch(`/api/customers/${selectedCustomer.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include',
        body: JSON.stringify({ reason })
      })

      if (!response.ok) {
        throw new Error('拒绝操作失败')
      }

      const data = await response.json()
      if (data.success) {
        toast({
          title: "已拒绝",
          description: "已拒绝该用户的申请，系统已发送通知邮件",
          variant: "success"
        })

        // 更新本地状态
        setCustomers(prev =>
          prev.map(customer =>
            customer.id === selectedCustomer.id
              ? { ...customer, status: "inactive" }
              : customer
          )
        )

        // 关闭对话框
        setIsRejectDialogOpen(false);
      } else {
        throw new Error(data.message || '拒绝操作失败')
      }
    } catch (error) {
      console.error('拒绝操作失败:', error)
      toast({
        title: "操作失败",
        description: error instanceof Error ? error.message : "拒绝操作失败，请重试",
        variant: "destructive"
      })
    }
  }

  // 处理停用用户
  const handleDisableUser = (customer: CustomerType) => {
    setSelectedCustomer(customer)
    setIsDisableDialogOpen(true)
  }

  // 处理禁用用户成功
  const handleDisableSuccess = (userId: string, reason: string) => {
    // 更新本地状态
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === userId ? { ...customer, status: "inactive" } : customer
      )
    )
  }

  // 处理启用用户
  const handleEnableUser = (customer: CustomerType) => {
    setSelectedCustomer(customer)
    setIsEnableDialogOpen(true)
  }

  // 处理启用用户成功
  const handleEnableSuccess = (userId: string) => {
    // 更新本地状态
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === userId ? { ...customer, status: "active" } : customer
      )
    )
  }

  // 处理充值
  const handleRecharge = (customer: CustomerType) => {
    setSelectedCustomer(customer)
    setIsRechargeDialogOpen(true)
  }

  // 处理授信
  const handleCredit = (customer: CustomerType) => {
    setSelectedCustomer(customer)
    setIsCreditDialogOpen(true)
  }

  // 处理充值成功
  const handleRechargeSuccess = (newBalance: number) => {
    if (!selectedCustomer) return

    console.log('客户列表页面充值成功，更新余额:', {
      customerId: selectedCustomer.id,
      oldBalance: selectedCustomer.balance,
      newBalance
    })

    // 更新本地状态
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === selectedCustomer.id
          ? { ...customer, balance: newBalance }
          : customer
      )
    )

    // 刷新客户列表
    setTimeout(() => {
      fetchCustomers()
    }, 500)

    setIsRechargeDialogOpen(false)
    // 不再显示重复的成功提示，因为对话框中已经显示了正确的余额
  }

  // 处理授信成功
  const handleCreditSuccess = (newCreditLimit: number) => {
    if (!selectedCustomer) return

    console.log('客户列表页面授信成功，更新额度:', {
      customerId: selectedCustomer.id,
      oldCreditLimit: selectedCustomer.creditLimit,
      newCreditLimit
    })

    // 更新本地状态
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === selectedCustomer.id
          ? { ...customer, creditLimit: newCreditLimit }
          : customer
      )
    )

    // 刷新客户列表
    setTimeout(() => {
      fetchCustomers()
    }, 500)

    setIsCreditDialogOpen(false)
    // 不再显示重复的成功提示，因为对话框中已经显示了正确的授信额度
  }

  // 处理导出
  const handleExport = async (format: string) => {
    try {
      toast({
        title: "导出开始",
        description: `正在导出客户数据为 ${format.toUpperCase()} 格式`,
      })

      const response = await fetch(`/api/customers/export?format=${format}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('导出失败')
      }

      // 处理文件下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `客户列表_${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "导出成功",
        description: `客户数据已成功导出为 ${format.toUpperCase()} 格式`,
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出客户数据失败，请重试",
        variant: "destructive"
      })
    }
  }

  // 处理添加用户
  const handleAddUser = async (user: any) => {
    // 如果用户数据中已经有id，说明是从对话框返回的已创建成功的用户
    if (user.id) {
      // 直接添加到列表中
      setCustomers(prev => [user, ...prev])
      setIsAddUserDialogOpen(false)

      toast({
        title: "用户创建成功",
        description: `已成功创建用户 ${user.name || user.username}`,
        variant: "success",
        className: "bg-green-500 text-white border-green-600"
      })
      return
    }

    await handleAsync(async () => {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include',
        body: JSON.stringify({
          // 不再使用name字段，在后端会自动使用username作为昵称
          username: user.username,
          email: user.email,
          phone: user.phone,
          password: user.password, // 添加密码字段
          accountType: user.accountType,
          creditLimit: parseFloat(user.creditLimit) || 0
        })
      })

      if (!response.ok) {
        throw new Error('创建用户失败')
      }

      const data = await response.json()

      if (data.success && data.data) {
        // 添加新用户到列表
        setCustomers(prev => [data.data, ...prev])
        setIsAddUserDialogOpen(false)

        toast({
          title: "用户创建成功",
          description: `已成功创建用户 ${data.data.name || data.data.username}`,
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })
      } else {
        throw new Error(data.message || '创建用户失败')
      }
    }, {
      context: { action: '创建新用户', userData: { username: user.username, email: user.email } }
    })
  }

  // 重置筛选器
  const resetFilters = () => {
    setSearchTerm("")
    setAccountTypeFilter("all")
    setStatusFilter("all")
    setVerificationFilter("all")
  }

  return (
    <DashboardShell>
      <CustomerFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        accountTypeFilter={accountTypeFilter}
        setAccountTypeFilter={setAccountTypeFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        verificationFilter={verificationFilter}
        setVerificationFilter={setVerificationFilter}
        onExport={handleExport}
        onAddUser={() => setIsAddUserDialogOpen(true)}
        resetFilters={resetFilters}
      />

      <CustomerTable
        customers={filteredCustomers.slice((currentPage - 1) * 10, currentPage * 10)}
        loading={loading}
        onApprove={handleViewVerification}
        onReject={handleViewVerification}
        onDisableUser={handleDisableUser}
        onEnableUser={handleEnableUser}
        onRecharge={handleRecharge}
        onCredit={handleCredit}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />

      <AddUserDialog
        open={isAddUserDialogOpen}
        onOpenChange={setIsAddUserDialogOpen}
        onUserAdded={handleAddUser}
      />

      {selectedCustomer && (
        <>
          <UserRechargeDialog
            open={isRechargeDialogOpen}
            onOpenChange={setIsRechargeDialogOpen}
            userId={selectedCustomer.id}
            userName={selectedCustomer.name || selectedCustomer.username || ""}
            currentBalance={selectedCustomer.balance || 0}
            onSuccess={handleRechargeSuccess}
          />

          <UserCreditDialog
            open={isCreditDialogOpen}
            onOpenChange={setIsCreditDialogOpen}
            userId={selectedCustomer.id}
            userName={selectedCustomer.name || selectedCustomer.username || ""}
            currentCreditLimit={selectedCustomer.creditLimit || 0}
            onSuccess={handleCreditSuccess}
          />

          <UserDisableDialog
            open={isDisableDialogOpen}
            onOpenChange={setIsDisableDialogOpen}
            userId={selectedCustomer.id}
            userName={selectedCustomer.name || selectedCustomer.username || ""}
            onSuccess={handleDisableSuccess}
          />

          <UserEnableDialog
            open={isEnableDialogOpen}
            onOpenChange={setIsEnableDialogOpen}
            userId={selectedCustomer.id}
            userName={selectedCustomer.name || selectedCustomer.username || ""}
            onSuccess={handleEnableSuccess}
          />

          <RejectDialog
            isOpen={isRejectDialogOpen}
            onClose={() => setIsRejectDialogOpen(false)}
            onConfirm={handleConfirmReject}
            accountName={selectedCustomer.name || selectedCustomer.username || ""}
          />

          <VerificationViewDialog
            open={isVerificationDialogOpen}
            onOpenChange={setIsVerificationDialogOpen}
            userId={selectedCustomer.id}
            userName={selectedCustomer.name || selectedCustomer.username || ""}
            onApprove={handleApprove}
            onReject={handleReject}
          />
        </>
      )}
    </DashboardShell>
  )
}