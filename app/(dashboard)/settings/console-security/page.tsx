"use client"

/**
 * 控制台安全设置页面
 * 用于测试和配置控制台安全功能
 */

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import logger from "@/lib/utils/logger"
import { applyConsoleSecurity } from "@/lib/console-security"

/**
 * 控制台安全设置页面组件
 */
export default function ConsoleSecurityPage() {
  // 控制台配置状态
  const [consoleStatus, setConsoleStatus] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("settings")

  // 加载控制台状态
  useEffect(() => {
    if (typeof logger.getConsoleStatus === 'function') {
      setConsoleStatus(logger.getConsoleStatus())
    }
  }, [])

  // 测试日志函数
  const testLog = () => {
    console.log("普通日志测试", { user: { name: "admin", email: "<EMAIL>" } })
  }

  const testWarn = () => {
    console.warn("警告日志测试", { sensitive: true, token: "secret-token-123" })
  }

  const testError = () => {
    console.error("错误日志测试", new Error("测试错误"))
  }

  const testSensitive = () => {
    console.log("敏感信息测试", {
      password: "123456",
      email: "<EMAIL>",
      creditCard: "1234-5678-9012-3456",
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    })
  }

  const testSensitiveUrl = () => {
    console.log("敏感URL测试", {
      apiUrl: "https://services.vcrm.vip:8000/api/users",
      endpoint: "services.vcrm.vip/tasks",
      config: {
        baseUrl: "https://services.vcrm.vip:8000",
        timeout: 5000
      }
    })
  }

  // 应用控制台安全设置
  const applySettings = () => {
    applyConsoleSecurity()
    if (typeof logger.getConsoleStatus === 'function') {
      setConsoleStatus(logger.getConsoleStatus())
    }
  }

  // 恢复原始控制台
  const restoreSettings = () => {
    // 简单的控制台恢复实现
    if (typeof window !== 'undefined') {
      // 这里可以添加恢复原始控制台的逻辑
      console.log('控制台恢复功能暂未实现')
    }
    if (typeof logger.getConsoleStatus === 'function') {
      setConsoleStatus(logger.getConsoleStatus())
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">控制台安全设置</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="settings">设置</TabsTrigger>
          <TabsTrigger value="test">测试</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>控制台安全配置</CardTitle>
              <CardDescription>
                配置浏览器控制台的安全选项，可以在生产环境中禁用控制台输出，防止敏感信息泄露
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="disabled" className="font-medium">完全禁用控制台</Label>
                    <p className="text-sm text-muted-foreground">
                      设置为开启将禁用所有控制台输出，包括console.log, console.error等
                    </p>
                  </div>
                  <Switch
                    id="disabled"
                    checked={consoleStatus?.disabled}
                    disabled={true}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="disabledInProduction" className="font-medium">在生产环境中禁用控制台</Label>
                    <p className="text-sm text-muted-foreground">
                      设置为开启将仅在生产环境(NODE_ENV=production)中禁用console输出
                    </p>
                  </div>
                  <Switch
                    id="disabledInProduction"
                    checked={consoleStatus?.disabledInProduction}
                    disabled={true}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="minLevel" className="font-medium">允许的最低日志级别</Label>
                    <p className="text-sm text-muted-foreground">
                      设置允许输出的最低日志级别
                    </p>
                  </div>
                  <Select disabled={true} value={consoleStatus?.minLevel?.toString()}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="选择日志级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">禁用所有</SelectItem>
                      <SelectItem value="1">只允许错误</SelectItem>
                      <SelectItem value="2">允许错误和警告</SelectItem>
                      <SelectItem value="3">允许错误、警告和信息</SelectItem>
                      <SelectItem value="4">允许所有</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="hideSensitiveUrls" className="font-medium">隐藏敏感URL信息</Label>
                    <p className="text-sm text-muted-foreground">
                      设置为开启将在控制台输出中隐藏敏感URL信息，如API地址等
                    </p>
                  </div>
                  <Switch
                    id="hideSensitiveUrls"
                    checked={consoleStatus?.hideSensitiveUrls}
                    disabled={true}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sensitiveUrls" className="font-medium">敏感URL列表</Label>
                    <p className="text-sm text-muted-foreground">
                      需要隐藏的敏感URL列表
                    </p>
                  </div>
                  <div className="text-sm text-muted-foreground max-w-[180px] text-right truncate">
                    {consoleStatus?.sensitiveUrls?.join(', ') || '无'}
                  </div>
                </div>
              </div>

              <div className="bg-muted p-4 rounded-md">
                <h3 className="font-medium mb-2">当前状态</h3>
                <pre className="text-xs overflow-auto p-2 bg-background rounded">
                  {consoleStatus ? JSON.stringify(consoleStatus, null, 2) : '加载中...'}
                </pre>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-md">
                <h3 className="font-medium mb-2 text-yellow-800 dark:text-yellow-300">配置说明</h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-400">
                  这些设置需要通过环境变量或配置文件修改。在 <code className="bg-yellow-100 dark:bg-yellow-900 px-1 rounded">lib/config.ts</code> 中可以找到相关配置。
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={restoreSettings}>
                恢复原始控制台
              </Button>
              <Button onClick={applySettings}>
                应用安全设置
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="test" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>控制台输出测试</CardTitle>
              <CardDescription>
                测试不同类型的控制台输出，查看安全设置的效果
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button onClick={testLog}>
                  测试普通日志
                </Button>
                <Button onClick={testWarn} variant="secondary">
                  测试警告日志
                </Button>
                <Button onClick={testError} variant="destructive">
                  测试错误日志
                </Button>
                <Button onClick={testSensitive} variant="outline">
                  测试敏感信息
                </Button>
                <Button onClick={testSensitiveUrl} variant="outline" className="col-span-2">
                  测试敏感URL (services.vcrm.vip)
                </Button>
              </div>

              <div className="bg-muted p-4 rounded-md">
                <h3 className="font-medium mb-2">使用说明</h3>
                <p className="text-sm">
                  点击上面的按钮测试不同类型的控制台输出。根据当前的安全设置，某些输出可能会被禁用或过滤。
                  请打开浏览器的开发者工具查看控制台输出结果。
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
