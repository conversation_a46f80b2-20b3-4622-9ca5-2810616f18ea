// 临时禁用控制台安全模块以调试登录问题
// import '../lib/init-console-security';
// import '../lib/init-security';


/**
 * 根布局组件
 * 为整个应用提供基础布局结构和全局配置
 *
 * 特性：
 * - 配置网站元数据（标题、描述等）
 * - 集成主题切换功能
 * - 提供认证上下文
 * - 支持 Toast 通知
 * - 使用 Inter 字体
 *
 * @example
 * ```tsx
 * // 页面会自动被包裹在这个布局中
 * <RootLayout>
 *   <YourPage />
 * </RootLayout>
 * ```
 */

import "@/app/globals.css"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
// import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"
import Script from "next/script"
import { RoutePrefetcher } from "@/components/route-prefetcher"
import { SidebarProvider } from "@/components/ui/sidebar"
import { getMetadata } from "@/lib/get-metadata"
import { SystemInitializer } from "@/app/components/system-initializer"
import { Providers } from "./providers"
import { getServerSession } from "next-auth/next"
import { options } from "./api/auth/[...nextauth]/options"
import { LayoutProcessPolyfill } from "./layout-process-polyfill"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  preload: true,
  adjustFontFallback: true,
  fallback: ['system-ui', 'arial'],
})

// 动态生成元数据
export async function generateMetadata(): Promise<Metadata> {
  // 获取动态元数据
  const dynamicMetadata = await getMetadata()

  // 添加静态元数据
  const otherMetadata: Record<string, string> = {
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "format-detection": "telephone=no",
    "mobile-web-app-capable": "yes",
  };

  // 合并动态元数据
  if (dynamicMetadata.other) {
    Object.assign(otherMetadata, dynamicMetadata.other);
  }

  return {
    ...dynamicMetadata,
    generator: "v0.dev",
    authors: [{ name: "Your Company" }],
    manifest: "/manifest.json",
    other: otherMetadata
  }
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" }
  ],
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: "cover",
}

interface RootLayoutProps {
  /** 子组件，将被包裹在布局中 */
  children: React.ReactNode
}

export default async function RootLayout({ children }: RootLayoutProps) {
  // 获取服务器端会话
  const session = await getServerSession(options);

  return (
    <html
      lang="zh-CN"
      suppressHydrationWarning
    >
      <head>
        {/* 临时禁用控制台安全脚本以调试登录问题 */}

        <link
          rel="preload"
          href="/dashboard"
          as="document"
        />
        <link
          rel="preload"
          href="/tasks"
          as="document"
        />
        <link
          rel="preconnect"
          href={process.env.NEXT_PUBLIC_API_URL}
          crossOrigin="anonymous"
        />
        <link
          rel="apple-touch-icon"
          href="/icons/icon-192x192.png"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="外呼系统" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <Script
          id="init-env"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // 初始化环境变量
              window.ENV = {
                NEXT_PUBLIC_API_BASE_URL: "${process.env.NEXT_PUBLIC_API_BASE_URL || '/api'}",
                NEXT_PUBLIC_VIDEO_CALL_ORG_CODE: "${process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || 'XUNMENGdorg'}",
                NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME: "${process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || 'XUNMENG001'}",
                NEXT_PUBLIC_VIDEO_CALL_APP_ID: "${process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || ''}",
                NODE_ENV: "${process.env.NODE_ENV || 'production'}"
              };
            `
          }}
        />
        <Script
          id="register-sw"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  // 检查是否为开发环境
                  const isDev = window.location.hostname === 'localhost' ||
                               window.location.hostname === '127.0.0.1' ||
                               window.location.hostname.includes('.local');

                  // 注册Service Worker
                  navigator.serviceWorker.register('/sw.js').then(
                    function(registration) {
                      // 只在开发环境中记录成功
                      if (isDev) {
                        console.log('ServiceWorker registration successful');
                      }
                    },
                    function(err) {
                      // 只在开发环境中记录错误
                      if (isDev && err) {
                        console.error('ServiceWorker registration failed');
                        console.error('Error details:', err);
                      }

                      // 在生产环境中静默失败，不输出任何日志
                    }
                  ).catch(function(err) {
                    // 捕获并抑制任何错误，防止它们被记录到控制台
                    if (isDev && err) {
                      console.error('ServiceWorker registration error caught:', err);
                    }
                  });
                });
              }
            `,
          }}
        />

        {/* 临时禁用激进的控制台安全脚本以调试登录问题 */}
        {/*
        <Script
          id="security-enhancements"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // 在页面加载最早阶段完全禁用控制台
              (function() {
                try {
                  const isDev = window.location.hostname === 'localhost' ||
                               window.location.hostname === '127.0.0.1' ||
                               window.location.hostname.includes('.local');

                  if (!isDev) {
                    // 完全禁用控制台 - 最激进的方法
                    const noop = function() {};

                    // 创建一个空的控制台对象
                    const emptyConsole = {
                      assert: noop,
                      clear: noop,
                      count: noop,
                      countReset: noop,
                      debug: noop,
                      dir: noop,
                      dirxml: noop,
                      error: noop,
                      exception: noop,
                      group: noop,
                      groupCollapsed: noop,
                      groupEnd: noop,
                      info: noop,
                      log: noop,
                      profile: noop,
                      profileEnd: noop,
                      table: noop,
                      time: noop,
                      timeEnd: noop,
                      timeLog: noop,
                      timeStamp: noop,
                      trace: noop,
                      warn: noop,
                      memory: {},
                      Console: function() { return emptyConsole; }
                    };

                    // 使用Object.defineProperty完全替换控制台对象
                    Object.defineProperty(window, 'console', {
                      configurable: false,
                      writable: false,
                      value: emptyConsole
                    });

                    // 拦截所有错误事件
                    window.addEventListener('error', function(event) {
                      event.preventDefault();
                      event.stopPropagation();
                      return true;
                    }, true);

                    // 拦截所有未处理的Promise拒绝
                    window.addEventListener('unhandledrejection', function(event) {
                      event.preventDefault();
                      event.stopPropagation();
                      return true;
                    }, true);

                    // 定期检查并重新应用控制台禁用
                    setInterval(function() {
                      Object.defineProperty(window, 'console', {
                        configurable: false,
                        writable: false,
                        value: emptyConsole
                      });
                    }, 100);
                  }
                } catch (e) {
                  // 忽略初始化错误
                }
              })();
            `,
          }}
        />
        */}
      </head>
      <body className={`${inter.className} overflow-x-hidden`}>
        <Providers session={session}>
          <SidebarProvider>
            <TooltipProvider>
              <LayoutProcessPolyfill />
              <RoutePrefetcher />
              <SystemInitializer />
              {children}
              <Toaster />
            </TooltipProvider>
          </SidebarProvider>
        </Providers>
      </body>
    </html>
  )
}

import './globals.css'