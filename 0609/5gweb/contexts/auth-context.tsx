"use client"

/**
 * 认证上下文模块
 * 提供全局的用户认证状态管理和认证相关方法
 * 
 * 功能：
 * - 用户登录状态管理
 * - 自动登录检查
 * - Token 管理
 * - 用户信息缓存
 * - 登录/登出操作
 * 
 * @example
 * ```tsx
 * // 在应用根组件中提供认证上下文
 * function App() {
 *   return (
 *     <AuthProvider>
 *       <YourApp />
 *     </AuthProvider>
 *   )
 * }
 * 
 * // 在组件中使用认证上下文
 * function YourComponent() {
 *   const { user, login, logout } = useAuth()
 *   
 *   if (!user) {
 *     return <LoginForm onSubmit={login} />
 *   }
 *   
 *   return (
 *     <div>
 *       <h1>Welcome, {user.name}!</h1>
 *       <button onClick={logout}>Logout</button>
 *     </div>
 *   )
 * }
 * ```
 */

import { createContext, useContext, useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { login as apiLogin, getCurrentUser, logout as apiLogout } from "@/lib/api/services"
import type { ApiResponse, LoginResponse, UserInfo } from "@/lib/api/interfaces"
import { toast } from "@/components/ui/use-toast"
import type { User } from "@/hooks/use-auth"

/**
 * 认证上下文类型接口
 */
export interface AuthContextType {
  /** 当前登录用户信息，未登录时为 null */
  user: User | null
  /** 是否正在进行认证相关操作 */
  loading: boolean
  /** 用户是否已认证 */
  isAuthenticated: boolean
  /** 错误信息 */
  error: string | null
  /** 
   * 用户登录方法
   * @param identifier - 用户名或邮箱
   * @param password - 密码
   * @param remember - 是否记住登录状态
   */
  login: (identifier: string, password: string, remember?: boolean) => Promise<void>
  /** 
   * 用户登出方法
   * 清除用户信息和 token，并跳转到登录页
   */
  logout: () => Promise<void>
}

export const AuthContext = createContext<AuthContextType | null>(null)

/**
 * 认证上下文提供者组件
 * 管理用户认证状态并提供认证相关方法
 * 
 * @param props - 组件属性
 * @param props.children - 子组件
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { data: session, status } = useSession()

  /**
   * 检查用户是否已登录
   */
  useEffect(() => {
    const checkAuth = async () => {
      if (status === "loading") return
      
      try {
        setLoading(true)
        setError(null)

        // 首先检查 NextAuth 会话
        if (session?.user) {
          console.log("使用 NextAuth 会话数据")
          setUser({
            success: true,
            data: session.user as UserInfo
          })
          return
        }

        // 如果没有 NextAuth 会话，尝试获取自定义认证状态
        console.log("尝试获取自定义认证状态...")
        const response = await getCurrentUser()
        
        if (response.success && response.data) {
          console.log("设置用户数据:", response.data)
          const userData: User = {
            success: true,
            data: response.data
          }
          setUser(userData)
        } else {
          console.log("未找到用户数据:", response.message)
          setUser(null)
        }
      } catch (error) {
        console.error("获取用户信息失败:", error)
        setUser(null)
        setError(error instanceof Error ? error.message : "获取用户信息失败")
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [session, status])

  /**
   * 用户登录方法
   */
  const login = async (identifier: string, password: string, remember?: boolean) => {
    setLoading(true)
    setError(null)
    try {
      console.log("尝试登录...")
      const response = await apiLogin({ identifier, password, remember })
      
      if (response.success && response.data?.user) {
        console.log("登录成功，设置用户数据:", response.data.user)
        const userData: User = {
          success: true,
          data: response.data.user
        }
        setUser(userData)
        
        toast({
          title: "登录成功",
          description: `欢迎回来，${response.data.user.name || response.data.user.username}`,
        })
        
        setTimeout(() => {
          router.push("/dashboard")
        }, 100)
      } else {
        console.log("登录失败:", response.message)
        setError(response.message || "用户名或密码错误")
        toast({
          title: "登录失败",
          description: response.message || "用户名或密码错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("登录失败:", error)
      const message = error instanceof Error ? error.message : "登录失败，请稍后重试"
      setError(message)
      toast({
        title: "登录失败",
        description: message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 用户登出方法
   */
  const logout = async () => {
    setLoading(true)
    try {
      await apiLogout()
      setUser(null)
      router.push("/login")
    } catch (error) {
      console.error("登出失败:", error)
      const message = error instanceof Error ? error.message : "登出失败，请稍后重试"
      toast({
        title: "登出失败",
        description: message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isAuthenticated: !!user,
        error,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

/**
 * 认证上下文 Hook
 * 在组件中使用认证上下文
 * 
 * @throws {Error} 如果在 AuthProvider 外部使用
 * @returns {AuthContextType} 认证上下文
 */
export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}