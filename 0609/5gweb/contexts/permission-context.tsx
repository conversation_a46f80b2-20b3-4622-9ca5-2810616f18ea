"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { hasRole, hasPermissions, hasResourcePermission } from "@/lib/abac/utils"

/**
 * 权限上下文值
 */
interface PermissionContextValue {
  user: any
  loading: boolean
  checkRole: (requiredRole: string) => boolean
  checkPermissions: (requiredPermissions: string[]) => boolean
  checkResourcePermission: (resource: string, action: string) => boolean
  checkPathPermission: (path: string) => Promise<boolean>
}

/**
 * 权限上下文
 */
const PermissionContext = createContext<PermissionContextValue | null>(null)

/**
 * 权限上下文提供者属性
 */
interface PermissionProviderProps {
  children: ReactNode
}

/**
 * 权限上下文提供者
 */
export function PermissionProvider({ children }: PermissionProviderProps) {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 从localStorage获取用户信息
    const userStr = localStorage.getItem("user")
    if (userStr) {
      setUser(JSON.parse(userStr))
    }
    setLoading(false)
  }, [])

  /**
   * 检查用户是否有指定角色
   */
  const checkRole = (requiredRole: string): boolean => {
    if (!user) return false
    return hasRole(user, requiredRole)
  }

  /**
   * 检查用户是否有指定权限
   */
  const checkPermissions = (requiredPermissions: string[]): boolean => {
    if (!user) return false
    return hasPermissions(user, requiredPermissions)
  }

  /**
   * 检查用户是否有指定资源和操作的权限
   */
  const checkResourcePermission = (
    resource: string,
    action: string
  ): boolean => {
    if (!user) return false
    return hasResourcePermission(user, resource, action)
  }

  /**
   * 检查用户是否有权限访问指定路径
   */
  const checkPathPermission = async (path: string): Promise<boolean> => {
    try {
      const response = await fetch("/api/policies/check", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          resource: path,
          action: "view",
        }),
      })

      const result = await response.json()
      return result.allowed
    } catch (error) {
      console.error("权限检查失败:", error)
      return false
    }
  }

  const value = {
    user,
    loading,
    checkRole,
    checkPermissions,
    checkResourcePermission,
    checkPathPermission,
  }

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  )
}

/**
 * 使用权限上下文
 */
export function usePermissionContext() {
  const context = useContext(PermissionContext)
  if (!context) {
    throw new Error("usePermissionContext must be used within a PermissionProvider")
  }
  return context
} 