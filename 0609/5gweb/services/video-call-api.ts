import { VIDEO_CALL_CONFIG, API_ENDPOINTS } from "@/config/api-config"
import { aesUtils } from "@/utils/crypto-utils"

export class VideoCallApiService {
  private baseUrl: string
  private orgCode: string
  private loginName: string
  private appId: string

  constructor() {
    this.baseUrl = VIDEO_CALL_CONFIG.BASE_URL
    this.orgCode = VIDEO_CALL_CONFIG.ORG_CODE
    this.loginName = VIDEO_CALL_CONFIG.LOGIN_NAME
    this.appId = VIDEO_CALL_CONFIG.APP_ID
  }

  /**
   * 获取通用请求头
   * @returns 请求头对象
   */
  private getHeaders() {
    return {
      "Content-Type": "application/json; charset=utf-8",
      "access-token": aesUtils.generateSign(),
    }
  }

  /**
   * 获取通用请求参数
   * @returns 基础请求参数对象
   */
  private getBaseParams() {
    return {
      orgCode: this.orgCode,
      loginName: this.loginName,
    }
  }

  /**
   * 创建外呼任务
   * @param params 任务参数
   * @returns 任务创建结果
   */
  async createCallTask(params: any) {
    const url = `${this.baseUrl}${API_ENDPOINTS.CALL_TASK.CREATE}`
    const response = await fetch(url, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify({
        ...this.getBaseParams(),
        ...params,
      }),
    })

    return await response.json()
  }

  /**
   * 获取任务列表
   * @param params 查询参数
   * @returns 任务列表
   */
  async getTaskList(params: any) {
    const queryParams = new URLSearchParams({
      ...this.getBaseParams(),
      ...params,
    }).toString()

    const url = `${this.baseUrl}${API_ENDPOINTS.CALL_TASK.LIST}?${queryParams}`
    const response = await fetch(url, {
      method: "GET",
      headers: this.getHeaders(),
    })

    return await response.json()
  }

  /**
   * 获取任务详情
   * @param taskId 任务ID
   * @returns 任务详情
   */
  async getTaskDetail(taskId: string) {
    const queryParams = new URLSearchParams(this.getBaseParams()).toString()
    const url = `${this.baseUrl}${API_ENDPOINTS.CALL_TASK.DETAIL}/${taskId}?${queryParams}`

    const response = await fetch(url, {
      method: "GET",
      headers: this.getHeaders(),
    })

    return await response.json()
  }

  /**
   * 创建客户名单批次
   * @param params 批次参数
   * @returns 批次创建结果
   */
  async createCustomerBatch(params: any) {
    const url = `${this.baseUrl}${API_ENDPOINTS.CUSTOMER.CREATE_BATCH}`
    const response = await fetch(url, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify({
        ...this.getBaseParams(),
        appId: this.appId,
        ...params,
      }),
    })

    return await response.json()
  }

  /**
   * 导入客户名单
   * @param batchId 批次ID
   * @param userInfoList 客户名单
   * @returns 导入结果
   */
  async importCustomers(batchId: string, userInfoList: any[]) {
    const url = `${this.baseUrl}${API_ENDPOINTS.CUSTOMER.IMPORT}`
    const response = await fetch(url, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify({
        ...this.getBaseParams(),
        appId: this.appId,
        batchId,
        userInfoList,
      }),
    })

    return await response.json()
  }

  /**
   * 获取黑名单分组列表
   * @returns 黑名单分组列表
   */
  async getBlacklistGroups() {
    const url = `${this.baseUrl}${API_ENDPOINTS.BLACKLIST.GROUPS}`
    const response = await fetch(url, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify({
        ...this.getBaseParams(),
        appId: this.appId,
      }),
    })

    return await response.json()
  }

  /**
   * 获取Bot列表
   * @returns Bot列表
   */
  async getBotList() {
    const url = `${this.baseUrl}${API_ENDPOINTS.BOT.LIST}`
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify({
        ...this.getBaseParams(),
        sign: aesUtils.generateSign(),
      }),
    })

    return await response.json()
  }

  /**
   * 解密回调数据
   * @param encryptedData 加密的回调数据
   * @returns 解密后的数据
   */
  decryptCallbackData(encryptedData: string) {
    try {
      const decryptedData = aesUtils.cbcDecrypt(encryptedData)
      return JSON.parse(decryptedData)
    } catch (error) {
      console.error("解密回调数据失败:", error)
      return null
    }
  }
}

// 导出单例实例，方便直接使用
export const videoCallApi = new VideoCallApiService()

