/**
 * JWT认证守卫
 * 
 * @description
 * 用于保护需要认证的路由
 * 验证请求中的JWT令牌是否有效
 * 
 * @module JwtAuthGuard
 */

import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

/**
 * 公开路由装饰器的元数据键
 */
export const IS_PUBLIC_KEY = 'isPublic';

/**
 * 公开路由装饰器
 * 用于标记不需要认证的路由
 */
export const Public = () => {
  return (target: any, key?: string | symbol, descriptor?: any) => {
    const reflector = new Reflector();
    reflector.set(IS_PUBLIC_KEY, true, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  /**
   * 构造函数
   * @param reflector 反射器，用于获取路由元数据
   */
  constructor(private reflector: Reflector) {
    super();
  }

  /**
   * 判断请求是否可以通过认证
   * @param context 执行上下文
   * @returns 是否允许请求通过
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // 检查路由是否被标记为公开
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果是公开路由，直接放行
    if (isPublic) {
      return true;
    }

    // 调用父类的canActivate方法进行JWT验证
    return super.canActivate(context);
  }

  /**
   * 处理认证失败的情况
   * @param err 错误信息
   */
  handleRequest(err: any, user: any, info: any) {
    // 如果有错误或用户不存在，抛出未授权异常
    if (err || !user) {
      throw err || new UnauthorizedException('认证失败');
    }
    return user;
  }
} 