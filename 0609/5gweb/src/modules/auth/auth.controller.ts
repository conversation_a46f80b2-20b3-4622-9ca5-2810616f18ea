/**
 * 认证控制器
 * 
 * @description
 * 提供认证相关的API端点，包括：
 * 1. 用户登录
 * 2. 用户注册
 * 3. 修改密码
 * 4. 获取当前用户信息
 * 
 * @module AuthController
 */

import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Public } from './guards/jwt-auth.guard';
import {
  LoginDto,
  RegisterDto,
  ChangePasswordDto,
  AuthResponseDto,
} from './dto/auth.dto';
import { User } from './entities/user.entity';

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  /**
   * 构造函数
   * @param authService 认证服务
   */
  constructor(private readonly authService: AuthService) {}

  /**
   * 用户登录
   * @param loginDto 登录数据
   * @returns 认证响应数据
   */
  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录', description: '使用用户名和密码登录系统' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '登录成功',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '用户名或密码错误',
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.login(loginDto);
  }

  /**
   * 用户注册
   * @param registerDto 注册数据
   * @returns 注册成功的用户信息
   */
  @Public()
  @Post('register')
  @ApiOperation({ summary: '用户注册', description: '创建新用户账户' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '注册成功',
    type: User,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '用户名或邮箱已存在',
  })
  async register(@Body() registerDto: RegisterDto): Promise<User> {
    return this.authService.register(registerDto);
  }

  /**
   * 修改密码
   * @param req 请求对象
   * @param changePasswordDto 修改密码数据
   * @returns 更新后的用户信息
   */
  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改密码', description: '修改当前用户的密码' })
  @ApiBody({ type: ChangePasswordDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '密码修改成功',
    type: User,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '当前密码错误',
  })
  async changePassword(
    @Request() req,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<User> {
    return this.authService.changePassword(req.user.id, changePasswordDto);
  }

  /**
   * 获取当前用户信息
   * @param req 请求对象
   * @returns 当前用户信息
   */
  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息', description: '获取当前登录用户的详细信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: User,
  })
  getProfile(@Request() req): User {
    return req.user;
  }
} 