/**
 * 用户模块DTO
 * 
 * @description
 * 定义用户模块相关的数据传输对象，包括：
 * 1. 创建用户请求
 * 2. 更新用户请求
 * 3. 查询用户参数
 * 4. 用户响应数据
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsEnum, IsOptional, IsArray, MinLength, MaxLength, Matches, IsInt, Min, Max, IsDateString, IsIn } from 'class-validator';
import { UserRole, UserStatus } from '../../auth/entities/user.entity';

/**
 * 创建用户DTO
 */
export class CreateUserDto {
  @ApiProperty({ example: 'john_doe', description: '用户名' })
  @IsString()
  @MinLength(4)
  @MaxLength(20)
  @Matches(/^[a-zA-Z0-9_-]*$/, {
    message: '用户名只能包含字母、数字、下划线和连字符'
  })
  username: string;

  @ApiProperty({ example: 'Password123!', description: '密码' })
  @IsString()
  @MinLength(8)
  @MaxLength(32)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
    message: '密码必须包含大小写字母、数字和特殊字符'
  })
  password: string;

  @ApiProperty({ example: '<EMAIL>', description: '邮箱' })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ example: '13800138000', description: '手机号' })
  @IsOptional()
  @Matches(/^1[3-9]\d{9}$/, {
    message: '请输入有效的手机号'
  })
  phone?: string;

  @ApiPropertyOptional({ example: '张三', description: '真实姓名' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @ApiProperty({ enum: UserRole, example: UserRole.USER, description: '用户角色' })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiPropertyOptional({ type: [String], example: ['user:read', 'user:write'], description: '用户权限' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];
}

/**
 * 更新用户DTO
 */
export class UpdateUserDto {
  @ApiPropertyOptional({ example: '<EMAIL>', description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ example: '13800138000', description: '手机号' })
  @IsOptional()
  @Matches(/^1[3-9]\d{9}$/, {
    message: '请输入有效的手机号'
  })
  phone?: string;

  @ApiPropertyOptional({ example: '张三', description: '真实姓名' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @ApiPropertyOptional({ enum: UserRole, example: UserRole.USER, description: '用户角色' })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({ enum: UserStatus, example: UserStatus.ACTIVE, description: '用户状态' })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({ type: [String], example: ['user:read', 'user:write'], description: '用户权限' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];
}

/**
 * 用户查询DTO
 */
export class UserQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  pageSize?: number = 10;

  @ApiPropertyOptional({ description: '用户名' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '角色', enum: UserRole })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({ description: '状态', enum: UserStatus })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({ description: '创建时间范围-开始' })
  @IsOptional()
  @IsDateString()
  createdAtStart?: string;

  @ApiPropertyOptional({ description: '创建时间范围-结束' })
  @IsOptional()
  @IsDateString()
  createdAtEnd?: string;

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 用户响应DTO
 */
export class UserResponseDto {
  @ApiProperty({ example: '1', description: '用户ID' })
  id: string;

  @ApiProperty({ example: 'john_doe', description: '用户名' })
  username: string;

  @ApiProperty({ example: '<EMAIL>', description: '邮箱' })
  email: string;

  @ApiPropertyOptional({ example: '13800138000', description: '手机号' })
  phone?: string;

  @ApiPropertyOptional({ example: '张三', description: '真实姓名' })
  realName?: string;

  @ApiProperty({ enum: UserRole, example: UserRole.USER, description: '用户角色' })
  role: UserRole;

  @ApiProperty({ enum: UserStatus, example: UserStatus.ACTIVE, description: '用户状态' })
  status: UserStatus;

  @ApiProperty({ type: [String], example: ['user:read', 'user:write'], description: '用户权限' })
  permissions: string[];

  @ApiPropertyOptional({ example: '2024-01-01T00:00:00Z', description: '最后登录时间' })
  lastLoginAt?: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z', description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z', description: '更新时间' })
  updatedAt: Date;
} 