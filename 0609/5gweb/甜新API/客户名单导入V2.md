Title: 客户名单导入V2--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797821

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
    

##### 简要描述

客户名单导入V2

##### 请求URL

*   `/openapi/customer/v2`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | post |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 账号名 |
| appId | 是 | string | 应用id，固定值，由对接人员提供 |
| batchId | 是 | string | 导入名单批次(任务)id, 每个导入批次最多可以放20万名单 |
| userInfoList | 是 | array | 名单列表：单次最多支持300条数据 |
| userInfoList.phone | 是 | string | 明文手机号，与 encryptedPhone 字段必须要有其中一个 |
| userInfoList.encryptedPhone | 是 | string | aes算法加密后的手机号，与 phone 字段必须要有其中一个 |
| userInfoList.name | 是 | string | 客户姓名 |
| userInfoList.otherInfo | 否 | string | 客户备注 |
| userInfoList.otherInfo.title | 否 | string | 客户备注字段名 |
| userInfoList.otherInfo.value | 否 | string | 客户备注字段值 |

##### 请求示例

复制```
{
    "name": "客户名单v2001",
    "appId": "d9e81d829bd8fed2",
    "orgCode": "lx20240428",
    "loginName": "CS001",
    "userInfoList": [
        {
            "name": "闪闪",
            "phone": "19000000001",
            "otherInfo": [
                {
                    "title": "amount",
                    "value": "800"
                }
            ]
        }
    ]
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "errInfo": "",
    "result": {
        "batchId": "66bb103c1d715a83ca17aea5"
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败 |
| errInfo | string | 失败描述 |
| result | object | 结果 |
| result.batchId | string | 导入批次id |
