Title: 获取任务统计分析数据--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797901

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

通过该接口可以获取到外呼不同维度的统计分析数据：

1.  外呼通话数据统计分析  
    包括【概览】【接通方式】【5G视频接通-通话数据分析/短信发送分析】【5G语音接通-通话数据分析/短信发送分析】。
2.  挂断分布统计分析
3.  每秒挂断分布统计分析
4.  每5秒挂断分布统计分析
5.  接通趋势统计分析
6.  用户意图分类统计分析
7.  意图触发详情统计分析

##### 请求URL

*   `/openapi/task/panels`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | post |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 否，但与下方sign必须2选1 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| sign | 否，但与上方access-token必须2选1 | string | 见开发前须知中的token/签名生成方式 |
| loginName | 是 | string | 发送任务的账号名 |
| mediaDeliverIds | 是 | array | 外呼任务Id数组 |

##### 请求示例

复制```
{
    "mediaDeliverIds": [
        "66398d96a59cac4a28996a8b",
        "66398d96a59cac4a28996a8e",
        "66398d96a59cac4a28996a91",
        "66398d96a59cac4a28996a94",
        "66398d96a59cac4a28996a97",
        "66398d96a59cac4a28996a9a",
        "66398d96a59cac4a28996a9d",
        "66398d96a59cac4a28996a9f",
        "66398d96a59cac4a28996aa1"
    ],
    "orgCode": "jgtest001",
    "loginName": "test001"
}
```

##### 返回示例

复制```
{
   "errCode": 0,
   "errMsg": null,
   "result": {
      "callonStatisticData": {
         "callStatisticData": {
            "callTotalCount": 40085,
            "callonCount": 18315,
            "callonNotCount": 21770,
            "videoCallonCount": 16126,
            "audioCallonCount": 2189,
            "totalHoldingTime": 444793.58,
            "videoTotalHoldingTime": 404591.02,
            "audioTotalHoldingTime": 40202.56,
            "s5HangupCount": 216,
            "videos5HangupCount": 78,
            "audios5HangupCount": 138,
            "badResponseCount": 75,
            "videoBadResponseCount": 15,
            "audioBadResponseCount": 60,
            "playFinishCount": 16177,
            "videoPlayFinishCount": 15825,
            "audioPlayFinishCount": 352,
            "hangupPosition1Count": 510,
            "videoHangupPosition1Count": 178,
            "audioHangupPosition1Count": 332,
            "userSpeakTimes": 144,
            "userSpeakAvgTimes": 0.0079,
            "speakCount": 88,
            "videoSpeakCount": 3,
            "audioSpeakCount": 85,
            "interactPosition": 0,
            "videoInteractPosition": 0,
            "audioInteractPosition": 0,
            "deliverNumber": 51029,
            "callonRate": 0.4569,
            "videoCallonRate": 0.8805,
            "audioCallonRate": 0.1195,
            "avgTotalHoldingTime": 24.29,
            "avgVideoHoldingTime": 25.09,
            "avgAudioHoldingTime": 18.37,
            "s5HangupRate": 0.0118,
            "videos5HangupRate": 0.0048,
            "audios5HangupRate": 0.063,
            "badResponseRate": 0.0041,
            "videoBadResponseRate": 0.0009,
            "audioBadResponseRate": 0.0274,
            "videoCallonRateForAll": 0.4023,
            "audioCallonRateForAll": 0.0546,
            "playFinishRate": 0.8833,
            "videoPlayFinishRate": 0.9813,
            "audioPlayFinishRate": 0.1608,
            "hangupPosition1Rate": 0.0278,
            "videoHangupPosition1Rate": 0.011,
            "audioHangupPosition1Rate": 0.1517,
            "interactionCount": 88,
            "videoInteractionCount": 3,
            "audioInteractionCount": 85,
            "interactionRate": 0.0048,
            "videoInteractionRate": 0.0002,
            "audioInteractionRate": 0.0388,
            "userSpeakTotalTimes": 144
         },
         "messageStatisticData": {
            "smsSendCount": 1635,
            "smsSendSuccessCount": 1635,
            "smsReceivedCount": 1584,
            "videoSmsSendCount": 0,
            "videoSmsSendSuccessCount": 0,
            "videoSmsReceivedCount": 0,
            "textSmsSendCount": 1635,
            "textSmsSendSuccessCount": 1635,
            "textSmsReceivedCount": 1584,
            "smsUrlAccessCount": 0,
            "videoSmsUrlAccessCount": 0,
            "textSmsUrlAccesCount": 0,
            "vjsmsSendCount": 1,
            "vjsmsSendSuccessCount": 1,
            "vjsmsReceivedCount": 0,
            "vjvideoSmsSendCount": 0,
            "vjvideoSmsSendSuccessCount": 0,
            "vjvideoSmsReceivedCount": 0,
            "vjtextSmsSendCount": 1,
            "vjtextSmsSendSuccessCount": 1,
            "vjtextSmsReceivedCount": 0,
            "vjsmsUrlAccessCount": 0,
            "vjvideoSmsUrlAccessCount": 0,
            "vjtextSmsUrlAccesCount": 0,
            "ajsmsSendCount": 1634,
            "ajsmsSendSuccessCount": 1634,
            "ajsmsReceivedCount": 1584,
            "ajvideoSmsSendCount": 0,
            "ajvideoSmsSendSuccessCount": 0,
            "ajvideoSmsReceivedCount": 0,
            "ajtextSmsSendCount": 1634,
            "ajtextSmsSendSuccessCount": 1634,
            "ajtextSmsReceivedCount": 1584,
            "ajsmsUrlAccessCount": 0,
            "ajvideoSmsUrlAccessCount": 0,
            "ajtextSmsUrlAccesCount": 0,
            "id": 0,
            "smsSendRate": 0.0408,
            "smsSendSuccessRate": 1,
            "smsReceivedRate": 0.9688,
            "videoSmsSendRate": 0,
            "videoSmsSendSuccessRate": 0,
            "videoSmsReceivedRate": 0,
            "textSmsSendRate": 0.0408,
            "textSmsSendSuccessRate": 0.0893,
            "textSmsReceivedRate": 0.9688,
            "smsUrlAccessRate": 0,
            "videoSmsUrlAccessRate": 0,
            "textSmsUrlAccesRate": 0,
            "vjsmsSendRate": 0.0001,
            "vjsmsSendSuccessRate": 0.0001,
            "vjsmsReceivedRate": 0,
            "vjvideoSmsSendRate": 0,
            "vjvideoSmsSendSuccessRate": 0,
            "vjvideoSmsReceivedRate": 0,
            "vjtextSmsSendRate": 0.0001,
            "vjtextSmsSendSuccessRate": 0.0001,
            "vjtextSmsReceivedRate": 0,
            "vjsmsUrlAccessRate": 0,
            "vjvideoSmsUrlAccessRate": 0,
            "vjtextSmsUrlAccesRate": 0,
            "ajsmsSendRate": 0.7465,
            "ajsmsSendSuccessRate": 0.7465,
            "ajsmsReceivedRate": 0.9694,
            "ajvideoSmsSendRate": 0,
            "ajvideoSmsSendSuccessRate": 0,
            "ajvideoSmsReceivedRate": 0,
            "ajtextSmsSendRate": 0.7465,
            "ajtextSmsSendSuccessRate": 0.7465,
            "ajtextSmsReceivedRate": 0.9694,
            "ajsmsUrlAccessRate": 0,
            "ajvideoSmsUrlAccessRate": 0,
            "ajtextSmsUrlAccesRate": 0
         },
         "targetDoc": {
            "__v": 0,
            "smsUrlAccessRate": -0.5,
            "smsReceivedRate": 0.4688,
            "smsSendSuccessRate": 0.5,
            "interactPosition": -3,
            "interactionRate": -0.4952,
            "hangupPosition1Rate": -0.4722,
            "s5HangupRate": -0.4882,
            "playFinishRate": 0.3833,
            "avgTotalHoldingTime": -35.71,
            "audioCallonRate": -0.3805,
            "videoCallonRate": 0.3805,
            "callonRate": -0.0431,
            "avgVideoHoldingTime": -34.91,
            "avgAudioHoldingTime": -41.63,
            "videoPlayFinishRate": 0.4813,
            "audioPlayFinishRate": -0.3392,
            "videos5HangupRate": -0.4952,
            "audios5HangupRate": -0.437,
            "videoHangupPosition1Rate": -0.489,
            "audioHangupPosition1Rate": -0.3483,
            "videoInteractionRate": -0.4998,
            "audioInteractionRate": -0.4612,
            "videoInteractPosition": -3,
            "audioInteractPosition": -3,
            "vjsmsSendSuccessRate": -0.4999,
            "vjsmsReceivedRate": -0.5,
            "vjsmsUrlAccessRate": -0.5,
            "ajsmsSendSuccessRate": 0.2465,
            "ajsmsReceivedRate": 0.4694,
            "ajsmsUrlAccessRate": -0.5
         },
         "targetSettings": {
            "_id": "665936da1c2c5a61de12f4d0",
            "__v": 0,
            "smsUrlAccessRate": 0.5,
            "smsReceivedRate": 0.5,
            "smsSendSuccessRate": 0.5,
            "interactPosition": 3,
            "interactionRate": 0.5,
            "hangupPosition1Rate": 0.5,
            "s5HangupRate": 0.5,
            "playFinishRate": 0.5,
            "avgTotalHoldingTime": 60,
            "audioCallonRate": 0.5,
            "videoCallonRate": 0.5,
            "callonRate": 0.5,
            "sceneName": "通用业务",
            "avgVideoHoldingTime": 60,
            "avgAudioHoldingTime": 60,
            "videoPlayFinishRate": 0.5,
            "audioPlayFinishRate": 0.5,
            "videos5HangupRate": 0.5,
            "audios5HangupRate": 0.5,
            "videoHangupPosition1Rate": 0.5,
            "audioHangupPosition1Rate": 0.5,
            "videoInteractionRate": 0.5,
            "audioInteractionRate": 0.5,
            "videoInteractPosition": 3,
            "audioInteractPosition": 3,
            "vjsmsSendSuccessRate": 0.5,
            "vjsmsReceivedRate": 0.5,
            "vjsmsUrlAccessRate": 0.5,
            "ajsmsSendSuccessRate": 0.5,
            "ajsmsReceivedRate": 0.5,
            "ajsmsUrlAccessRate": 0.5
         }
      },
      "positionStatisticData": [
         {
            "position": 1,
            "videoCount": 178,
            "audioCount": 332,
            "totalCount": 510,
            "videoRate": 0.011,
            "audioRate": 0.1517,
            "totalRate": 0.0278
         },
         {
            "position": 2,
            "videoCount": 109,
            "audioCount": 121,
            "totalCount": 230,
            "videoRate": 0.0068,
            "audioRate": 0.0553,
            "totalRate": 0.0126
         },
         {
            "position": 3,
            "videoCount": 15829,
            "audioCount": 862,
            "totalCount": 16691,
            "videoRate": 0.9816,
            "audioRate": 0.3938,
            "totalRate": 0.9113
         },
         {
            "position": 4,
            "videoCount": 3,
            "audioCount": 76,
            "totalCount": 79,
            "videoRate": 0.0002,
            "audioRate": 0.0347,
            "totalRate": 0.0043
         },
         {
            "position": 5,
            "videoCount": 2,
            "audioCount": 554,
            "totalCount": 556,
            "videoRate": 0.0001,
            "audioRate": 0.2531,
            "totalRate": 0.0304
         },
         {
            "position": 6,
            "videoCount": 5,
            "audioCount": 57,
            "totalCount": 62,
            "videoRate": 0.0003,
            "audioRate": 0.026,
            "totalRate": 0.0034
         },
         {
            "position": 7,
            "videoCount": 0,
            "audioCount": 94,
            "totalCount": 94,
            "videoRate": 0,
            "audioRate": 0.0429,
            "totalRate": 0.0051
         },
         {
            "position": 8,
            "videoCount": 0,
            "audioCount": 16,
            "totalCount": 16,
            "videoRate": 0,
            "audioRate": 0.0073,
            "totalRate": 0.0009
         },
         {
            "position": 9,
            "videoCount": 0,
            "audioCount": 77,
            "totalCount": 77,
            "videoRate": 0,
            "audioRate": 0.0352,
            "totalRate": 0.0042
         }
      ],
      "hangup1SecondsData": [
         {
            "hangupSeconds": 1,
            "videoCount": 5,
            "audioCount": 11,
            "totalCount": 16,
            "videoRate": 0.0003,
            "audioRate": 0.005,
            "totalRate": 0.0009
         },
         {
            "hangupSeconds": 2,
            "videoCount": 8,
            "audioCount": 9,
            "totalCount": 17,
            "videoRate": 0.0005,
            "audioRate": 0.0041,
            "totalRate": 0.0009
         },
         {
            "hangupSeconds": 3,
            "videoCount": 21,
            "audioCount": 16,
            "totalCount": 37,
            "videoRate": 0.0013,
            "audioRate": 0.0073,
            "totalRate": 0.002
         },
         {
            "hangupSeconds": 4,
            "videoCount": 22,
            "audioCount": 15,
            "totalCount": 37,
            "videoRate": 0.0014,
            "audioRate": 0.0069,
            "totalRate": 0.002
         },
         {
            "hangupSeconds": 5,
            "videoCount": 22,
            "audioCount": 87,
            "totalCount": 109,
            "videoRate": 0.0014,
            "audioRate": 0.0397,
            "totalRate": 0.006
         },
         {
            "hangupSeconds": 6,
            "videoCount": 32,
            "audioCount": 145,
            "totalCount": 177,
            "videoRate": 0.002,
            "audioRate": 0.0662,
            "totalRate": 0.0097
         },
         {
            "hangupSeconds": 7,
            "videoCount": 20,
            "audioCount": 271,
            "totalCount": 291,
            "videoRate": 0.0012,
            "audioRate": 0.1238,
            "totalRate": 0.0159
         },
         {
            "hangupSeconds": 8,
            "videoCount": 22,
            "audioCount": 250,
            "totalCount": 272,
            "videoRate": 0.0014,
            "audioRate": 0.1142,
            "totalRate": 0.0149
         }
      ],
      "hangup5SecondsData": [
         {
            "hangupSeconds": 5,
            "videoCount": 78,
            "audioCount": 138,
            "totalCount": 216,
            "videoRate": 0.0048,
            "audioRate": 0.063,
            "totalRate": 0.0118
         },
         {
            "hangupSeconds": 10,
            "videoCount": 113,
            "audioCount": 963,
            "totalCount": 1076,
            "videoRate": 0.007,
            "audioRate": 0.4399,
            "totalRate": 0.0587
         },
         {
            "hangupSeconds": 15,
            "videoCount": 37,
            "audioCount": 252,
            "totalCount": 289,
            "videoRate": 0.0023,
            "audioRate": 0.1151,
            "totalRate": 0.0158
         }
      ],
      "callonTrendStatisticData": [
         {
            "hour": "14:00",
            "videoCallonCount": 4,
            "audioCallonCount": 120,
            "callonTotalCount": 124,
            "callonCount": 124,
            "callonNotCount": 909,
            "callTotalCount": 1033,
            "videoCallonRate": 0.0323,
            "audioCallonRate": 0.9677,
            "callonRate": 0.12
         },
         {
            "hour": "16:00",
            "videoCallonCount": 230,
            "audioCallonCount": 144,
            "callonTotalCount": 374,
            "callonCount": 374,
            "callonNotCount": 3595,
            "callTotalCount": 3969,
            "videoCallonRate": 0.615,
            "audioCallonRate": 0.385,
            "callonRate": 0.0942
         },
         {
            "hour": "17:00",
            "videoCallonCount": 69,
            "audioCallonCount": 798,
            "callonTotalCount": 867,
            "callonCount": 867,
            "callonNotCount": 2532,
            "callTotalCount": 3399,
            "videoCallonRate": 0.0796,
            "audioCallonRate": 0.9204,
            "callonRate": 0.2551
         },
         {
            "hour": "18:00",
            "videoCallonCount": 0,
            "audioCallonCount": 862,
            "callonTotalCount": 862,
            "callonCount": 862,
            "callonNotCount": 1749,
            "callTotalCount": 2611,
            "videoCallonRate": 0,
            "audioCallonRate": 1,
            "callonRate": 0.3301
         },
         {
            "hour": "21:00",
            "videoCallonCount": 13573,
            "audioCallonCount": 264,
            "callonTotalCount": 13837,
            "callonCount": 13837,
            "callonNotCount": 20581,
            "callTotalCount": 34418,
            "videoCallonRate": 0.9809,
            "audioCallonRate": 0.0191,
            "callonRate": 0.402
         },
         {
            "hour": "22:00",
            "videoCallonCount": 2250,
            "audioCallonCount": 1,
            "callonTotalCount": 2251,
            "callonCount": 2251,
            "callonNotCount": 3320,
            "callTotalCount": 5571,
            "videoCallonRate": 0.9996,
            "audioCallonRate": 0.0004,
            "callonRate": 0.4041
         }
      ],
      "intentStatisticData": [
         {
            "intentLevel": "A",
            "videoCount": 16126,
            "audioCount": 2071,
            "totalCount": 18197,
            "totalRate": 0.3568,
            "videoRate": 1,
            "audioRate": 0.9461
         },
         {
            "intentLevel": "B",
            "videoCount": 0,
            "audioCount": 6,
            "totalCount": 6,
            "totalRate": 0.0001,
            "videoRate": 0,
            "audioRate": 0.0027
         },
         {
            "intentLevel": "C",
            "videoCount": 0,
            "audioCount": 108,
            "totalCount": 108,
            "totalRate": 0.0021,
            "videoRate": 0,
            "audioRate": 0.0493
         },
         {
            "intentLevel": "D",
            "videoCount": 0,
            "audioCount": 1,
            "totalCount": 1,
            "totalRate": 0,
            "videoRate": 0,
            "audioRate": 0.0005
         },
         {
            "intentLevel": "F",
            "videoCount": 0,
            "audioCount": 3,
            "totalCount": 3,
            "totalRate": 0.0001,
            "videoRate": 0,
            "audioRate": 0.0014
         },
         {
            "intentLevel": "Z",
            "videoCount": 0,
            "audioCount": 0,
            "totalCount": 32686,
            "totalRate": 0.6409,
            "videoRate": 0,
            "audioRate": 0,
            "otherCount": 32686,
            "otherRate": 1
         }
      ],
      "intentTouchStatisticData": {
         "totalList": [
            {
               "intentTouchType": "ecommerce_em_normal_response",
               "count": 42,
               "rate": 0.2917,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_greeting",
               "count": 30,
               "rate": 0.2083,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_ask_who",
               "count": 19,
               "rate": 0.1319,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_voice_assistant",
               "count": 10,
               "rate": 0.0694,
               "intentTouchName": null
            },
            {
               "intentTouchType": "不可打断",
               "count": 9,
               "rate": 0.0625,
               "intentTouchName": null
            },
            {
               "intentTouchType": "nlu_fallback",
               "count": 5,
               "rate": 0.0347,
               "intentTouchName": "模型未理解"
            },
            {
               "intentTouchType": "credit_okay",
               "count": 4,
               "rate": 0.0278,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_bad_response",
               "count": 2,
               "rate": 0.0139,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_farewell",
               "count": 2,
               "rate": 0.0139,
               "intentTouchName": null
            },
            {
               "intentTouchType": "other",
               "intentTouchName": "其他",
               "count": 21,
               "rate": 0.1458
            }
         ],
         "videoList": [
            {
               "intentTouchType": "nlu_fallback",
               "count": 1,
               "rate": 0.2,
               "intentTouchName": "模型未理解"
            },
            {
               "intentTouchType": "normal_response",
               "count": 1,
               "rate": 0.2,
               "intentTouchName": "随口应答"
            },
            {
               "intentTouchType": "recruit_affirm",
               "count": 1,
               "rate": 0.2,
               "intentTouchName": null
            },
            {
               "intentTouchType": "recruit_ask_other_city",
               "count": 1,
               "rate": 0.2,
               "intentTouchName": null
            },
            {
               "intentTouchType": "不可打断",
               "count": 1,
               "rate": 0.2,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_ask_robot",
               "count": 0,
               "rate": 0,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_bad_response",
               "count": 0,
               "rate": 0,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_greeting",
               "count": 0,
               "rate": 0,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_how_operate",
               "count": 0,
               "rate": 0,
               "intentTouchName": null
            },
            {
               "intentTouchType": "other",
               "intentTouchName": "其他",
               "count": 0,
               "rate": 0
            }
         ],
         "audioList": [
            {
               "intentTouchType": "ecommerce_em_normal_response",
               "count": 42,
               "rate": 0.3022,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_greeting",
               "count": 30,
               "rate": 0.2158,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_ask_who",
               "count": 19,
               "rate": 0.1367,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_voice_assistant",
               "count": 10,
               "rate": 0.0719,
               "intentTouchName": null
            },
            {
               "intentTouchType": "不可打断",
               "count": 8,
               "rate": 0.0576,
               "intentTouchName": null
            },
            {
               "intentTouchType": "credit_okay",
               "count": 4,
               "rate": 0.0288,
               "intentTouchName": null
            },
            {
               "intentTouchType": "nlu_fallback",
               "count": 4,
               "rate": 0.0288,
               "intentTouchName": "模型未理解"
            },
            {
               "intentTouchType": "credit_bad_response",
               "count": 2,
               "rate": 0.0144,
               "intentTouchName": null
            },
            {
               "intentTouchType": "ecommerce_em_farewell",
               "count": 2,
               "rate": 0.0144,
               "intentTouchName": null
            },
            {
               "intentTouchType": "other",
               "intentTouchName": "其他",
               "count": 18,
               "rate": 0.1295
            }
         ]
      }
   }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败，具体参见下表 |
| result | object | 结果 |
| result.callonStatisticData | object | 外呼通话数据统计分析 |
| result.callonStatisticData.callStatisticData | object | 外呼通话概览统计分析 |
| result.callonStatisticData.callStatisticData.callTotalCount | number | 外呼总数 |
| result.callonStatisticData.callStatisticData.callonCount | number | 接通总数 |
| result.callonStatisticData.callStatisticData.callonNotCount | number | 未接通总数 |
| result.callonStatisticData.callStatisticData.videoCallonCount | number | 5G视频接通总数 |
| result.callonStatisticData.callStatisticData.audioCallonCount | number | 5G语音接通总数 |
| result.callonStatisticData.callStatisticData.totalHoldingTime | number | 通话总时长 |
| result.callonStatisticData.callStatisticData.videoTotalHoldingTime | number | 视频接通通话总时长 |
| result.callonStatisticData.callStatisticData.audioTotalHoldingTime | number | 语音接通通话总时长 |
| result.callonStatisticData.callStatisticData.s5HangupCount | number | 5秒挂断总数 |
| result.callonStatisticData.callStatisticData.videos5HangupCount | number | 视频接通5秒挂断总数 |
| result.callonStatisticData.callStatisticData.audios5HangupCount | number | 语音接通5秒挂断总数 |
| result.callonStatisticData.callStatisticData.badResponseCount | number | 辱骂总数 |
| result.callonStatisticData.callStatisticData.videoBadResponseCount | number | 视频接通辱骂总数 |
| result.callonStatisticData.callStatisticData.audioBadResponseCount | number | 语音接通辱骂总数 |
| result.callonStatisticData.callStatisticData.videoCallonRate | number | 视频接通率 |
| result.callonStatisticData.callStatisticData.audioCallonRate | number | 语音接通率 |
| result.callonStatisticData.callStatisticData.avgTotalHoldingTime | number | 平均通话时长 |
| result.callonStatisticData.callStatisticData.avgVideoHoldingTime | number | 视频接通平均通话时长 |
| result.callonStatisticData.callStatisticData.avgAudioHoldingTime | number | 语音接通平均通话时长 |
| result.callonStatisticData.callStatisticData.s5HangupRate | number | 5秒挂断率 |
| result.callonStatisticData.callStatisticData.videos5HangupRate | number | 视频接通5秒挂断率 |
| result.callonStatisticData.callStatisticData.audios5HangupRate | number | 语音接通5秒挂断率 |
| result.callonStatisticData.callStatisticData.badResponseRate | number | 辱骂比例 |
| result.callonStatisticData.callStatisticData.videoBadResponseRate | number | 视频接通辱骂比例 |
| result.callonStatisticData.callStatisticData.audioBadResponseRate | number | 语音接通辱骂比例 |
| result.callonStatisticData.callStatisticData.deliverNumber | number | 导入数量 |
| result.callonStatisticData.callStatisticData.callonRate | number | 接通率 |
| result.callonStatisticData.callStatisticData.playFinishCount | number | 完播数 |
| result.callonStatisticData.callStatisticData.videoPlayFinishCount | number | 视频接通完播数 |
| result.callonStatisticData.callStatisticData.audioPlayFinishCount | number | 语音接通完播数 |
| result.callonStatisticData.callStatisticData.playFinishRate | number | 完播率 |
| result.callonStatisticData.callStatisticData.videoPlayFinishRate | number | 视频接通完播率 |
| result.callonStatisticData.callStatisticData.audioPlayFinishRate | number | 语音接通完播率 |
| result.callonStatisticData.callStatisticData.hangupPosition1Rate | number | 开场节点挂断率 |
| result.callonStatisticData.callStatisticData.videoHangupPosition1Rate | number | 视频接通开场节点挂断率 |
| result.callonStatisticData.callStatisticData.audioHangupPosition1Rate | number | 语音接通开场节点挂断率 |
| result.callonStatisticData.callStatisticData.interactionRate | number | 互动率 |
| result.callonStatisticData.callStatisticData.videoInteractionRate | number | 视频接通互动率 |
| result.callonStatisticData.callStatisticData.audioInteractionRate | number | 语音接通互动率 |
| result.callonStatisticData.callStatisticData.interactPosition | number | 互动轮次 |
| result.callonStatisticData.callStatisticData.videoInteractPosition | number | 视频接通互动轮次 |
| result.callonStatisticData.callStatisticData.audioInteractPosition | number | 语音接通互动轮次 |
| result.callonStatisticData.callStatisticData.userSpeakTotalTimes | number | 用户发言总次数 |
| result.callonStatisticData.callStatisticData.userSpeakAvgTimes | number | 用户发言次数（平均用户发言次数） |
| result.callonStatisticData.messageStatisticData | object | 短信发送统计分析 |
| result.callonStatisticData.messageStatisticData.smsSendCount | number | 短信执行总数 |
| result.callonStatisticData.messageStatisticData.smsSendSuccessCount | number | 短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.smsReceivedCount | number | 短信接收数量 |
| result.callonStatisticData.messageStatisticData.videoSmsSendCount | number | 视频短信执行数量 |
| result.callonStatisticData.messageStatisticData.videoSmsSendSuccessCount | number | 视频短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.videoSmsReceivedCount | number | 视频短信接收数量 |
| result.callonStatisticData.messageStatisticData.textSmsSendCount | number | 文本短信执行数量 |
| result.callonStatisticData.messageStatisticData.textSmsSendSuccessCount | number | 文本短息发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.textSmsReceivedCount | number | 文本短信接收数量 |
| result.callonStatisticData.messageStatisticData.smsUrlAccessCount | number | 短信点击数量 |
| result.callonStatisticData.messageStatisticData.videoSmsUrlAccessCount | number | 视频短信点击数量 |
| result.callonStatisticData.messageStatisticData.textSmsUrlAccesCount | number | 文本短信点击数量 |
| result.callonStatisticData.messageStatisticData.smsSendRate | number | 短信执行率 |
| result.callonStatisticData.messageStatisticData.smsSendSuccessRate | number | 短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.smsReceivedRate | number | 短信接收率 |
| result.callonStatisticData.messageStatisticData.videoSmsSendRate | number | 视频短信执行率 |
| result.callonStatisticData.messageStatisticData.videoSmsSendSuccessRate | number | 视频短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.videoSmsReceivedRate | number | 视频短信接收率 |
| result.callonStatisticData.messageStatisticData.textSmsSendRate | number | 文本短信执行率 |
| result.callonStatisticData.messageStatisticData.textSmsSendSuccessRate | number | 文本短信发送率（发送成功率） |
| result.callonStatisticData.messageStatisticData.textSmsReceivedRate | number | 文本短信接收率 |
| result.callonStatisticData.messageStatisticData.smsUrlAccessRate | number | 短信点击率 |
| result.callonStatisticData.messageStatisticData.videoSmsUrlAccessRate | number | 视频短信点击率 |
| result.callonStatisticData.messageStatisticData.textSmsUrlAccesRate | number | 文本短信点击率 |
| result.callonStatisticData.messageStatisticData.vjsmsSendCount | number | 5G视频接通-短信执行总数 |
| result.callonStatisticData.messageStatisticData.vjsmsSendSuccessCount | number | 5G视频接通-短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.vjsmsReceivedCount | number | 5G视频接通-短信接收数量 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsSendCount | number | 5G视频接通-视频短信执行数量 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsSendSuccessCount | number | 5G视频接通-视频短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.vjvideoSmsReceivedCount | number | 5G视频接通-视频短信接收数量 |
| result.callonStatisticData.messageStatisticData.vjtextSmsSendCount | number | 5G视频接通-文本短信执行数量 |
| result.callonStatisticData.messageStatisticData.vjtextSmsSendSuccessCount | number | 5G视频接通-文本短息发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.vjtextSmsReceivedCount | number | 5G视频接通-文本短信接收数量 |
| result.callonStatisticData.messageStatisticData.vjsmsUrlAccessCount | number | 5G视频接通-短信点击数量 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsUrlAccessCount | number | 5G视频接通-视频短信点击数量 |
| result.callonStatisticData.messageStatisticData.vjtextSmsUrlAccesCount | number | 5G视频接通-文本短信点击数量 |
| result.callonStatisticData.messageStatisticData.vjsmsSendRate | number | 5G视频接通-短信执行率 |
| result.callonStatisticData.messageStatisticData.vjsmsSendSuccessRate | number | 5G视频接通-短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.vjsmsReceivedRate | number | 5G视频接通-短信接收率 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsSendRate | number | 5G视频接通-视频短信执行率 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsSendSuccessRate | number | 5G视频接通-视频短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.vjvideoSmsReceivedRate | number | 5G视频接通-视频短信接收率 |
| result.callonStatisticData.messageStatisticData.vjtextSmsSendRate | number | 5G视频接通-文本短信执行率 |
| result.callonStatisticData.messageStatisticData.vjtextSmsSendSuccessRate | number | 5G视频接通-文本短信发送率（发送成功率） |
| result.callonStatisticData.messageStatisticData.vjtextSmsReceivedRate | number | 5G视频接通-文本短信接收率 |
| result.callonStatisticData.messageStatisticData.vjsmsUrlAccessRate | number | 5G视频接通-短信点击率 |
| result.callonStatisticData.messageStatisticData.vjvideoSmsUrlAccessRate | number | 5G视频接通-视频短信点击率 |
| result.callonStatisticData.messageStatisticData.vjtextSmsUrlAccesRate | number | 5G视频接通-文本短信点击率 |
| result.callonStatisticData.messageStatisticData.ajsmsSendCount | number | 5G语音接通-短信执行总数 |
| result.callonStatisticData.messageStatisticData.ajsmsSendSuccessCount | number | 5G语音接通-短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.ajsmsReceivedCount | number | 5G语音接通-短信接收数量 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsSendCount | number | 5G语音接通-视频短信执行数量 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsSendSuccessCount | number | 5G语音接通-视频短信发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.ajvideoSmsReceivedCount | number | 5G语音接通-视频短信接收数量 |
| result.callonStatisticData.messageStatisticData.ajtextSmsSendCount | number | 5G语音接通-文本短信执行数量 |
| result.callonStatisticData.messageStatisticData.ajtextSmsSendSuccessCount | number | 5G语音接通-文本短息发送数量(发送成功数) |
| result.callonStatisticData.messageStatisticData.ajtextSmsReceivedCount | number | 5G语音接通-文本短信接收数量 |
| result.callonStatisticData.messageStatisticData.ajsmsUrlAccessCount | number | 5G语音接通-短信点击数量 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsUrlAccessCount | number | 5G语音接通-视频短信点击数量 |
| result.callonStatisticData.messageStatisticData.ajtextSmsUrlAccesCount | number | 5G语音接通-文本短信点击数量 |
| result.callonStatisticData.messageStatisticData.ajsmsSendRate | number | 5G语音接通-短信执行率 |
| result.callonStatisticData.messageStatisticData.ajsmsSendSuccessRate | number | 5G语音接通-短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.ajsmsReceivedRate | number | 5G语音接通-短信接收率 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsSendRate | number | 5G语音接通-视频短信执行率 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsSendSuccessRate | number | 5G语音接通-视频短信发送率(发送成功率) |
| result.callonStatisticData.messageStatisticData.ajvideoSmsReceivedRate | number | 5G语音接通-视频短信接收率 |
| result.callonStatisticData.messageStatisticData.ajtextSmsSendRate | number | 5G语音接通-文本短信执行率 |
| result.callonStatisticData.messageStatisticData.ajtextSmsSendSuccessRate | number | 5G语音接通-文本短信发送率（发送成功率） |
| result.callonStatisticData.messageStatisticData.ajtextSmsReceivedRate | number | 5G语音接通-文本短信接收率 |
| result.callonStatisticData.messageStatisticData.ajsmsUrlAccessRate | number | 5G语音接通-短信点击率 |
| result.callonStatisticData.messageStatisticData.ajvideoSmsUrlAccessRate | number | 5G语音接通-视频短信点击率 |
| result.callonStatisticData.messageStatisticData.ajtextSmsUrlAccesRate | number | 5G语音接通-文本短信点击率 |
| result.callonStatisticData.targetDoc | object | 外呼统计字段与场景指标设置差异值：负数-下降；正数-上升；targetDoc 对象中的字段名称和字段意义同上 callStatisticData，messageStatisticData 对象中的字段名称和字段意义 |
| result.callonStatisticData.targetDoc.sceneName | string | 场景名称 |
| result.callonStatisticData.targetSettings | object | 场景指标标准值设置详情，targetSettings 对象中的字段名称和字段意义同上 callStatisticData，messageStatisticData 对象中的字段名称和字段意义 |
| result.callonStatisticData.targetSettings.sceneName | string | 场景名称 |
| result.positionStatisticData | object | 挂断分布-轮次统计分析 |
| result.positionStatisticData.position | number | 轮次 |
| result.positionStatisticData.videoCount | number | 视频接通总数 |
| result.positionStatisticData.audioCount | number | 音频接通总数 |
| result.positionStatisticData.totalCount | number | 接通总数 |
| result.positionStatisticData.videoRate | number | 视频接通率 |
| result.positionStatisticData.audioRate | number | 音频接通率 |
| result.positionStatisticData.totalRate | number | 接通率 |
| result.hangup1SecondsData | object | 挂断分布-每1秒挂断统计分析 |
| result.hangup1SecondsData.hangupSeconds | number | 挂断所在秒数 |
| result.hangup1SecondsData.videoCount | number | 视频接通总数 |
| result.hangup1SecondsData.audioCount | number | 音频接通总数 |
| result.hangup1SecondsData.totalCount | number | 接通总数 |
| result.hangup1SecondsData.videoRate | number | 视频接通率 |
| result.hangup1SecondsData.audioRate | number | 音频接通率 |
| result.hangup1SecondsData.totalRate | number | 接通率 |
| result.hangup5SecondsData | object | 挂断分布-每5秒挂断统计分析 |
| result.hangup5SecondsData.hangupSeconds | number | 挂断所在秒数 |
| result.hangup5SecondsData.videoCount | number | 视频接通总数 |
| result.hangup5SecondsData.audioCount | number | 音频接通总数 |
| result.hangup5SecondsData.totalCount | number | 接通总数 |
| result.hangup5SecondsData.videoRate | number | 视频接通率 |
| result.hangup5SecondsData.audioRate | number | 音频接通率 |
| result.hangup5SecondsData.totalRate | number | 接通率 |
| result.callonTrendStatisticData | object | 接通趋势统计分析 |
| result.callonTrendStatisticData.hour | string | 时间 |
| result.callonTrendStatisticData.videoCallonCount | number | 视频接通数 |
| result.callonTrendStatisticData.audioCallonCount | number | 语音接通数 |
| result.callonTrendStatisticData.callonCount | number | 接通总数 |
| result.callonTrendStatisticData.callonTotalCount | number | 接通总数，（与 callonCount 相同，任取一个即可） |
| result.callonTrendStatisticData.callonNotCount | number | 未接通数 |
| result.callonTrendStatisticData.videoCallonRate | number | 视频接通占比 |
| result.callonTrendStatisticData.audioCallonRate | number | 语音接通占比 |
| result.callonTrendStatisticData.callonRate | number | 接通率 |
| result.callonTrendStatisticData.callTotalCount | number | 外呼总数 |
| result.intentStatisticData | object | 用户意图分类统计统计分析 |
| result.intentStatisticData.intentLevel | string | 意图分类 |
| result.intentStatisticData.videoCount | number | 视频接通数 |
| result.intentStatisticData.audioCount | number | 语音接通数 |
| result.intentStatisticData.totalCount | number | 接通总数 |
| result.intentStatisticData.videoRate | number | 视频接通占比 |
| result.intentStatisticData.audioRate | number | 语音接通占比 |
| result.intentStatisticData.totalRate | number | 接通率 |
| result.intentStatisticData.otherCount | number | 其他总数 |
| result.intentStatisticData.otherRate | number | 其他概率 |
| result.intentTouchStatisticData | object | 意图触发情况统计统计分析 |
| result.intentTouchStatisticData.totalList | array | 总体触发情况 |
| result.intentTouchStatisticData.totalList.intentTouchType | string | 意图触发类型 |
| result.intentTouchStatisticData.totalList.intentTouchName | string | 意图触发类型名称 |
| result.intentTouchStatisticData.totalList.count | number | 意图触发对话数量 |
| result.intentTouchStatisticData.totalList.rate | number | 意图触发对话数量占比 |
| result.intentTouchStatisticData.videoList | array | 5G视频接通-触发情况 |
| result.intentTouchStatisticData.videoList.intentTouchType | string | 意图触发类型 |
| result.intentTouchStatisticData.videoList.intentTouchName | string | 意图触发类型名称 |
| result.intentTouchStatisticData.videoList.count | number | 意图触发对话数量 |
| result.intentTouchStatisticData.videoList.rate | number | 意图触发对话数量占比 |
| result.intentTouchStatisticData.audioList | array | 5G语音接通-触发情况 |
| result.intentTouchStatisticData.audioList.intentTouchType | string | 意图触发类型 |
| result.intentTouchStatisticData.audioList.intentTouchName | string | 意图触发类型名称 |
| result.intentTouchStatisticData.audioList.count | number | 意图触发对话数量 |
| result.intentTouchStatisticData.audioList.rate | number | 意图触发对话数量占比 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 10 | 系统内部错误 |
