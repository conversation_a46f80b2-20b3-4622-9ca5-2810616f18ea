Title: 推送客户名单接口--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797744

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

通过接口传入客户名单，不进行外呼，后续由运营人员在平台进行处理后，再人工进行外呼  
也支持结果回调

##### 请求URL

*   `/cms/api/customer/external/create`

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

**\*注意：该接口支持批量传入名单，请勿一个手机号对应一个请求发送。**

**\*注意：该接口目前建议单次最多传输10万手机号的数据量。**

##### 参数

| 参数名 | 必选 | 类型 | 说明 |
| --- | --- | --- | --- |
| name | 是 | string | 名称 |
| userInfoList | 是 | array | 名单列表 |
| userInfoList.name | 否 | string | 姓名 |
| userInfoList.phone | 否 | string | 手机号 |
| userInfoList.encryptedPhone | 否 | string | 加密手机号（注意：手机号直接通过aes加密，加密demo见AES目录） |
| userInfoList.otherInfo | 否 | array | 备注信息 |
| userInfoList.otherInfo.title | 否 | string | 标题 |
| userInfoList.otherInfo.value | 否 | string | 内容 |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| sign | 是 | string | 见开发须知中的签名生成方式 |
| loginName | 是 | string | 账号名 |
| appId | 是 | string | 应用id，固定值，由对接人员提供 |

##### 请求示例

复制```
{
    "name": "客户导入测试1",
    "userInfoList": [
        {
            "name": "louis",
            "phone": "17612162706",
            "otherInfo": [
                {
                    "title": "aaa",
                    "value": "222"
                }
            ]
        },
        {
            "name": "louis2",
            "phone": "13585924653",
            "otherInfo": [
                {
                    "title": "aaa",
                    "value": "222"
                }
            ]
        }
    ],
    "orgCode": "spcd_org",
    "sign": "{{sign}}",
    "loginName": "ty-louis",
    "appId": "d9e81d829bd8fed2"
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "errInfo": "",
    "result": {
        "batchId": "656eba9403141aa747d5981c"
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败 |
| result | object | 结果 |
| result.batchId | string | 导入id |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| \-2 | 找不到此机构 |
| \-3 | 该机构下没有此账号 |
| 500 | 其它异常错误 |
