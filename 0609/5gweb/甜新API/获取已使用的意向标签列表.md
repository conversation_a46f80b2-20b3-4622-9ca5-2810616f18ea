Title: 获取已使用的意向标签列表--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797902

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

通过该接口可以获取到外呼内容Bot已经设置的意向标签规则列表。

##### 请求URL

*   `/openapi/videoBot/intent/settings`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | get |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 发送任务的账号名 |
| botId | 是 | string | 外呼内容Id（BotId） |

##### 请求示例

复制```
{
    "orgCode": "spcd_org",
    "sign": "8db1f84648c43e7d0e3f3927baf6108770f5aa7ff68c6c104de04916e44ae2e9",
    "loginName": "test",
    "botId": "66ecd652d83b51002025dbdc"
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "result": {
        "botId": "66ecd652d83b51002025dbdc",
        "name": "测试Bot",
        "details": [
            {
                "_id": "6720b3478294e83884ebd3d1",
                "sort": 1,
                "desc": "最高意向",
                "level": "A"
            },
            {
                "_id": "6720b3478294e83884ebd3d0",
                "sort": 2,
                "desc": "意向不是最高",
                "level": "B"
            },
            {
                "_id": "6720b3478294e83884ebd3cf",
                "sort": 3,
                "desc": "意向不是很高",
                "level": "F"
            },
            {
                "_id": "6720b3478294e83884ebd3ce",
                "sort": 4,
                "desc": "意向一般",
                "level": "G"
            },
            {
                "_id": "6720b3478294e83884ebd3cd",
                "sort": 5,
                "desc": "意向不高",
                "level": "H"
            },
            {
                "_id": "6720b3478294e83884ebd3cc",
                "sort": 6,
                "desc": "没有意向",
                "level": "K"
            }
        ]
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败，具体参见下表 |
| result | object | 结果 |
| result.botId | string | BotId |
| result.name | string | Bot 名称 |
| result.details | array | 意图标签规则列表 |
| result.details\[n\].sort | number | 意图标签排序 |
| result.details\[n\].desc | string | 意图标签描述 |
| result.details\[n\].level | string | 意图标签 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 10 | 系统内部错误 |
