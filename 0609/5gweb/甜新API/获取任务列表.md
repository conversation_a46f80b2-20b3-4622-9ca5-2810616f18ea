Title: 获取任务列表--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797899

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

获取任务列表

##### 请求URL

*   `/openapi/callout/taskList`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | get |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 发送任务的账号名 |
| startCreateAt | 是 | int | 创建时间开始时间的时间戳，指格林威治时间 1970 年 01 月 01 日 00 时 00 分 00 秒(北京时间 1970 年 01 月 01 日 08 时 00 分 00 秒)起至现在的总毫秒数 PS：开始时间结束时间间距不超过1个月 |
| endCreateAt | 是 | int | 创建时间结束时间的时间戳，指格林威治时间 1970 年 01 月 01 日 00 时 00 分 00 秒(北京时间 1970 年 01 月 01 日 08 时 00 分 00 秒)起至现在的总毫秒数 PS：开始时间结束时间间距不超过1个月 |
| pageIndex | 否 | int | 返回页码 默认 1，页码从 1 开始 PS：当前采用分页返回，数量和页数会一起传，如果不传，则采用 默认值 |
| pageSize | 否 | int | 返回数量，默认 100。最大 100 |
| status | 否 | string | 任务状态：initialized 待启动 started已启动 paused 已暂停 finished 已完成 stopped 已停止 |

##### 返回示例

复制```
{
    "errCode": 0,
    "result": {
        "list": [
            {
                "taskId": "6721a32d2c89f300198b8c86",
                "name": "打断暂停测试-视频-声动",
                "status": "finished",
                "createdAt": "2024-10-30 11:08:29",
                "creator": "wxy-zx",
                "videoBotId": "6719e7384871f300288bad5e"
            },
            {
                "taskId": "6721a40e59f2240027cf1e57",
                "name": "打断暂停测试-视频-声动",
                "status": "finished",
                "createdAt": "2024-10-30 11:12:14",
                "creator": "wxy-zx",
                "videoBotId": "6719e7384871f300288bad5e"
            },
            {
                "taskId": "6721a4f90e253c0020a49646",
                "name": "打断暂停测试-视频-声动",
                "status": "finished",
                "createdAt": "2024-10-30 11:16:09",
                "creator": "wxy-zx",
                "videoBotId": "6719e7384871f300288bad5e"
            }
        ],
        "hasNext": true
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败，具体参见下表 |
| result | object | 结果 |
| result.list.taskId | string | 任务ID |
| result.list.name | string | 任务名称 |
| result.list.status | string | 任务状态：initialized 待启动 started已启动 paused 已暂停 finished 已完成 stopped 已停止 |
| result.list.createdAt | string | 创建时间 yyyy-MM-dd HH:mm:ss |
| result.list.creator | string | 创建人账号 |
| result.list.videoBotId | string | 视频机器人ID |
| result.hasNext | boolean | 是否存在下一页 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 1002 | 登录名错误 |
| 1020 | 时间范围错误 |
| 10 | 系统内部错误 |
