Title: 客户名单任务(批次)创建V2--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797822

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
    

##### 简要描述

客户名单导入V2接口需要提供一个 batchId 字段，此字段就是由本接口提供。用于持续导入的任务记录，每个批次最多可以持续导入20万客户名单。如超过需要重新获取 batchId。

##### 请求URL

*   `/openapi/customer/task/v2`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | post |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 账号名 |
| appId | 是 | string | 应用id，固定值，由对接人员提供 |
| name | 是 | string | 导入名单批次(任务)名称 |
| blackListGroups | 否 | array | 导入名单批次(任务)使用的客户黑名单组 |

##### 请求示例

复制```
{
    "name": "客户导入批次001",
    "appId": "d9e81d829bd8fed2",
    "orgCode": "test001",
    "loginName": "test001",
    "blackListGroups": ["66bb07357afe95b4ad066e62"]
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "errInfo": "",
    "result": {
        "batchId": "66bf0936dc6e23610d115bad"
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败 |
| errInfo | string | 失败描述 |
| result | object | 结果 |
| result.batchId | string | 导入批次id |
