Title: 发起AI视频外呼任务--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797742

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

通过接口创建新的外呼任务，传入包括手机号在内的各项参数而发起外呼，传入的手机号会被去重。  
调用接口前请联系客户经理配置外呼线路、视频音频等必要资源。  
调用接口前需要提供出口IP，加入白名单。

##### 请求URL

*   `/api/mediaDeliverPlatform/external/create`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | post |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 否，但与下方sign必须2选1 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| sign | 否，但与上方access-token必须2选1 | string | 见开发前须知中的token/签名生成方式 |
| loginName | 是 | string | 发送任务的账号名 |
| name | 是 | string | 任务名称 |
| mediaType | 是 | string | 发送内容的类型： 视频互动 videoBot |
| videoBotId | 是 | string | 视频互动内容id，支持外呼结果回执接口 |
| shuntAudioBotId | 如果租户启用机型检测，必填 | string | 会使用另外线路和内容外呼苹果手机 |
| isVoiceDivide | 否 | boolean | 是否开启语音分流 |
| audioBotId | 如果isVoiceDivide为true，必填 | string | 音频BotId |
| deliverList | 是 | array | 发送任务的呼叫列表，最大呼叫数量10万，建议每次5000条。注意一下接口超时时间，平均5000条数据的接口请求时间在10s左右 |
| deliverList.name | 否 | string | 用户名称 |
| deliverList.phone | 是 | string | 手机号，或者加密手机号 |
| deliverList.otherInfo | 否 | array | 支持备注，千人千面变量，短信模版等（回执会原样返回） |
| deliverList.otherInfo.title | 否 | string | 标题 |
| deliverList.otherInfo.value | 否 | string | 值 |
| isRelateSendMessage | 否，缺省false | boolean | 是否关联文本短信：true关联，false不关联 |
| msgTemplateId | 如果isRelateSendMessage为true，必填 | string | 文本短信模板id |
| msgTitle | 如果isRelateSendMessage为true，必填 | string | 文本短信签名 |
| sendMessagePhase | 如果isRelateSendMessage为true，必填 | int | 文本短信发送时机 ：1-拨打电话时, 2-电话接通时, 3-通话结束后 |
| isRelateVideoMessage | 否，缺省false | boolean | 是否关联视频短信：true关联，false不关联 |
| videoMessageTemplateId | 如果isRelateVideoMessage为true，必填 | string | 视频短信模板id |
| sendVideoMessagePhase | 如果isRelateVideoMessage为true，必填 | int | 视频短信发送时机： 1-拨打电话时, 2-电话接通时, 3-通话结束后 |
| calloutMode | 否 | int | 起呼方式 1：双向视频起呼 2：语音起呼 |
| sendFlashMessage | 否 | boolean | 是否使用闪信：true使用，false不使用 |
| flashMessageTemplateId | 如果 sendFlashMessage 为true，必填 | string | 闪信模板id |

##### 请求示例

复制```
{
    "orgCode": "spcd_org",
    "sign": "8db1f84648c43e7d0e3f3927baf6108770f5aa7ff68c6c104de04916e44ae2e9",
    "loginName": "test",
    "name": "测试任务",
    "mediaType": "videoBot",
    "videoBotId": "6375dd1293941036cf3a4a5b",
    "deliverList": [
      {"name": "",  "phone": "13500000000", "otherInfo": [{"title": "姓名", "valu": "张三"}]},
      {"phone": "13500000001", "otherInfo": [{"title": "姓名", "value": "张四"}]},
      {"phone": '13500000002', "otherInfo": [{"title": "姓名", "value": "张五"}]},
      {"phone": "4bf8ae0ba3e8c0f17d885b8c5b0af602"}, //手机号支持加密(加密前：13500000003)
    ],
    "isRelateSendMessage": true,
    "msgTemplateId": "600184215",
    "msgTitle": "甜新科技",
    "sendMessagePhase": 1,
    "isRelateVideoMessage": true,
    "videoMessageTemplateId": "16083",
    "sendFlashMessage": true,
    "flashMessageTemplateId": "661f2fc46870835ca0428b71",
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "result": {
        "taskId": "xxxx"
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败，具体参见下表 |
| result | object | 结果 |
| result.taskId | string | 任务id |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 1002 | 登录名错误 |
| 1003 | 没有有效的手机号 |
| 1004 | 变量错误 |
| 1005 | 文本短信参数错误 |
| 1006 | 文本短信模板不存在 |
| 1007 | 视频短信参数错误 |
| 1008 | 视频短信模板不存在 |
| 1009 | 视频未审核通过 |
| 1010 | 无效的botId |
| 1011 | 视频bot未审核通过 |
| 1012 | 您的可用额度不足，请联系商务进行充值 |
| 1013 | 无效的视频内容id |
| 1014 | 无可用线路 |
| 1015 | 闪信模板不存在 |
| 1016 | 无效的shuntAudioBotId |
| 1017 | shuntAudioBot未审核通过 |
| 1018 | 无效的audioBotId |
| 1019 | audioBot未审核通过 |
| 10 | 系统内部错误 |
