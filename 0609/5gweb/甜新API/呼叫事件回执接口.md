Title: 呼叫事件回执接口--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797747

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [解密后示例](https://doc.vcrm.vip:8800/web/#%E8%A7%A3%E5%AF%86%E5%90%8E%E7%A4%BA%E4%BE%8B)
*   [解密后参数说明](https://doc.vcrm.vip:8800/web/#%E8%A7%A3%E5%AF%86%E5%90%8E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [返回参数](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0)
    

##### 简要描述

在每一通呼叫接通时，将通话接通事件回调到您的接口，以便完成后续的业务行为  
您需要实现回调接口，并提供回调地址  
回调返回数据为密文，需要进行解密  
目前无重试机制  
响应体希望能按照下面的返回参数编写。

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

##### 参数

| 参数名 | 必选 | 类型 | 说明 |
| --- | --- | --- | --- |
| data | 是 | String | 加密数据 |

##### 请求示例

复制```
//请求体内容
 {
   "data": "2fd1840a320202df2a2f72881a30b688af68939be980b50794f3bc7332bdec9438dcafc96420df7e4b702f0c73b138fd992bf4c021d21dd86dedc060ee4825a3e081f6baeb95abd1e63d17de69b832d76654296c6284039366b9a97fe1598ee843bcfef639967ac21293766e8364582e9784a2cba28243f3b3278d2e209da30a4c52c25d6089ea77da4d22da61bab869fc10286e150d421ee800558a91cd6d2e698d5198a9c7febda9591b8ccad891ca5eda20d8d7dea40b04ad6e04e1162af4" //通过aes加密后的数据
 }
```

##### 解密后示例

复制```
//通过AES算法解密
{
    "mediaDeliverId": "661618298009b00018f6d05c",
    "callId": "661618298009b00018f6d05f",
    "eventType":"callOn"
    "phone": "17612162706",
    "comment": [{ "title": "policyHolderUserName", "value": "" }, { "title": "upgradeUrl11", "value": "" }],
}
```

##### 解密后参数说明

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| mediaDeliverId | string | 任务id |
| callId | string | 呼叫id |
| eventType | string | 事件类型 callStart: 外呼开始 callOn:接通 |
| phone | string | 手机号码 |
| comment | array | 备注 |
| comment.title | string | 备注title |
| comment.value | string | 备注内容 |

##### 返回参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| errCode | number | 0.成功，其它失败 |
| errInfo | string | 失败信息 |
