import React, { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { toast } from "@/components/ui/use-toast"

interface AuthCodeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (code: string, authCode: string) => Promise<void>
}

export function AuthCodeDialog({ open, onOpenChange, onSubmit }: AuthCodeDialogProps) {
  const [code, setCode] = useState("")
  const [authCode, setAuthCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 发送验证码
  const sendVerificationCode = async () => {
    try {
      setIsSendingCode(true)
      const response = await fetch("/api/settings/email/send-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "发送成功",
          description: "验证码已发送到您的邮箱",
        })
        // 开始倒计时
        setCountdown(60)
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
      } else {
        throw new Error(data.message || "发送验证码失败")
      }
    } catch (error) {
      console.error("发送验证码失败:", error)
      toast({
        title: "发送失败",
        description: error instanceof Error ? error.message : "无法发送验证码",
        variant: "destructive",
      })
    } finally {
      setIsSendingCode(false)
    }
  }

  // 提交验证码和授权码
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!code) {
      toast({
        title: "验证失败",
        description: "请输入验证码",
        variant: "destructive",
      })
      return
    }
    if (!authCode) {
      toast({
        title: "验证失败",
        description: "请输入新的邮箱授权码",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)
      await onSubmit(code, authCode)
      // 成功后清空输入
      setCode("")
      setAuthCode("")
      onOpenChange(false)
    } catch (error) {
      console.error("验证失败:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>修改邮箱授权码</DialogTitle>
          <DialogDescription>
            为了保障系统安全，修改邮箱授权码需要进行邮箱验证。请先获取验证码，然后输入新的授权码。
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="verification-code" className="text-right">
                验证码
              </Label>
              <div className="col-span-3 flex gap-2">
                <Input
                  id="verification-code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="flex-1"
                  placeholder="请输入验证码"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={sendVerificationCode}
                  disabled={isSendingCode || countdown > 0}
                >
                  {isSendingCode ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {countdown > 0 ? `${countdown}秒后重试` : "获取验证码"}
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="auth-code" className="text-right">
                新授权码
              </Label>
              <div className="col-span-3">
                <Input
                  id="auth-code"
                  type="password"
                  value={authCode}
                  onChange={(e) => setAuthCode(e.target.value)}
                  placeholder="请输入新的邮箱授权码"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  提交中...
                </>
              ) : (
                "确认修改"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
