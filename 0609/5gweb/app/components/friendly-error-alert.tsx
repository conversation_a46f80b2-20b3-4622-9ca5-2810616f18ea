'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { RefreshCw } from 'lucide-react'

interface FriendlyErrorAlertProps {
  title?: string
  message?: string
  onRetry?: () => void
  retryText?: string
  className?: string
}

/**
 * 友好的错误提示组件
 * 
 * 提供一个更友好的错误提示界面，不使用红色警告色，而是使用更柔和的界面
 * 
 * @example
 * ```tsx
 * <FriendlyErrorAlert 
 *   title="无法获取短信模板" 
 *   message="系统将自动调整设置，您可以继续创建任务" 
 *   onRetry={() => fetchSmsTemplates()}
 *   retryText="重新获取"
 * />
 * ```
 */
export function FriendlyErrorAlert({
  title = '暂时无法获取数据',
  message = '系统将自动调整设置，您可以继续操作',
  onRetry,
  retryText = '重试',
  className = '',
}: FriendlyErrorAlertProps) {
  return (
    <Card className={`border-gray-200 shadow-sm ${className}`}>
      <CardHeader className="bg-gray-50 border-b border-gray-100 pb-3">
        <CardTitle className="text-gray-700 text-base font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="pt-4 pb-2 text-gray-600 text-sm">
        {message}
      </CardContent>
      {onRetry && (
        <CardFooter className="pt-0 pb-3 flex justify-end">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onRetry}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          >
            <RefreshCw className="mr-2 h-3 w-3" />
            {retryText}
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
