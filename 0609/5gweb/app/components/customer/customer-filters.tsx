"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Search, Plus, Download, FileText, FileSpreadsheet } from "lucide-react"

interface CustomerFiltersProps {
  searchTerm: string
  setSearchTerm: (value: string) => void
  accountTypeFilter: string
  setAccountTypeFilter: (value: string) => void
  statusFilter: string
  setStatusFilter: (value: string) => void
  verificationFilter?: string
  setVerificationFilter?: (value: string) => void
  onExport: (format: string) => void
  onAddUser: () => void
  resetFilters: () => void
}

export function CustomerFilters({
  searchTerm,
  setSearchTerm,
  accountTypeFilter,
  setAccountTypeFilter,
  statusFilter,
  setStatusFilter,
  verificationFilter = "all",
  setVerificationFilter = () => {},
  onExport,
  onAddUser,
  resetFilters,
}: CustomerFiltersProps) {
  return (
    <div className="flex flex-wrap items-center gap-4 py-5 px-6 bg-white bg-opacity-90 rounded-t-lg shadow-md border border-blue-100 mb-0 mx-auto" style={{ width: 'calc(90% - 2px)', marginBottom: '-1px' }}>
      <div className="relative flex-1 min-w-[200px]">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索客户..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8 border-blue-200 focus:border-blue-400"
        />
      </div>

      <div className="w-[180px]">
        <Select value={accountTypeFilter} onValueChange={setAccountTypeFilter}>
          <SelectTrigger>
            <SelectValue placeholder="账户类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="admin">管理员</SelectItem>
            <SelectItem value="enterprise">企业客户</SelectItem>
            <SelectItem value="personal">个人客户</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[180px]">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger>
            <SelectValue placeholder="账户状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="active">启用</SelectItem>
            <SelectItem value="inactive">停用</SelectItem>
            <SelectItem value="pending">待审核</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[180px]">
        <Select value={verificationFilter} onValueChange={setVerificationFilter}>
          <SelectTrigger>
            <SelectValue placeholder="认证状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部认证</SelectItem>
            <SelectItem value="pending">待审核认证</SelectItem>
            <SelectItem value="approved">已通过</SelectItem>
            <SelectItem value="rejected">已拒绝</SelectItem>
            <SelectItem value="none">未提交</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {(searchTerm || accountTypeFilter !== "all" || statusFilter !== "all" || verificationFilter !== "all") && (
        <Button
          variant="outline"
          className="border-blue-200 text-blue-600"
          onClick={() => {
            setSearchTerm("")
            setAccountTypeFilter("all")
            setStatusFilter("all")
            setVerificationFilter("all")
          }}
        >
          清除筛选
        </Button>
      )}

      <div className="ml-auto flex gap-2">
        <Button
          onClick={onAddUser}
          className="bg-blue-500 hover:bg-blue-600 text-white border-0"
        >
          <Plus className="mr-2 h-4 w-4" />
          添加客户
        </Button>
        <Popover>
          <PopoverTrigger asChild>
            <Button className="bg-green-500 hover:bg-green-600 text-white">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56" align="end">
            <div className="space-y-1">
              <h4 className="font-medium text-sm">选择导出格式</h4>
              <Button variant="ghost" className="w-full justify-start" onClick={() => onExport("csv")}>
                <FileText className="mr-2 h-4 w-4" />
                CSV 格式
              </Button>
              <Button variant="ghost" className="w-full justify-start" onClick={() => onExport("excel")}>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Excel 格式
              </Button>
              {/* 移除PDF导出选项 */}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
