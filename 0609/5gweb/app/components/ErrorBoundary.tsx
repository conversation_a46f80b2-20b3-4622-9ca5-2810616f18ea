import { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorService } from '@/lib/error-service';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 使用错误服务记录错误
    ErrorService.logError(error, errorInfo);

    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  public render() {
    if (this.state.hasError) {
      // 如果提供了自定义的fallback组件，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 否则使用默认的错误UI
      return (
        <div className="flex items-center justify-center min-h-[200px] p-4">
          <div className="text-center max-w-md">
            <div className="flex justify-center mb-4">
              <AlertCircle className="h-10 w-10 text-red-500" />
            </div>
            <h2 className="text-lg font-semibold text-gray-700 dark:text-gray-300">页面出现了一些问题</h2>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 mb-4">请稍后再试或联系管理员</p>
            <Button
              onClick={() => window.location.reload()}
              size="sm"
              className="bg-primary hover:bg-primary/90"
            >
              刷新页面
            </Button>
            {process.env.NODE_ENV !== 'production' && this.state.error && (
              <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-left overflow-auto">
                <p className="font-mono text-xs text-red-600 dark:text-red-400">
                  {this.state.error.message}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;