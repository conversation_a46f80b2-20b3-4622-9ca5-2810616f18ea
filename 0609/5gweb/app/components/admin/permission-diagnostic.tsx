"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, CheckCircle, XCircle, RefreshCw } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface DiagnosticReport {
  userRoleExists: boolean
  userRoleHasAllPermissions: boolean
  userRoleHasAllMenus: boolean
  usersWithoutRole: number
  casbinPoliciesConsistent: boolean
  issues: string[]
  recommendations: string[]
}

export function PermissionDiagnostic() {
  const [loading, setLoading] = useState(false)
  const [fixing, setFixing] = useState(false)
  const [report, setReport] = useState<DiagnosticReport | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取诊断报告
  const fetchDiagnosticReport = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch("/api/admin/diagnose-permissions")
      const data = await response.json()
      
      if (data.success) {
        setReport(data.data)
      } else {
        setError(data.message || "获取诊断报告失败")
        toast({
          title: "获取诊断报告失败",
          description: data.message,
          variant: "destructive"
        })
      }
    } catch (err) {
      setError("获取诊断报告时发生错误")
      toast({
        title: "获取诊断报告失败",
        description: "请检查网络连接或联系管理员",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 修复权限问题
  const fixPermissionIssue = async (fixType: string) => {
    try {
      setFixing(true)
      
      const response = await fetch("/api/admin/fix-permissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ fixType })
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "修复成功",
          description: data.message,
          variant: "success"
        })
        
        // 重新获取诊断报告
        await fetchDiagnosticReport()
      } else {
        toast({
          title: "修复失败",
          description: data.message,
          variant: "destructive"
        })
      }
    } catch (err) {
      toast({
        title: "修复失败",
        description: "请检查网络连接或联系管理员",
        variant: "destructive"
      })
    } finally {
      setFixing(false)
    }
  }

  // 修复所有权限问题
  const fixAllPermissionIssues = async () => {
    await fixPermissionIssue("fixAll")
  }

  // 组件挂载时获取诊断报告
  useEffect(() => {
    fetchDiagnosticReport()
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>权限系统诊断</span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchDiagnosticReport}
            disabled={loading}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
            刷新
          </Button>
        </CardTitle>
        <CardDescription>
          检查和修复权限系统中的问题
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-lg">正在诊断权限系统...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>诊断失败</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : report ? (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="font-medium mr-2">USER 角色存在</span>
                  {report.userRoleExists ? (
                    <Badge variant="success" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" /> 是
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="bg-red-100 text-red-800">
                      <XCircle className="h-3 w-3 mr-1" /> 否
                    </Badge>
                  )}
                </div>
                {!report.userRoleExists && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => fixPermissionIssue("createUserRole")}
                    disabled={fixing}
                  >
                    {fixing && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                    创建 USER 角色
                  </Button>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="font-medium mr-2">USER 角色权限完整</span>
                  {report.userRoleHasAllPermissions ? (
                    <Badge variant="success" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" /> 是
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="bg-red-100 text-red-800">
                      <XCircle className="h-3 w-3 mr-1" /> 否
                    </Badge>
                  )}
                </div>
                {!report.userRoleHasAllPermissions && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => fixPermissionIssue("addUserRolePermissions")}
                    disabled={fixing}
                  >
                    {fixing && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                    添加缺失权限
                  </Button>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="font-medium mr-2">USER 角色菜单完整</span>
                  {report.userRoleHasAllMenus ? (
                    <Badge variant="success" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" /> 是
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="bg-red-100 text-red-800">
                      <XCircle className="h-3 w-3 mr-1" /> 否
                    </Badge>
                  )}
                </div>
                {!report.userRoleHasAllMenus && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => fixPermissionIssue("addUserRoleMenus")}
                    disabled={fixing}
                  >
                    {fixing && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                    添加缺失菜单
                  </Button>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="font-medium mr-2">用户角色分配</span>
                  {report.usersWithoutRole === 0 ? (
                    <Badge variant="success" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" /> 正常
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="bg-red-100 text-red-800">
                      <XCircle className="h-3 w-3 mr-1" /> {report.usersWithoutRole} 个用户缺少角色
                    </Badge>
                  )}
                </div>
                {report.usersWithoutRole > 0 && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => fixPermissionIssue("assignRolesToUsers")}
                    disabled={fixing}
                  >
                    {fixing && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                    分配用户角色
                  </Button>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="font-medium mr-2">jCasbin 策略一致性</span>
                  {report.casbinPoliciesConsistent ? (
                    <Badge variant="success" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" /> 一致
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="bg-red-100 text-red-800">
                      <XCircle className="h-3 w-3 mr-1" /> 不一致
                    </Badge>
                  )}
                </div>
                {!report.casbinPoliciesConsistent && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => fixPermissionIssue("syncCasbinPolicies")}
                    disabled={fixing}
                  >
                    {fixing && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                    同步 jCasbin 策略
                  </Button>
                )}
              </div>
            </div>

            {report.issues.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">发现的问题</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {report.issues.map((issue, index) => (
                    <li key={index} className="text-red-600">{issue}</li>
                  ))}
                </ul>
              </div>
            )}

            {report.recommendations.length > 0 && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2">建议的解决方案</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {report.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-blue-600">{recommendation}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            暂无诊断数据
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={fetchDiagnosticReport}
          disabled={loading || fixing}
        >
          {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
          重新诊断
        </Button>
        <Button 
          variant="default" 
          onClick={fixAllPermissionIssues}
          disabled={loading || fixing || !report || report.issues.length === 0}
        >
          {fixing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
          一键修复所有问题
        </Button>
      </CardFooter>
    </Card>
  )
}
