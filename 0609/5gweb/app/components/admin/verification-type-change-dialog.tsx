"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

interface VerificationTypeChangeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: {
    id: string
    username: string
    name?: string
    verificationType: string
  }
  onSuccess?: () => void
}

export function VerificationTypeChangeDialog({
  open,
  onOpenChange,
  user,
  onSuccess
}: VerificationTypeChangeDialogProps) {
  const [newType, setNewType] = useState<string>(
    user.verificationType === "personal" ? "enterprise" : "personal"
  )
  const [reason, setReason] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  const handleSubmit = async () => {
    if (!reason.trim()) {
      toast({
        variant: "destructive",
        title: "请填写变更原因",
        description: "变更原因不能为空"
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch("/api/admin/user/verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          userId: user.id,
          newType,
          reason
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "认证类型变更成功",
          description: `已将用户 ${user.username || user.name || user.id} 的认证类型变更为 ${newType === "personal" ? "个人认证" : "企业认证"}`
        })
        onOpenChange(false)
        if (onSuccess) {
          onSuccess()
        }
      } else {
        toast({
          variant: "destructive",
          title: "认证类型变更失败",
          description: data.message || "请稍后再试"
        })
      }
    } catch (error) {
      console.error("认证类型变更错误:", error)
      toast({
        variant: "destructive",
        title: "认证类型变更失败",
        description: "请稍后再试或联系技术支持"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>变更认证类型</DialogTitle>
          <DialogDescription>
            为用户 {user.name || user.username || user.id} 变更认证类型
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="current-type" className="text-right">
              当前类型
            </Label>
            <div className="col-span-3">
              <span className="px-2 py-1 rounded-md bg-gray-100 text-gray-800">
                {user.verificationType === "personal" 
                  ? "个人认证" 
                  : user.verificationType === "enterprise" 
                    ? "企业认证" 
                    : "未认证"}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="new-type" className="text-right">
              新认证类型
            </Label>
            <Select
              value={newType}
              onValueChange={setNewType}
              disabled={isSubmitting}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="选择新认证类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="personal">个人认证</SelectItem>
                <SelectItem value="enterprise">企业认证</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reason" className="text-right">
              变更原因
            </Label>
            <Textarea
              id="reason"
              placeholder="请输入变更原因"
              className="col-span-3"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={isSubmitting}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            确认变更
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
