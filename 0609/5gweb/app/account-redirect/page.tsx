"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function AccountRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    router.replace("/accounts")
  }, [router])

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">正在重定向...</h1>
        <p>正在将您重定向到账户管理</p>
      </div>
    </div>
  )
} 
