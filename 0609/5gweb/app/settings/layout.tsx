import type React from "react"
import { LayoutDashboard, User, Bell, HelpCircle, Phone } from "lucide-react"

import { Separator } from "@/components/ui/separator"
import { SiteHeader } from "@/components/site-header"
import { MainNav } from "@/components/navigation/main-nav"
import { SidebarNav } from "@/components/navigation/sidebar-nav"

interface SettingsLayoutProps {
  children: React.ReactNode
}

const sidebarNavItems = [
  {
    title: "概览",
    href: "/settings",
    icon: <LayoutDashboard className="mr-2 h-4 w-4" />,
    description: "查看账户设置概览",
  },
  {
    title: "账户",
    href: "/settings/account",
    icon: <User className="mr-2 h-4 w-4" />,
    description: "管理您的个人账户信息",
  },
  {
    title: "通知",
    href: "/settings/notifications",
    icon: <Bell className="mr-2 h-4 w-4" />,
    description: "配置您的通知偏好",
  },
  {
    title: "帮助",
    href: "/settings/help",
    icon: <HelpCircle className="mr-2 h-4 w-4" />,
    description: "获取帮助和支持",
  },
  {
    title: "5G视频外呼",
    href: "/settings/video-call",
    icon: <Phone className="mr-2 h-4 w-4" />,
    description: "管理5G视频外呼任务、机器人和客户名单",
  },
]

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <div className="flex h-screen w-full space-x-20">
      <aside className="flex-col py-6 pl-6 w-80">
        <MainNav className="mb-6" />
        <SidebarNav items={sidebarNavItems} />
      </aside>
      <div className="flex-1 overflow-hidden p-8">{children}</div>
    </div>
  )
}

