"use client"

import { createContext, useContext } from "react"
import { User } from "@/types/user"

// 菜单项接口
export interface MenuItem {
  title: string;
  href: string;
  icon?: string;
  customIconUrl?: string;
  children?: MenuItem[];
}

// Dashboard上下文
export type DashboardContextValue = {
  userInfo: User | null;
  menuItems: MenuItem[];
  isCollapsed: boolean;
  toggleCollapsed: () => void;
  isMobileView: boolean;
}

export const DashboardContext = createContext<DashboardContextValue | null>(null);

/**
 * 使用仪表板上下文的自定义Hook
 * 提供访问仪表板状态和操作的方法
 */
export function useDashboard() {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error("useDashboard must be used within DashboardProvider");
  }
  return context;
}
