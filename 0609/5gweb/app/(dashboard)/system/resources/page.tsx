"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ResourceTable } from "@/components/shared/resource-table";
import { ResourceForm } from "@/components/shared/resource-form";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Resource } from "@/types/resource";

export default function ResourcesPage() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingResource, setEditingResource] = useState<Resource | undefined>();
  const { toast } = useToast();

  // 获取资源列表
  const fetchResources = async () => {
    try {
      const response = await fetch("/api/resources");
      if (!response.ok) throw new Error("获取资源列表失败");
      const data = await response.json();
      setResources(data.items);
    } catch (error) {
      toast({
        title: "错误",
        description: "获取资源列表失败",
        variant: "destructive",
      });
    }
  };

  // 创建资源
  const handleCreate = async (data: any) => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/resources", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("创建资源失败");
      await fetchResources();
      setIsDialogOpen(false);
      toast({
        title: "成功",
        description: "创建资源成功",
      });
    } catch (error) {
      toast({
        title: "错误",
        description: "创建资源失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 更新资源
  const handleUpdate = async (data: any) => {
    if (!editingResource) return;
    try {
      setIsLoading(true);
      const response = await fetch(`/api/resources/${editingResource.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("更新资源失败");
      await fetchResources();
      setIsDialogOpen(false);
      setEditingResource(undefined);
      toast({
        title: "成功",
        description: "更新资源成功",
      });
    } catch (error) {
      toast({
        title: "错误",
        description: "更新资源失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 删除资源
  const handleDelete = async (resource: Resource) => {
    if (!confirm("确定要删除该资源吗？")) return;
    try {
      const response = await fetch(`/api/resources/${resource.id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("删除资源失败");
      await fetchResources();
      toast({
        title: "成功",
        description: "删除资源成功",
      });
    } catch (error) {
      toast({
        title: "错误",
        description: "删除资源失败",
        variant: "destructive",
      });
    }
  };

  // 编辑资源
  const handleEdit = (resource: Resource) => {
    setEditingResource(resource);
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">资源管理</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              新建资源
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingResource ? "编辑资源" : "新建资源"}
              </DialogTitle>
            </DialogHeader>
            <ResourceForm
              initialData={editingResource}
              onSubmit={editingResource ? handleUpdate : handleCreate}
              isLoading={isLoading}
            />
          </DialogContent>
        </Dialog>
      </div>
      <ResourceTable
        resources={resources}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}