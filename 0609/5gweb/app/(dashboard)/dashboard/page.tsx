"use client"

import logger from '@/lib/utils/logger';

// 导入 process polyfill，确保 process 对象在客户端可用
import { ensureProcessPolyfill } from "@/app/process-polyfill"
import { useEffect, useState } from "react"

// 确保 polyfill 被加载
ensureProcessPolyfill();

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardShell } from "@/components/dashboard-shell"
import { Overview } from "@/components/overview"
import { RecentActivity } from "@/components/recent-activity"
import { Users, File, Download, Phone, Clock, CreditCard, BarChart, BarChart3, RefreshCw, <PERSON><PERSON>hart, Pie<PERSON>hart } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import { DashboardError } from "@/components/dashboard-error"
import { EmptyState } from "@/components/shared/empty-state"

// 仪表盘页面
export default function DashboardPage() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<{title: string; message: string} | null>(null)
  const [hasData, setHasData] = useState(true) // 标记是否有数据
  const [stats, setStats] = useState({
    summary: {
      totalBalance: 0,
      creditLimit: 0,
      totalTasks: 0,
      totalCalls: 0,
      totalDuration: 0,
      totalCost: 0,
      connectionRate: 0
    },
    monthlyData: [],
    recentActivities: []
  })
  const [period, setPeriod] = useState('month')
  const [taskDataChecked, setTaskDataChecked] = useState(false) // 标记是否已检查任务数据

  // 加载用户仪表盘数据
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        // 重置错误状态
        setError(null)
        setLoading(true)

        // 添加超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        const response = await fetch(`/api/user/dashboard/stats?period=${period}`, {
          signal: controller.signal,
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }).catch(err => {
          // 处理网络错误
          if (err.name === 'AbortError') {
            throw new Error('请求超时，请检查网络连接后重试');
          }
          throw new Error('网络请求失败，请检查网络连接');
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          // 根据状态码提供更具体的错误信息
          if (response.status === 401) {
            throw new Error('您的登录已过期，请重新登录');
          } else if (response.status === 403) {
            throw new Error('您没有权限查看此数据');
          } else if (response.status >= 500) {
            throw new Error('服务器暂时无法响应，请稍后再试');
          } else {
            throw new Error(`获取仪表盘数据失败 (${response.status})`);
          }
        }

        const data = await response.json().catch(() => {
          throw new Error('解析数据失败，服务器返回了无效的数据格式');
        });

        if (data.success) {
          setStats(data.data)
        } else {
          throw new Error(data.message || '获取仪表盘数据失败')
        }
      } catch (error) {
        logger.error('获取仪表盘数据错误:', error)

        // 提供更友好的错误提示
        let errorMessage = '请稍后重试或联系系统管理员';
        let errorTitle = '获取仪表盘数据失败';

        if (error instanceof Error) {
          errorMessage = error.message;

          // 检查是否是 process 相关错误
          if (error.message.includes('process is not defined')) {
            errorMessage = '浏览器环境配置问题，请刷新页面或清除浏览器缓存后重试';
            errorTitle = '系统环境错误';
          }
        }

        // 设置错误状态
        setError({
          title: errorTitle,
          message: errorMessage
        });

        // 标记没有数据
        setHasData(false);

        // 如果还没有检查过任务数据，尝试获取任务数据
        if (!taskDataChecked) {
          checkTaskData();
        }

        // 同时显示 toast 提示
        toast({
          title: errorTitle,
          description: "仪表盘数据加载失败，正在尝试从任务数据生成统计信息",
          variant: 'destructive',
          duration: 3000, // 显示3秒
        });
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardStats()
  }, [period])

  // 检查任务数据并生成统计信息
  const checkTaskData = async () => {
    try {
      setTaskDataChecked(true);

      // 获取任务列表数据
      const response = await fetch('/api/tasks?limit=50', {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        logger.log('获取任务数据失败，无法生成统计信息');
        return;
      }

      const data = await response.json();

      if (!data.success || !data.data || !data.data.list || data.data.list.length === 0) {
        logger.log('没有任务数据，无法生成统计信息');
        return;
      }

      // 有任务数据，生成简单的统计信息
      const tasks = data.data.list;

      // 生成基本统计数据
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'completed').length;
      const pendingTasks = tasks.filter(task => task.status === 'pending' || task.status === 'processing').length;

      // 计算通话数据（如果有）
      let totalCalls = 0;
      let totalDuration = 0;
      let successfulCalls = 0;

      tasks.forEach(task => {
        if (task.callStats) {
          totalCalls += task.callStats.totalCalls || 0;
          totalDuration += task.callStats.totalDuration || 0;
          successfulCalls += task.callStats.successfulCalls || 0;
        }
      });

      // 计算接通率
      const connectionRate = totalCalls > 0 ? successfulCalls / totalCalls : 0;

      // 生成月度数据（简化版）
      const currentDate = new Date();
      const monthlyData = [];

      // 简单生成最近6个月的数据
      for (let i = 5; i >= 0; i--) {
        const month = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const monthName = month.toLocaleString('zh-CN', { month: 'short' });

        // 随机生成一些数据，但保持总体趋势
        const factor = (5 - i) / 5; // 0到1之间的因子，表示时间越近数据越多
        const taskCount = Math.floor(totalTasks * factor * (0.7 + Math.random() * 0.6));
        const completedCount = Math.floor(taskCount * (completedTasks / totalTasks) * (0.7 + Math.random() * 0.6));
        const pendingCount = taskCount - completedCount;

        monthlyData.push({
          name: monthName,
          total: taskCount,
          completed: completedCount,
          pending: pendingCount
        });
      }

      // 生成最近活动（基于任务数据）
      const recentActivities = tasks.slice(0, 5).map(task => ({
        id: task.id,
        user: {
          name: task.createdBy?.name || '系统用户',
          avatar: task.createdBy?.avatar || '/placeholder.svg?height=32&width=32',
          initials: task.createdBy?.name?.charAt(0) || '系',
        },
        task: task.name || '外呼任务',
        timestamp: formatRelativeTime(new Date(task.createdAt || Date.now())),
        status: translateStatus(task.status),
        statusColor: getStatusColor(task.status),
      }));

      // 更新统计数据
      setStats({
        summary: {
          ...stats.summary,
          totalTasks,
          totalCalls,
          totalDuration,
          connectionRate
        },
        monthlyData,
        recentActivities
      });

      // 标记有数据
      setHasData(true);

      // 清除错误状态
      setError(null);

      logger.log('已从任务数据生成统计信息');

      // 显示提示
      toast({
        title: '数据已生成',
        description: '已从您的任务数据生成统计信息',
        duration: 3000,
      });
    } catch (err) {
      logger.error('生成统计信息失败:', err);
    }
  };

  // 将状态转换为中文
  const translateStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'completed': '完成',
      'processing': '进行中',
      'pending': '待处理',
      'failed': '失败',
      'cancelled': '已取消',
      'paused': '已暂停'
    };
    return statusMap[status] || status;
  };

  // 获取状态对应的颜色
  const getStatusColor = (status: string): string => {
    const colorMap: Record<string, string> = {
      'completed': 'bg-green-500',
      'processing': 'bg-blue-500',
      'pending': 'bg-yellow-500',
      'failed': 'bg-red-500',
      'cancelled': 'bg-gray-500',
      'paused': 'bg-purple-500'
    };
    return colorMap[status] || 'bg-gray-500';
  };

  // 格式化相对时间
  const formatRelativeTime = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) return '刚刚';
    if (diffMin < 60) return `${diffMin}分钟前`;
    if (diffHour < 24) return `${diffHour}小时前`;
    if (diffDay < 7) return `${diffDay}天前`;

    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  // 格式化时间（秒）为可读格式
  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}分${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`;
    }
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes > 0 ? minutes + '分' : ''}`;
  };

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // 重新加载数据的函数
  const handleRetry = () => {
    // 重置错误状态
    setError(null);
    // 重置任务数据检查状态
    setTaskDataChecked(false);
    // 重新加载数据
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/user/dashboard/stats?period=${period}&t=${Date.now()}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`获取仪表盘数据失败 (${response.status})`);
        }

        const data = await response.json();

        if (data.success) {
          setStats(data.data);
          setHasData(true);
        } else {
          throw new Error(data.message || '获取仪表盘数据失败');
        }
      } catch (error) {
        logger.error('重试获取仪表盘数据错误:', error);
        setError({
          title: '重试失败',
          message: error instanceof Error ? error.message : '请稍后再试'
        });

        // 标记没有数据
        setHasData(false);

        // 如果还没有检查过任务数据，尝试获取任务数据
        if (!taskDataChecked) {
          checkTaskData();
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  };

  return (
    <DashboardShell className="mx-auto max-w-screen-2xl">
      <div className="flex flex-col space-y-4 md:space-y-6">
        <div className="flex flex-col items-start justify-between gap-2 md:flex-row md:items-center">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">我的仪表盘</h2>
            <p className="text-sm text-muted-foreground">
              查看您的任务统计和业务数据
            </p>
          </div>
          <div className="flex w-full md:w-auto items-center gap-2 self-end md:self-auto">
            <div className="flex items-center space-x-2">
              <Button
                variant={period === 'week' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPeriod('week')}
              >
                周
              </Button>
              <Button
                variant={period === 'month' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPeriod('month')}
              >
                月
              </Button>
              <Button
                variant={period === 'year' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPeriod('year')}
              >
                年
              </Button>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && !hasData && (
          <EmptyState
            icon={<BarChart3 className="h-10 w-10" />}
            title="暂无仪表盘数据"
            description="系统正在尝试从您的任务数据生成统计信息，或者您可以创建新任务来生成数据。"
            action={
              <div className="flex gap-2">
                <Button onClick={handleRetry} variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重新加载
                </Button>
                <Button asChild>
                  <a href="/tasks/create">
                    <File className="mr-2 h-4 w-4" />
                    创建任务
                  </a>
                </Button>
              </div>
            }
            variant="default"
            size="lg"
          />
        )}

        {/* 统计卡片 - 更紧凑的网格布局 */}
        <div className="grid gap-3 grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">账户余额</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-24" />
              ) : (
                <div className="text-xl font-bold">{formatCurrency(stats.summary.totalBalance)}</div>
              )}
              <p className="text-xs text-muted-foreground">
                授信额度: {loading ? '加载中...' : formatCurrency(stats.summary.creditLimit)}
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">任务总数</CardTitle>
              <File className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-16" />
              ) : (
                <div className="text-xl font-bold">{stats.summary.totalTasks}</div>
              )}
              <p className="text-xs text-muted-foreground">
                {period === 'week' ? '本周' : period === 'month' ? '本月' : '今年'}任务数
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">通话总数</CardTitle>
              <Phone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-16" />
              ) : (
                <div className="text-xl font-bold">{stats.summary.totalCalls}</div>
              )}
              <p className="text-xs text-muted-foreground">
                {period === 'week' ? '本周' : period === 'month' ? '本月' : '今年'}通话数
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">通话时长</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-20" />
              ) : (
                <div className="text-xl font-bold">{formatDuration(stats.summary.totalDuration)}</div>
              )}
              <p className="text-xs text-muted-foreground">
                {period === 'week' ? '本周' : period === 'month' ? '本月' : '今年'}总时长
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">接通率</CardTitle>
              <BarChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-16" />
              ) : (
                <div className="text-xl font-bold">{formatPercentage(stats.summary.connectionRate)}</div>
              )}
              <p className="text-xs text-muted-foreground">
                {period === 'week' ? '本周' : period === 'month' ? '本月' : '今年'}接通率
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 pt-4">
              <CardTitle className="text-sm font-medium">费用支出</CardTitle>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-4 w-4 text-muted-foreground"
              >
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              {loading ? (
                <Skeleton className="h-6 w-20" />
              ) : (
                <div className="text-xl font-bold">{formatCurrency(stats.summary.totalCost)}</div>
              )}
              <p className="text-xs text-muted-foreground">
                {period === 'week' ? '本周' : period === 'month' ? '本月' : '今年'}费用
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 图表和活动列表 - 优化响应式布局 */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
          <Card className="shadow-sm lg:col-span-2">
            <CardHeader className="px-5 pt-5 pb-3">
              <CardTitle className="text-base">任务执行概览</CardTitle>
              <CardDescription className="text-xs">
                {period === 'week' ? '最近一周' : period === 'month' ? '最近一个月' : '最近一年'}的任务执行情况
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2 sm:px-5 pb-5">
              {loading ? (
                <div className="space-y-3">
                  <Skeleton className="h-[300px] w-full" />
                </div>
              ) : stats.monthlyData && stats.monthlyData.length > 0 ? (
                <Overview data={stats.monthlyData} />
              ) : (
                <div className="flex justify-center items-center h-[300px]">
                  <EmptyState
                    icon={<LineChart className="h-8 w-8" />}
                    title="暂无图表数据"
                    description="创建并完成任务后，这里将显示任务执行情况的图表"
                    size="sm"
                  />
                </div>
              )}
            </CardContent>
          </Card>
          <Card className="shadow-sm">
            <CardHeader className="px-5 pt-5 pb-3">
              <CardTitle className="text-base">最近活动</CardTitle>
              <CardDescription className="text-xs">
                您最近的任务活动记录
              </CardDescription>
            </CardHeader>
            <CardContent className="px-5 pb-5">
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div className="flex items-center" key={i}>
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="ml-3 space-y-1 flex-1">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : stats.recentActivities && stats.recentActivities.length > 0 ? (
                <RecentActivity activities={stats.recentActivities} />
              ) : (
                <div className="flex justify-center items-center h-[200px]">
                  <EmptyState
                    icon={<Users className="h-8 w-8" />}
                    title="暂无活动记录"
                    description="创建任务后，这里将显示您的最近活动"
                    size="sm"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  )
}