"use client"

import { useEffect, useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestPage() {
  const [cookieInfo, setCookieInfo] = useState<string>("加载中...")

  useEffect(() => {
    // 获取所有cookie信息（仅用于测试）
    const cookies = document.cookie.split(';').map(cookie => cookie.trim())
    setCookieInfo(cookies.join('\n'))
  }, [])

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-4">
        <h1 className="text-2xl font-bold">登录测试页面</h1>
        <p>如果你能看到这个页面，说明登录成功并且重定向正常工作！</p>
        
        <Card>
          <CardHeader>
            <CardTitle>Cookie 信息（仅用于测试）</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-40">
              {cookieInfo}
            </pre>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
