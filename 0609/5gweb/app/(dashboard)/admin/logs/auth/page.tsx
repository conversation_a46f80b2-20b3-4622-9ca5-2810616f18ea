"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Search, RefreshCw, Download, ChevronDown, ArrowLeft, RotateCcw, Settings, Trash2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface AuthLog {
  id: string
  userId: string
  action: string
  status: string
  details?: any
  ipAddress?: string
  userAgent?: string
  createdAt: string
  user: {
    id: string
    username: string
    name?: string
    email: string
    image?: string
    roleCode: string
  }
}

export default function AuthLogsPage() {
  const router = useRouter()
  const [logs, setLogs] = useState<AuthLog[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [logStats, setLogStats] = useState<any>(null)
  const [rotateLoading, setRotateLoading] = useState(false)
  const [cleanupLoading, setCleanupLoading] = useState(false)
  const [cleanupDays, setCleanupDays] = useState(30)
  const [showStatsDialog, setShowStatsDialog] = useState(false)

  // 筛选条件
  const [userId, setUserId] = useState<string>("")
  const [action, setAction] = useState<string>("")
  const [status, setStatus] = useState<string>("")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  // 加载日志列表
  const loadLogs = async (pageNum = page) => {
    try {
      setLoading(true)

      // 构建查询参数
      const params = new URLSearchParams({
        page: pageNum.toString(),
        pageSize: pageSize.toString(),
      })

      if (userId) params.append("userId", userId)
      if (action && action !== "all") params.append("action", action)
      if (status && status !== "all") params.append("status", status)
      if (startDate) params.append("startDate", startDate.toISOString())
      if (endDate) params.append("endDate", endDate.toISOString())

      const response = await fetch(`/api/logs/auth?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setLogs(data.data.logs)
        setTotal(data.data.total)
        setPage(data.data.page)
        setTotalPages(data.data.totalPages)
      } else {
        toast.error("加载认证日志列表失败")
      }
    } catch (error) {
      console.error("加载认证日志列表失败:", error)
      toast.error("加载认证日志列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
    loadLogs(newPage)
  }

  // 处理筛选
  const handleFilter = () => {
    setPage(1)
    loadLogs(1)
  }

  // 重置筛选条件
  const resetFilters = () => {
    setUserId("")
    setAction("all")
    setStatus("all")
    setStartDate(undefined)
    setEndDate(undefined)
    setPage(1)
    loadLogs(1)
  }

  // 导出日志
  const handleExport = async (exportFormat = "json") => {
    try {
      const params = new URLSearchParams()

      if (userId) params.append("userId", userId)
      if (action && action !== "all") params.append("action", action)
      if (status && status !== "all") params.append("status", status)
      if (startDate) params.append("startDate", startDate.toISOString())
      if (endDate) params.append("endDate", endDate.toISOString())
      params.append("format", exportFormat) // 添加导出格式参数

      const response = await fetch(`/api/logs/auth/export?${params.toString()}`)

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url

        // 根据格式设置文件名后缀
        const timestamp = format(new Date(), "yyyyMMdd_HHmmss")
        let extension = "json"
        if (exportFormat === "csv") extension = "csv"
        if (exportFormat === "excel") extension = "xls"

        a.download = `认证日志_${timestamp}.${extension}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success("导出成功")
      } else {
        toast.error("导出失败")
      }
    } catch (error) {
      console.error("导出认证日志失败:", error)
      toast.error("导出认证日志失败")
    }
  }

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "yyyy-MM-dd HH:mm:ss")
  }

  // 获取操作类型标签样式
  const getActionStyle = (action: string) => {
    // 认证相关操作
    if (action.startsWith('login_')) {
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100"
    }
    if (action.startsWith('token_')) {
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-100"
    }
    if (action.startsWith('session_')) {
      return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-100"
    }

    return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
  }

  // 获取状态标签样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100"
      case "info":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
    }
  }

  // 获取操作类型标签文本
  const getActionLabel = (action: string) => {
    switch (action) {
      case "login_attempt":
        return "登录尝试"
      case "token_validation":
        return "令牌验证"
      case "session_create":
        return "会话创建"
      case "session_refresh":
        return "会话刷新"
      case "session_invalidate":
        return "会话注销"
      default:
        return action
    }
  }

  // 获取状态标签文本
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "success":
        return "成功"
      case "error":
        return "失败"
      case "warning":
        return "警告"
      case "info":
        return "信息"
      default:
        return status
    }
  }

  // 加载日志统计信息
  const loadLogStats = async () => {
    try {
      const response = await fetch('/api/cron/log-rotation')
      const data = await response.json()

      if (data.success) {
        setLogStats(data.data.authLogs)
      } else {
        toast.error("加载日志统计信息失败")
      }
    } catch (error) {
      console.error("加载日志统计信息失败:", error)
      toast.error("加载日志统计信息失败")
    }
  }

  // 执行日志轮转
  const handleRotateLogs = async () => {
    try {
      setRotateLoading(true)
      const response = await fetch('/api/cron/log-rotation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'rotate' })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        loadLogStats()
        loadLogs()
      } else {
        toast.error(data.message || '日志轮转失败')
      }
    } catch (error) {
      console.error('日志轮转失败:', error)
      toast.error('日志轮转失败')
    } finally {
      setRotateLoading(false)
    }
  }

  // 执行日志清理
  const handleCleanupLogs = async () => {
    try {
      setCleanupLoading(true)
      const response = await fetch('/api/cron/log-rotation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'cleanup', days: cleanupDays })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        loadLogStats()
        loadLogs()
      } else {
        toast.error(data.message || '日志清理失败')
      }
    } catch (error) {
      console.error('日志清理失败:', error)
      toast.error('日志清理失败')
    } finally {
      setCleanupLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadLogs()
    loadLogStats()
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin/logs')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold">认证日志</h2>
        </div>
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700">
                <Download className="mr-2 h-4 w-4" />
                导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport("json")}>导出为JSON</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("csv")}>导出为CSV</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("excel")}>导出为Excel</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                管理
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setShowStatsDialog(true)}>
                <BarChart className="mr-2 h-4 w-4" />
                日志统计
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => document.getElementById('rotate-logs-trigger')?.click()}>
                <RotateCcw className="mr-2 h-4 w-4" />
                日志轮转
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => document.getElementById('cleanup-logs-trigger')?.click()}>
                <Trash2 className="mr-2 h-4 w-4" />
                日志清理
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={() => loadLogs()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">筛选条件</CardTitle>
          <CardDescription>
            设置筛选条件查询特定的认证日志
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">用户ID</label>
              <Input
                placeholder="输入用户ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">操作类型</label>
              <Select value={action} onValueChange={setAction}>
                <SelectTrigger>
                  <SelectValue placeholder="选择操作类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="login_attempt">登录尝试</SelectItem>
                  <SelectItem value="token_validation">令牌验证</SelectItem>
                  <SelectItem value="session_create">会话创建</SelectItem>
                  <SelectItem value="session_refresh">会话刷新</SelectItem>
                  <SelectItem value="session_invalidate">会话注销</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">状态</label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="error">失败</SelectItem>
                  <SelectItem value="warning">警告</SelectItem>
                  <SelectItem value="info">信息</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">开始日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "yyyy-MM-dd") : "选择日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">结束日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "yyyy-MM-dd") : "选择日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-end space-x-2 col-span-1 md:col-span-2">
              <Button onClick={handleFilter}>
                <Search className="mr-2 h-4 w-4" />
                查询
              </Button>
              <Button variant="outline" onClick={resetFilters}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">认证日志列表</CardTitle>
          <CardDescription>
            显示系统认证日志记录，共 {total} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>操作时间</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>操作类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>IP地址</TableHead>
                  <TableHead>详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">
                        {formatDateTime(log.createdAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {log.user.image && (
                            <img
                              src={log.user.image}
                              alt={log.user.name || log.user.username}
                              className="h-6 w-6 rounded-full"
                            />
                          )}
                          <div>
                            <div className="font-medium">
                              {log.user.name || log.user.username}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {log.user.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={getActionStyle(log.action)}
                          variant="outline"
                        >
                          {getActionLabel(log.action)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={getStatusStyle(log.status)}
                          variant="outline"
                        >
                          {getStatusLabel(log.status)}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.ipAddress || "-"}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            toast.info(
                              <pre className="text-xs overflow-auto max-h-80">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>,
                              {
                                duration: 10000,
                              }
                            )
                          }}
                        >
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, page - 1))}
                      disabled={page === 1}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (pageNum) => (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={pageNum === page}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  )}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        handlePageChange(Math.min(totalPages, page + 1))
                      }
                      disabled={page === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 日志统计对话框 */}
      <Dialog open={showStatsDialog} onOpenChange={setShowStatsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>认证日志统计</DialogTitle>
            <DialogDescription>
              查看认证日志的统计信息和摘要
            </DialogDescription>
          </DialogHeader>

          {logStats ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">总日志数</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{logStats.total}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">最近24小时</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{logStats.last24Hours}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">最近7天</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{logStats.last7Days}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">最近30天</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{logStats.last30Days}</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">成功</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xl font-bold text-green-600">{logStats.byStatus.success}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">错误</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xl font-bold text-red-600">{logStats.byStatus.error}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">警告</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xl font-bold text-yellow-600">{logStats.byStatus.warning}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xl font-bold text-blue-600">{logStats.byStatus.info}</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">最早日志时间</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      {logStats.oldestLogDate ? format(new Date(logStats.oldestLogDate), "yyyy-MM-dd HH:mm:ss") : "-"}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">最新日志时间</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      {logStats.newestLogDate ? format(new Date(logStats.newestLogDate), "yyyy-MM-dd HH:mm:ss") : "-"}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <div className="py-4 text-center">加载统计信息中...</div>
          )}

          <DialogFooter>
            <Button onClick={() => setShowStatsDialog(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 日志轮转确认对话框 */}
      <AlertDialog>
        <AlertDialogTrigger id="rotate-logs-trigger" className="hidden" />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>日志轮转</AlertDialogTitle>
            <AlertDialogDescription>
              日志轮转将根据日志等级清理过期的日志：
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>成功的日志保留15天</li>
                <li>错误的日志保留90天</li>
                <li>警告的日志保留30天</li>
                <li>信息的日志保留7天</li>
              </ul>
              <p className="mt-2">此操作不可撤销，是否继续？</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRotateLogs}
              disabled={rotateLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {rotateLoading ? '正在轮转...' : '执行轮转'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 日志清理确认对话框 */}
      <AlertDialog>
        <AlertDialogTrigger id="cleanup-logs-trigger" className="hidden" />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>日志清理</AlertDialogTitle>
            <AlertDialogDescription>
              <div className="space-y-4">
                <p>日志清理将删除指定天数之前的所有认证日志。</p>

                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium">保留天数：</label>
                  <Select value={cleanupDays.toString()} onValueChange={(value) => setCleanupDays(parseInt(value))}>
                    <SelectTrigger className="w-24">
                      <SelectValue placeholder="选择天数" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7天</SelectItem>
                      <SelectItem value="15">15天</SelectItem>
                      <SelectItem value="30">30天</SelectItem>
                      <SelectItem value="60">60天</SelectItem>
                      <SelectItem value="90">90天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <p className="text-sm text-red-500">注意：此操作不可撤销，清理后的日志将无法恢复。</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCleanupLogs}
              disabled={cleanupLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {cleanupLoading ? '正在清理...' : '执行清理'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
