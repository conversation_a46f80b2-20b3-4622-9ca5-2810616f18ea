"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Search, RefreshCw, Download, ChevronDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface SystemLog {
  id: string
  userId: string
  action: string
  module: string
  resourceId?: string
  resourceType?: string
  details?: any
  ipAddress?: string
  userAgent?: string
  createdAt: string
  user: {
    id: string
    username: string
    name?: string
    email: string
    image?: string
    roleCode: string
  }
}

export default function SystemLogsPage() {
  const router = useRouter()
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // 筛选条件
  const [userId, setUserId] = useState<string>("")
  const [action, setAction] = useState<string>("")
  const [module, setModule] = useState<string>("")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  // 加载日志列表
  const loadLogs = async (pageNum = page) => {
    try {
      setLoading(true)

      // 构建查询参数
      const params = new URLSearchParams({
        page: pageNum.toString(),
        pageSize: pageSize.toString(),
      })

      if (userId) params.append("userId", userId)
      if (action && action !== "all") params.append("action", action)
      if (module && module !== "all") params.append("module", module)
      if (startDate) params.append("startDate", startDate.toISOString())
      if (endDate) params.append("endDate", endDate.toISOString())

      const response = await fetch(`/api/logs?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setLogs(data.data.logs)
        setTotal(data.data.total)
        setPage(data.data.page)
        setTotalPages(data.data.totalPages)
      } else {
        toast.error("加载日志列表失败")
      }
    } catch (error) {
      console.error("加载日志列表失败:", error)
      toast.error("加载日志列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
    loadLogs(newPage)
  }

  // 处理筛选
  const handleFilter = () => {
    setPage(1)
    loadLogs(1)
  }

  // 重置筛选条件
  const resetFilters = () => {
    setUserId("")
    setAction("all")
    setModule("all")
    setStartDate(undefined)
    setEndDate(undefined)
    setPage(1)
    loadLogs(1)
  }

  // 导出日志
  const handleExport = async (exportFormat = "json") => {
    try {
      const params = new URLSearchParams()

      if (userId) params.append("userId", userId)
      if (action && action !== "all") params.append("action", action)
      if (module && module !== "all") params.append("module", module)
      if (startDate) params.append("startDate", startDate.toISOString())
      if (endDate) params.append("endDate", endDate.toISOString())
      params.append("format", exportFormat) // 添加导出格式参数

      const response = await fetch(`/api/logs/export?${params.toString()}`)

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url

        // 根据格式设置文件名后缀
        const timestamp = format(new Date(), "yyyyMMdd_HHmmss")
        let extension = "json"
        if (exportFormat === "csv") extension = "csv"
        if (exportFormat === "excel") extension = "xls"

        a.download = `系统日志_${timestamp}.${extension}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success("导出成功")
      } else {
        toast.error("导出失败")
      }
    } catch (error) {
      console.error("导出系统日志失败:", error)
      toast.error("导出系统日志失败")
    }
  }

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "yyyy-MM-dd HH:mm:ss")
  }

  // 获取操作类型标签样式
  const getActionStyle = (action: string) => {
    // 认证相关操作
    if (action.startsWith('login_')) {
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100"
    }
    if (action.startsWith('token_')) {
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-100"
    }
    if (action.startsWith('session_')) {
      return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-100"
    }

    // 标准操作
    switch (action) {
      case "create":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
      case "update":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
      case "delete":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
      case "view":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
    }
  }

  // 获取模块标签样式
  const getModuleStyle = (module: string) => {
    switch (module) {
      case "role":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100"
      case "user":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100"
      case "menu":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-100"
      case "logs":
        return "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-100"
      case "auth":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
    }
  }

  // 初始加载
  useEffect(() => {
    loadLogs()
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">系统操作日志</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => router.push('/admin/logs/auth')}>
            认证日志
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700">
                <Download className="mr-2 h-4 w-4" />
                导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport("json")}>导出为JSON</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("csv")}>导出为CSV</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("excel")}>导出为Excel</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => loadLogs()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">筛选条件</CardTitle>
          <CardDescription>
            设置筛选条件查询特定的操作日志
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">用户ID</label>
              <Input
                placeholder="输入用户ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">操作类型</label>
              <Select value={action} onValueChange={setAction}>
                <SelectTrigger>
                  <SelectValue placeholder="选择操作类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="create">创建</SelectItem>
                  <SelectItem value="update">更新</SelectItem>
                  <SelectItem value="delete">删除</SelectItem>
                  <SelectItem value="view">查看</SelectItem>
                  <SelectItem value="login_attempt">登录尝试</SelectItem>
                  <SelectItem value="token_validation">令牌验证</SelectItem>
                  <SelectItem value="session_create">会话创建</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">模块</label>
              <Select value={module} onValueChange={setModule}>
                <SelectTrigger>
                  <SelectValue placeholder="选择模块" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="role">角色</SelectItem>
                  <SelectItem value="user">用户</SelectItem>
                  <SelectItem value="menu">菜单</SelectItem>
                  <SelectItem value="logs">日志</SelectItem>
                  <SelectItem value="auth">认证</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">开始日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "yyyy-MM-dd") : "选择日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">结束日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "yyyy-MM-dd") : "选择日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-end space-x-2 col-span-1 md:col-span-2">
              <Button onClick={handleFilter}>
                <Search className="mr-2 h-4 w-4" />
                查询
              </Button>
              <Button variant="outline" onClick={resetFilters}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">日志列表</CardTitle>
          <CardDescription>
            显示系统操作日志记录，共 {total} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>操作时间</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>操作类型</TableHead>
                  <TableHead>模块</TableHead>
                  <TableHead>资源ID</TableHead>
                  <TableHead>IP地址</TableHead>
                  <TableHead>详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">
                        {formatDateTime(log.createdAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {log.user.image && (
                            <img
                              src={log.user.image}
                              alt={log.user.name || log.user.username}
                              className="h-6 w-6 rounded-full"
                            />
                          )}
                          <div>
                            <div className="font-medium">
                              {log.user.name || log.user.username}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {log.user.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getActionStyle(
                            log.action
                          )}`}
                        >
                          {log.action === "create"
                            ? "创建"
                            : log.action === "update"
                            ? "更新"
                            : log.action === "delete"
                            ? "删除"
                            : log.action === "view"
                            ? "查看"
                            : log.action === "login_attempt"
                            ? "登录尝试"
                            : log.action === "token_validation"
                            ? "令牌验证"
                            : log.action === "session_create"
                            ? "会话创建"
                            : log.action}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getModuleStyle(
                            log.module
                          )}`}
                        >
                          {log.module === "role"
                            ? "角色"
                            : log.module === "user"
                            ? "用户"
                            : log.module === "menu"
                            ? "菜单"
                            : log.module === "logs"
                            ? "日志"
                            : log.module === "auth"
                            ? "认证"
                            : log.module}
                        </span>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.resourceId || "-"}
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.ipAddress || "-"}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            toast.info(
                              <pre className="text-xs overflow-auto max-h-80">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>,
                              {
                                duration: 10000,
                              }
                            )
                          }}
                        >
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, page - 1))}
                      disabled={page === 1}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (pageNum) => (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={pageNum === page}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  )}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        handlePageChange(Math.min(totalPages, page + 1))
                      }
                      disabled={page === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
