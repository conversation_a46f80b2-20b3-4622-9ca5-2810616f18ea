"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getSystemSettings } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"

export default function AppearanceSettingsPage() {
  const [backgroundImage, setBackgroundImage] = useState("")
  const [footerText, setFooterText] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const data = await getSystemSettings()
        if (data) {
          setBackgroundImage(data.backgroundImage || "")
          setFooterText(data.footerText || "")
        }
      } catch (error) {
        console.error("获取系统设置失败:", error)
      }
    }

    fetchSettings()
  }, [])

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // 模拟保存设置
      await new Promise((resolve) => setTimeout(resolve, 1000))
      toast({
        title: "设置已保存",
        description: "系统外观设置已成功更新。",
      })
    } catch (error) {
      toast({
        title: "保存失败",
        description: "无法保存系统设置，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">系统外观设置</h1>

      <Tabs defaultValue="login" className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-2 mb-6">
          <TabsTrigger value="login">登录页设置</TabsTrigger>
          <TabsTrigger value="dashboard">仪表盘设置</TabsTrigger>
        </TabsList>

        <TabsContent value="login">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>登录页背景</CardTitle>
                <CardDescription>设置登录页面的背景图片，建议使用1920x1080像素的图片</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="background-image">背景图片URL</Label>
                    <Input
                      id="background-image"
                      placeholder="输入图片URL或上传图片"
                      value={backgroundImage}
                      onChange={(e) => setBackgroundImage(e.target.value)}
                    />
                  </div>

                  <div className="mt-4">
                    <Button variant="outline" className="mr-2">
                      上传图片
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setBackgroundImage("/placeholder.svg?height=1080&width=1920")}
                    >
                      恢复默认
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>页脚设置</CardTitle>
                <CardDescription>设置登录页面底部显示的页脚文本，支持HTML标签</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="footer-text">页脚文本</Label>
                  <Textarea
                    id="footer-text"
                    placeholder="输入页脚文本，支持HTML标签"
                    value={footerText}
                    onChange={(e) => setFooterText(e.target.value)}
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6 flex justify-end">
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "保存中..." : "保存设置"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="dashboard">
          <Card>
            <CardHeader>
              <CardTitle>仪表盘设置</CardTitle>
              <CardDescription>设置仪表盘的主题和布局选项</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">仪表盘设置功能正在开发中...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

