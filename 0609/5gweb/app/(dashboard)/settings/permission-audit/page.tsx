'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { toast } from 'sonner'
import { RefreshCw, Download, Search, Calendar } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { zhCN } from 'date-fns/locale'

interface PermissionAuditLog {
  id: string
  userId: string
  action: string
  module: string
  resourceId: string
  resourceType: string
  details: any
  ipAddress: string
  userAgent: string
  createdAt: string
  user: {
    id: string
    username: string
    name: string
    email: string
    roleCode: string
  }
}

interface QueryResult {
  logs: PermissionAuditLog[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 操作类型选项
const ACTION_OPTIONS = [
  { value: 'all', label: '全部动作' },
  { value: 'add', label: '添加' },
  { value: 'remove', label: '删除' },
  { value: 'update', label: '更新' },
  { value: 'view', label: '查看' }
]

// 目标类型选项
const TARGET_TYPE_OPTIONS = [
  { value: 'all', label: '全部类型' },
  { value: 'role', label: '角色' },
  { value: 'user', label: '用户' },
  { value: 'menu', label: '菜单' },
  { value: 'resource', label: '资源' },
  { value: 'operation', label: '操作' }
]

export default function PermissionAuditPage() {
  // 状态定义
  const [logs, setLogs] = useState<PermissionAuditLog[]>([])
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(false)
  
  // 筛选条件状态
  const [filters, setFilters] = useState({
    userId: '',
    action: 'all',
    targetType: 'all',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined
  })

  // 加载日志数据
  const loadLogs = async (pageNum: number = page) => {
    try {
      setLoading(true)

      const params = new URLSearchParams({
        page: pageNum.toString(),
        pageSize: pageSize.toString()
      })

      // 添加筛选参数
      if (filters.userId.trim()) {
        params.append('userId', filters.userId.trim())
      }
      if (filters.action !== 'all') {
        params.append('action', filters.action)
      }
      if (filters.targetType !== 'all') {
        params.append('targetType', filters.targetType)
      }
      if (filters.startDate) {
        params.append('startDate', filters.startDate.toISOString())
      }
      if (filters.endDate) {
        params.append('endDate', filters.endDate.toISOString())
      }

      const response = await fetch(`/api/admin/permission-audit?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setLogs(data.data.logs || [])
        setTotal(data.data.total || 0)
        setPage(data.data.page || pageNum)
        setTotalPages(data.data.totalPages || 0)
      } else {
        toast.error(data.message || '加载权限审计日志失败')
        setLogs([])
        setTotal(0)
        setTotalPages(0)
      }
    } catch (error) {
      console.error('加载权限审计日志失败:', error)
      toast.error('加载权限审计日志失败')
      setLogs([])
      setTotal(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadLogs(1)
  }, [pageSize])

  // 更新筛选条件
  const updateFilter = (key: keyof typeof filters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // 搜索处理
  const handleSearch = () => {
    setPage(1)
    loadLogs(1)
  }

  // 重置筛选条件
  const handleReset = () => {
    setFilters({
      userId: '',
      action: 'all',
      targetType: 'all',
      startDate: undefined,
      endDate: undefined
    })
    setPage(1)
    // 延迟加载以确保状态更新
    setTimeout(() => loadLogs(1), 0)
  }

  // 翻页处理
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages || loading) return
    setPage(newPage)
    loadLogs(newPage)
  }

  // 格式化函数
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
    } catch {
      return dateString
    }
  }

  const formatAction = (action: string) => {
    const actionMap: Record<string, string> = {
      add: '添加',
      remove: '删除',
      update: '更新',
      view: '查看'
    }
    return actionMap[action] || action
  }

  const formatTargetType = (type: string) => {
    const typeMap: Record<string, string> = {
      role: '角色',
      user: '用户',
      menu: '菜单',
      resource: '资源',
      operation: '操作'
    }
    return typeMap[type] || type
  }

  const showLogDetails = (details: any) => {
    try {
      const formattedDetails = JSON.stringify(details, null, 2)
      toast.info(
        <pre className="text-xs overflow-auto max-h-80 whitespace-pre-wrap">
          {formattedDetails}
        </pre>,
        { duration: 10000 }
      )
    } catch {
      toast.error('无法显示日志详情')
    }
  }

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>权限审计日志</CardTitle>
          <CardDescription>查看系统权限变更的历史记录</CardDescription>
        </CardHeader>
        <CardContent>
          {/* 筛选条件区域 */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {/* 用户ID输入 */}
            <div className="flex flex-col space-y-1">
              <label className="text-sm font-medium">用户ID</label>
              <Input
                placeholder="输入用户ID"
                value={filters.userId}
                onChange={(e) => updateFilter('userId', e.target.value)}
                className="w-48"
                disabled={loading}
              />
            </div>

            {/* 动作选择 */}
            <div className="flex flex-col space-y-1">
              <label className="text-sm font-medium">操作类型</label>
              <Select
                value={filters.action}
                onValueChange={(value) => updateFilter('action', value)}
                disabled={loading}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ACTION_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 目标类型选择 */}
            <div className="flex flex-col space-y-1">
              <label className="text-sm font-medium">目标类型</label>
              <Select
                value={filters.targetType}
                onValueChange={(value) => updateFilter('targetType', value)}
                disabled={loading}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TARGET_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 开始日期 */}
            <div className="flex flex-col space-y-1">
              <label className="text-sm font-medium">开始日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-auto pl-3 text-left font-normal",
                      !filters.startDate && "text-muted-foreground"
                    )}
                    disabled={loading}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.startDate 
                      ? format(filters.startDate, 'yyyy-MM-dd', { locale: zhCN }) 
                      : '选择开始日期'
                    }
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filters.startDate}
                    onSelect={(date) => updateFilter('startDate', date)}
                    disabled={loading}
                    locale={zhCN}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* 结束日期 */}
            <div className="flex flex-col space-y-1">
              <label className="text-sm font-medium">结束日期</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-auto pl-3 text-left font-normal",
                      !filters.endDate && "text-muted-foreground"
                    )}
                    disabled={loading}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.endDate 
                      ? format(filters.endDate, 'yyyy-MM-dd', { locale: zhCN }) 
                      : '选择结束日期'
                    }
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={filters.endDate}
                    onSelect={(date) => updateFilter('endDate', date)}
                    disabled={loading}
                    locale={zhCN}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2 mb-4">
            <Button
              onClick={handleSearch}
              disabled={loading}
              className="flex items-center space-x-2"
            >
              <Search className="h-4 w-4" />
              <span>搜索</span>
            </Button>

            <Button
              variant="outline"
              onClick={() => loadLogs(page)}
              disabled={loading}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
              <span>刷新</span>
            </Button>

            <Button
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              重置
            </Button>
          </div>

          {/* 日志表格 */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">操作时间</TableHead>
                  <TableHead className="w-[120px]">操作用户</TableHead>
                  <TableHead className="w-[100px]">操作类型</TableHead>
                  <TableHead className="w-[120px]">模块</TableHead>
                  <TableHead className="w-[100px]">目标类型</TableHead>
                  <TableHead className="w-[120px]">资源ID</TableHead>
                  <TableHead className="w-[120px]">IP地址</TableHead>
                  <TableHead className="w-[100px]">详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>加载中...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      暂无审计日志
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id} className="hover:bg-muted/50">
                      <TableCell className="font-mono text-xs">
                        {formatDate(log.createdAt)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {log.user?.name || log.user?.username || '未知用户'}
                      </TableCell>
                      <TableCell>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                          {formatAction(log.action)}
                        </span>
                      </TableCell>
                      <TableCell>{log.module || '-'}</TableCell>
                      <TableCell>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                          {formatTargetType(log.resourceType)}
                        </span>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.resourceId || '-'}
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.ipAddress || '-'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => showLogDetails(log.details)}
                          className="h-8 px-2"
                        >
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页控件 */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                共 {total} 条记录，第 {page}/{totalPages} 页
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={page === 1 || loading}
                >
                  首页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page - 1)}
                  disabled={page <= 1 || loading}
                >
                  上一页
                </Button>
                <span className="text-sm text-muted-foreground px-2">
                  {page} / {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page >= totalPages || loading}
                >
                  下一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={page === totalPages || loading}
                >
                  末页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
