"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Pencil, Plus, Trash2, X } from "lucide-react"
import { smsTemplateService, type SmsTemplate } from "@/lib/api/sms-template-service"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

export default function SmsTemplatesPage() {
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 模板列表状态
  const [templates, setTemplates] = useState<SmsTemplate[]>([])
  const [totalTemplates, setTotalTemplates] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  // 筛选状态
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  // 对话框状态
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<SmsTemplate | null>(null)

  // 表单状态
  const [templateCode, setTemplateCode] = useState("")
  const [templateName, setTemplateName] = useState("")
  const [templateContent, setTemplateContent] = useState("")
  const [templateType, setTemplateType] = useState<"text" | "video" | "flash">("text")
  const [templateActive, setTemplateActive] = useState(true)

  // 页面加载时获取模板列表
  useEffect(() => {
    fetchTemplates()
  }, [currentPage, activeTab, searchQuery])

  // 获取模板列表
  const fetchTemplates = async () => {
    setIsLoading(true)
    try {
      const params: any = {
        page: currentPage,
        pageSize,
        search: searchQuery || undefined,
      }

      if (activeTab !== "all") {
        params.type = activeTab
      }

      const response = await smsTemplateService.getTemplates(params)

      if (response.success) {
        setTemplates(response.data.items)
        setTotalTemplates(response.data.total)
        setTotalPages(response.data.totalPages)
      } else {
        toast({
          title: "获取模板列表失败",
          description: response.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("获取模板列表失败:", error)
      toast({
        title: "获取模板列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 创建模板
  const handleCreateTemplate = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      const response = await smsTemplateService.createTemplate({
        code: templateCode,
        name: templateName,
        content: templateContent,
        type: templateType,
        isActive: templateActive,
      })

      if (response.success) {
        toast({
          title: "创建成功",
          description: "短信模板已成功创建",
          variant: "success",
        })
        resetForm()
        setShowCreateDialog(false)
        fetchTemplates()
      } else {
        toast({
          title: "创建失败",
          description: response.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("创建模板失败:", error)
      toast({
        title: "创建失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 更新模板
  const handleUpdateTemplate = async () => {
    if (!selectedTemplate || !validateForm(true)) return

    setIsLoading(true)
    try {
      const response = await smsTemplateService.updateTemplate(selectedTemplate.id, {
        name: templateName,
        content: templateContent,
        type: templateType,
        isActive: templateActive,
      })

      if (response.success) {
        toast({
          title: "更新成功",
          description: "短信模板已成功更新",
          variant: "success",
        })
        resetForm()
        setShowEditDialog(false)
        fetchTemplates()
      } else {
        toast({
          title: "更新失败",
          description: response.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("更新模板失败:", error)
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 删除模板
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return

    setIsLoading(true)
    try {
      const response = await smsTemplateService.deleteTemplate(selectedTemplate.id)

      if (response.success) {
        toast({
          title: "删除成功",
          description: "短信模板已成功删除",
          variant: "success",
        })
        setShowDeleteDialog(false)
        fetchTemplates()
      } else {
        toast({
          title: "删除失败",
          description: response.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("删除模板失败:", error)
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 表单验证
  const validateForm = (isEdit = false) => {
    if (!isEdit && !templateCode) {
      toast({
        title: "表单错误",
        description: "请输入模板代码",
        variant: "destructive",
      })
      return false
    }

    if (!templateName) {
      toast({
        title: "表单错误",
        description: "请输入模板名称",
        variant: "destructive",
      })
      return false
    }

    if (!templateContent) {
      toast({
        title: "表单错误",
        description: "请输入模板内容",
        variant: "destructive",
      })
      return false
    }

    if (!templateType) {
      toast({
        title: "表单错误",
        description: "请选择模板类型",
        variant: "destructive",
      })
      return false
    }

    return true
  }

  // 重置表单
  const resetForm = () => {
    setTemplateCode("")
    setTemplateName("")
    setTemplateContent("")
    setTemplateType("text")
    setTemplateActive(true)
    setSelectedTemplate(null)
  }

  // 打开编辑对话框
  const openEditDialog = (template: SmsTemplate) => {
    setSelectedTemplate(template)
    setTemplateCode(template.code)
    setTemplateName(template.name)
    setTemplateContent(template.content)
    setTemplateType(template.type as "text" | "video" | "flash")
    setTemplateActive(template.isActive)
    setShowEditDialog(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (template: SmsTemplate) => {
    setSelectedTemplate(template)
    setShowDeleteDialog(true)
  }

  // 获取模板类型显示文本
  const getTemplateTypeText = (type: string) => {
    switch (type) {
      case "text":
        return "文本短信"
      case "video":
        return "视频短信"
      case "flash":
        return "闪信"
      default:
        return type
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="短信模板管理" text="管理系统短信模板" />

      <Card>
        <CardHeader>
          <CardTitle>短信模板列表</CardTitle>
          <CardDescription>管理系统中使用的短信模板</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-[400px]">
                <TabsList>
                  <TabsTrigger value="all">全部</TabsTrigger>
                  <TabsTrigger value="text">文本短信</TabsTrigger>
                  <TabsTrigger value="video">视频短信</TabsTrigger>
                  <TabsTrigger value="flash">闪信</TabsTrigger>
                </TabsList>
              </Tabs>
              <Input
                placeholder="搜索模板名称或内容"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-[300px]"
              />
            </div>
            <Button onClick={() => {
              resetForm()
              setShowCreateDialog(true)
            }}>
              <Plus className="mr-2 h-4 w-4" />
              新建模板
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>模板代码</TableHead>
                  <TableHead>模板名称</TableHead>
                  <TableHead>模板类型</TableHead>
                  <TableHead>模板内容</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : templates.length > 0 ? (
                  templates.map((template) => (
                    <TableRow key={template.id}>
                      <TableCell className="font-medium">{template.code}</TableCell>
                      <TableCell>{template.name}</TableCell>
                      <TableCell>{getTemplateTypeText(template.type)}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{template.content}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            template.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {template.isActive ? "启用" : "停用"}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(template.createdAt).toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => openEditDialog(template)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="text-red-600"
                            onClick={() => openDeleteDialog(template)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      没有找到模板
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="flex justify-end mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      className={
                        currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建模板对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>新建短信模板</DialogTitle>
            <DialogDescription>创建新的短信模板</DialogDescription>
          </DialogHeader>
          <DialogClose
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            onClick={() => {
              resetForm()
              setShowCreateDialog(false)
            }}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogClose>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-code" className="text-right">
                模板代码
              </Label>
              <Input
                id="template-code"
                value={templateCode}
                onChange={(e) => setTemplateCode(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-name" className="text-right">
                模板名称
              </Label>
              <Input
                id="template-name"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-type" className="text-right">
                模板类型
              </Label>
              <Select value={templateType} onValueChange={(value: "text" | "video" | "flash") => setTemplateType(value)}>
                <SelectTrigger id="template-type" className="col-span-3">
                  <SelectValue placeholder="选择模板类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">文本短信</SelectItem>
                  <SelectItem value="video">视频短信</SelectItem>
                  <SelectItem value="flash">闪信</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="template-content" className="text-right pt-2">
                模板内容
              </Label>
              <Textarea
                id="template-content"
                value={templateContent}
                onChange={(e) => setTemplateContent(e.target.value)}
                className="col-span-3"
                rows={5}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-active" className="text-right">
                是否启用
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="template-active"
                  checked={templateActive}
                  onCheckedChange={setTemplateActive}
                />
                <Label htmlFor="template-active">{templateActive ? "启用" : "停用"}</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              resetForm()
              setShowCreateDialog(false)
            }}>
              取消
            </Button>
            <Button onClick={handleCreateTemplate} disabled={isLoading}>
              {isLoading ? "创建中..." : "创建"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑模板对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑短信模板</DialogTitle>
            <DialogDescription>编辑现有短信模板</DialogDescription>
          </DialogHeader>
          <DialogClose
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            onClick={() => {
              resetForm()
              setShowEditDialog(false)
            }}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogClose>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-template-code" className="text-right">
                模板代码
              </Label>
              <Input
                id="edit-template-code"
                value={templateCode}
                disabled
                className="col-span-3 bg-gray-100"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-template-name" className="text-right">
                模板名称
              </Label>
              <Input
                id="edit-template-name"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-template-type" className="text-right">
                模板类型
              </Label>
              <Select value={templateType} onValueChange={(value: "text" | "video" | "flash") => setTemplateType(value)}>
                <SelectTrigger id="edit-template-type" className="col-span-3">
                  <SelectValue placeholder="选择模板类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">文本短信</SelectItem>
                  <SelectItem value="video">视频短信</SelectItem>
                  <SelectItem value="flash">闪信</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="edit-template-content" className="text-right pt-2">
                模板内容
              </Label>
              <Textarea
                id="edit-template-content"
                value={templateContent}
                onChange={(e) => setTemplateContent(e.target.value)}
                className="col-span-3"
                rows={5}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-template-active" className="text-right">
                是否启用
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="edit-template-active"
                  checked={templateActive}
                  onCheckedChange={setTemplateActive}
                />
                <Label htmlFor="edit-template-active">{templateActive ? "启用" : "停用"}</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              resetForm()
              setShowEditDialog(false)
            }}>
              取消
            </Button>
            <Button onClick={handleUpdateTemplate} disabled={isLoading}>
              {isLoading ? "更新中..." : "更新"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除模板确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除模板 &quot;{selectedTemplate?.name}&quot; 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTemplate} disabled={isLoading}>
              {isLoading ? "删除中..." : "删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}
