"use client"

import logger from '@/lib/utils/logger';

/**
 * 系统设置页面组件
 * 提供系统全局配置和个性化设置功能
 *
 * 功能模块：
 * 1. 基本信息设置
 *    - 网站名称：系统显示的名称
 *    - 网站Logo：系统Logo图片
 *    - 页脚文本：网站底部显示的版权信息等
 *
 * 2. 主题设置
 *    - 主题模式：明亮/暗黑/跟随系统
 *    - 主题颜色：主色调、次要颜色等
 *    - 界面风格：布局、动画等
 *
 * 3. 功能开关
 *    - 用户注册：是否允许新用户注册
 *    - 密码重置：是否启用密码重置功能
 *    - 系统通知：是否启用系统通知
 *
 * 状态管理：
 * - settings: 系统设置状态
 * - isLoading: 加载状态
 * - isSaving: 保存状态
 * - errors: 错误信息
 *
 * API 调用：
 * - getSystemSettings: 获取系统设置
 * - updateSystemSettings: 更新系统设置
 */

import React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { useTheme } from "next-themes"
import { toast } from "@/components/ui/use-toast"

// UI Components
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"

// Icons
import { Icons } from "@/components/icons"
import {
  ChevronRight,
  ChevronDown,
  Plus,
  Trash2,
  LayoutDashboard,
  Users,
  Settings,
  Upload,
  FileText,
  Phone,
  Bell,
  Mail,
  Calendar,
  Pencil,
  UserRound,
  PanelRight,
  Save,
  PlusCircle,
  Edit,
} from "lucide-react"

/**
 * 系统设置接口
 */
interface SystemSettings {
  siteName: string
  logo: string
  footerText?: string
  description?: string
  keywords?: string
  applicationName?: string
  appleMobileWebAppTitle?: string
  theme: {
    primaryColor: string
    mode: "light" | "dark" | "system"
  }
  features: {
    enableRegistration: boolean
    enablePasswordReset: boolean
    enableNotifications: boolean
  }
  security?: {
    passwordMinLength?: number
    requireSpecialChar?: boolean
    requireNumber?: boolean
    requireUppercase?: boolean
    loginAttempts?: number
    sessionTimeout?: number
  }
  loginPage?: {
    backgroundImage?: string
    backgroundEffect?: "none" | "particles" | "grid" | "dataLines" | "all"
    title?: string
    subtitle?: string
    features?: Array<{
      icon: string
      text: string
    }>
  }
}

/**
 * 默认系统设置
 */
const defaultSettings: SystemSettings = {
  siteName: "外呼管理系统",
  logo: "/logo.png",
  footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
  description: "高效的外呼任务管理平台",
  keywords: "外呼,管理系统,任务管理",
  applicationName: "外呼管理系统",
  appleMobileWebAppTitle: "外呼系统",
  theme: {
    primaryColor: "#0284c7",
    mode: "system"
  },
  features: {
    enableRegistration: true,
    enablePasswordReset: true,
    enableNotifications: true
  },
  security: {
    passwordMinLength: 8,
    requireSpecialChar: true,
    requireNumber: true,
    requireUppercase: true,
    loginAttempts: 5,
    sessionTimeout: 30
  },
  loginPage: {
    backgroundImage: "/placeholder.svg?height=1080&width=1920",
    backgroundEffect: "all",
    title: "外呼管理系统",
    subtitle: "提升工作效率的得力助手",
    features: [
      { icon: "user", text: "专业的客户管理" },
      { icon: "lock", text: "安全的数据保护" },
      { icon: "mail", text: "高效的沟通工具" }
    ]
  }
}

// 可用图标列表 - 用于菜单设置
const availableIcons = [
  { name: "LayoutDashboard", icon: LayoutDashboard },
  { name: "Users", icon: Users },
  { name: "Settings", icon: Settings },
  { name: "Upload", icon: Upload },
  { name: "FileText", icon: FileText },
  { name: "Phone", icon: Phone },
  { name: "Bell", icon: Bell },
  { name: "Mail", icon: Mail },
  { name: "Calendar", icon: Calendar },
  { name: "UserRound", icon: UserRound },
  { name: "PanelRight", icon: PanelRight },
]

// 菜单项类型定义
interface MenuItem {
  id: string
  title: string
  path: string
  href: string
  icon: string
  isVisible: boolean
  hasChildren: boolean
  children: MenuItem[]
  order?: number
  customIconUrl?: string // 添加自定义图标URL字段
  code?: string // 与jCasbin兼容的菜单代码
  resource?: string // 与jCasbin兼容的资源标识符
}

// 生成唯一ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 9)
}

// 默认菜单配置
const defaultMenuItems: MenuItem[] = []

/**
 * 为数据生成签名
 * @param data 要签名的数据
 * @param expirationHours 过期时间（小时），默认24小时
 * @returns 带签名和过期时间的数据
 */
const signData = (data: any, expirationHours: number = 24): { data: any, signature: string, expiresAt: number } => {
  // 简单的签名方法，实际项目中可以使用更复杂的算法
  const dataString = JSON.stringify(data);
  // 创建一个简单的签名（实际项目中应使用更安全的方法）
  const signature = btoa(dataString.split('').map(c => c.charCodeAt(0).toString(16)).join(''));
  // 设置过期时间（默认24小时）
  const expiresAt = Date.now() + expirationHours * 60 * 60 * 1000;
  return { data, signature, expiresAt };
};

/**
 * 验证数据签名和过期时间
 * @param signedData 带签名的数据
 * @returns 如果签名有效且未过期则返回true，否则返回false
 */
const verifySignature = (signedData: { data: any, signature: string, expiresAt?: number }): boolean => {
  try {
    // 检查是否过期
    if (signedData.expiresAt && signedData.expiresAt < Date.now()) {
      logger.log('数据已过期，过期时间：', new Date(signedData.expiresAt).toLocaleString());
      return false;
    }

    // 验证签名
    const { data, signature } = signedData;
    const dataString = JSON.stringify(data);
    const expectedSignature = btoa(dataString.split('').map(c => c.charCodeAt(0).toString(16)).join(''));
    return signature === expectedSignature;
  } catch (error) {
    logger.error("签名验证失败:", error);
    return false;
  }
};

/**
 * 将数据库菜单格式转换为前端格式
 * 确保与NextAuth.js和jCasbin权限模型兼容
 */
const dbToFrontendMenu = (dbMenus: any[]): MenuItem[] => {
  // 创建一个映射表，用于快速查找菜单项
  const menuMap = new Map();

  // 首先将所有菜单项添加到映射表中
  dbMenus.forEach(menu => {
    menuMap.set(menu.id, {
      id: menu.id,
      title: menu.name,
      path: menu.path,
      href: menu.path, // 使用path作为href
      icon: menu.icon || 'LayoutDashboard',
      isVisible: menu.visible,
      hasChildren: false, // 先设为false，后面再更新
      children: [],
      order: menu.order,
      // 添加与jCasbin兼容的字段
      code: menu.code, // 保留原始code用于权限检查
      resource: `menu:${menu.code}` // 资源标识符，用于jCasbin
    });
  });

  // 构建菜单树
  const rootMenus: MenuItem[] = [];
  dbMenus.forEach(menu => {
    const menuItem = menuMap.get(menu.id);
    if (menu.parentId && menuMap.has(menu.parentId)) {
      // 如果有父菜单，则添加到父菜单的children中
      const parentMenu = menuMap.get(menu.parentId);
      parentMenu.hasChildren = true;
      parentMenu.children.push(menuItem);
    } else {
      // 如果没有父菜单，则为根菜单
      rootMenus.push(menuItem);
    }
  });

  // 按order排序
  return rootMenus.sort((a, b) => (a.order || 0) - (b.order || 0));
};

/**
 * 将前端菜单格式转换为数据库格式
 * 确保与NextAuth.js和jCasbin权限模型兼容
 */
const frontendToDbMenu = (frontendMenus: MenuItem[]): any[] => {
  const dbMenus: any[] = [];

  // 递归处理菜单项
  const processMenu = (menu: MenuItem, parentId: string | null = null) => {
    dbMenus.push({
      id: menu.id,
      code: menu.code || menu.id, // 优先使用code，如果没有则使用id
      name: menu.title,
      path: menu.path,
      icon: menu.icon,
      parentId: parentId,
      order: menu.order || 0,
      visible: menu.isVisible
    });

    // 处理子菜单
    if (menu.hasChildren && menu.children.length > 0) {
      menu.children.forEach(child => {
        processMenu(child, menu.id);
      });
    }
  };

  // 处理所有根菜单
  frontendMenus.forEach(menu => {
    processMenu(menu);
  });

  return dbMenus;
};

// 渲染菜单图标
const renderIcon = (iconName: string, customIconUrl?: string) => {
  // 优先使用自定义图标URL
  if (customIconUrl) {
    return (
      <img
        src={customIconUrl}
        alt="菜单图标"
        className="h-4 w-4 object-contain"
      />
    );
  }

  const icon = availableIcons.find(i => i.name === iconName)
  if (icon) {
    return React.createElement(icon.icon, { className: "h-4 w-4" })
  }
  return <LayoutDashboard className="h-4 w-4" />
}

export default function SettingsPage() {
  const router = useRouter()
  const { theme, setTheme } = useTheme()

  // 从URL参数获取默认选项卡
  const [activeTab, setActiveTab] = useState<string>("basic")

  // 状态管理
  const [mounted, setMounted] = useState(false)
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [dialogContent, setDialogContent] = useState({
    title: "",
    description: "",
    action: null as (() => void) | null
  })

  // 菜单设置相关状态
  const [menuItems, setMenuItems] = useState<MenuItem[]>(defaultMenuItems)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [currentItem, setCurrentItem] = useState<Partial<MenuItem> | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [newItemDialogOpen, setNewItemDialogOpen] = useState(false)
  const [childDialogOpen, setChildDialogOpen] = useState(false)
  const [isEditingChild, setIsEditingChild] = useState(false)
  const [currentParent, setCurrentParent] = useState<MenuItem | null>(null)
  const [currentChild, setCurrentChild] = useState<Partial<MenuItem> | null>(null)
  const [draggingItemId, setDraggingItemId] = useState<string | null>(null)
  const [customIconFile, setCustomIconFile] = useState<File | null>(null)

  // 菜单数据状态
  const [isDataFromServer, setIsDataFromServer] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<number>(Date.now())
  const [isDataExpired, setIsDataExpired] = useState(false)

  // 处理展开逻辑
  const toggleExpand = (itemId: string) => {
    if (expandedItems.includes(itemId)) {
      setExpandedItems(expandedItems.filter((id) => id !== itemId))
    } else {
      setExpandedItems([...expandedItems, itemId])
    }
  }

  // 确保组件挂载后再渲染，避免主题闪烁
  useEffect(() => {
    setMounted(true)

    // 从URL参数获取选项卡值
    const params = new URLSearchParams(window.location.search)
    const tabParam = params.get('tab')
    if (tabParam && ['basic', 'theme', 'features', 'security', 'loginPage', 'menu'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [])

  /**
   * 从localStorage加载菜单的辅助函数
   * @param forceRefresh 是否强制从服务器刷新数据，默认为false
   */
  const tryLoadFromLocalStorage = async (forceRefresh: boolean = false) => {
    try {
      // 先尝试加载带签名的菜单数据
      const storedSignedData = localStorage.getItem("signedMenuItems")

      // 如果有存储的数据且不强制刷新
      if (storedSignedData && !forceRefresh) {
        const signedData = JSON.parse(storedSignedData)

        // 验证签名和过期时间
        if (verifySignature(signedData)) {
          logger.error('从localStorage加载带签名的菜单数据成功')
          const parsedItems = signedData.data
          // 确保所有项目都有order字段
          const itemsWithOrder = parsedItems.map((item: MenuItem, index: number) => ({
            ...item,
            order: item.order ?? index
          }))
          // 按order属性排序
          setMenuItems(itemsWithOrder.sort((a: MenuItem, b: MenuItem) =>
            (a.order || 0) - (b.order || 0)
          ))

          // 更新数据状态
          setIsDataFromServer(false)
          setLastUpdated(signedData.expiresAt ? signedData.expiresAt - 48 * 60 * 60 * 1000 : Date.now())
          setIsDataExpired(false)

          // 检查数据是否即将过期（小于6小时）
          if (signedData.expiresAt && signedData.expiresAt - Date.now() < 6 * 60 * 60 * 1000) {
            setIsDataExpired(true)
          }

          return
        } else {
          console.warn("数据已过期或签名无效，尝试从服务器刷新")
          // 如果数据过期或签名无效，尝试从服务器刷新
          await refreshFromServer()
          return
        }
      }

      // 如果强制刷新或没有带签名的数据，尝试从服务器刷新
      if (forceRefresh || !storedSignedData) {
        await refreshFromServer()
        return
      }

      // 如果服务器刷新失败，尝试加载旧格式数据
      const storedMenuItems = localStorage.getItem("menuItems")
      if (storedMenuItems) {
        console.log('从localStorage加载旧格式菜单数据成功')
        const parsedItems = JSON.parse(storedMenuItems)
        // 确保所有项目都有order字段
        const itemsWithOrder = parsedItems.map((item: MenuItem, index: number) => ({
          ...item,
          order: item.order ?? index
        }))
        // 按order属性排序
        setMenuItems(itemsWithOrder.sort((a: MenuItem, b: MenuItem) =>
          (a.order || 0) - (b.order || 0)
        ))

        // 更新数据状态
        setIsDataFromServer(false)
        setLastUpdated(Date.now())
        setIsDataExpired(true) // 旧格式数据视为过期

        // 将旧格式数据转换为带签名的新格式
        const signedData = signData(itemsWithOrder, 24) // 设置24小时过期
        localStorage.setItem("signedMenuItems", JSON.stringify(signedData))
      } else {
        console.log('未找到菜单数据，使用默认空菜单')
        setMenuItems([])
        setIsDataFromServer(false)
        setLastUpdated(Date.now())
        setIsDataExpired(true)
      }
    } catch (innerError) {
      console.error("从localStorage加载菜单失败:", innerError)
      setMenuItems([])
      setIsDataFromServer(false)
      setLastUpdated(Date.now())
      setIsDataExpired(true)
    }
  }

  /**
   * 从服务器刷新菜单数据
   */
  const refreshFromServer = async () => {
    try {
      console.log('尝试从服务器刷新菜单数据...')
      const menuResponse = await fetch('/api/menus', {
        headers: {
          'Content-Type': 'application/json',
          // 确保包含会话cookie，NextAuth.js会自动处理
        },
      })

      if (menuResponse.ok) {
        const menuData = await menuResponse.json()

        if (menuData.success && menuData.data.length > 0) {
          console.log('从服务器成功刷新菜单数据')
          // 如果API加载成功，转换为前端格式并更新状态
          const frontendMenus = dbToFrontendMenu(menuData.data)
          setMenuItems(frontendMenus)

          // 更新数据状态
          const now = Date.now()
          setIsDataFromServer(true)
          setLastUpdated(now)
          setIsDataExpired(false)

          // 同时更新localStorage，保持一致性，添加签名
          // 设置为48小时过期，确保管理员有足够的时间使用缓存数据
          const signedData = signData(frontendMenus, 48)
          localStorage.setItem("signedMenuItems", JSON.stringify(signedData))
          return true
        }
      }
      return false
    } catch (error) {
      console.error('从服务器刷新菜单数据失败:', error)
      return false
    }
  }

  /**
   * 加载系统设置
   */
  useEffect(() => {
    async function loadSettings() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/settings')
        const data = await response.json()

        if (data.success) {
          setSettings(data.data)
          // 同步主题设置，确保安全访问
          if (data.data.theme?.mode) {
            setTheme(data.data.theme.mode)
          }
        } else {
      toast({
            title: "加载失败",
            description: data.message || "无法加载系统设置",
            variant: "destructive",
      })
        }

        // 加载菜单项
        try {
          // 使用新的加载逻辑，先检查本地存储，如果过期或无效则从服务器刷新
          await tryLoadFromLocalStorage(false)
        } catch (error) {
          console.error("加载菜单项失败:", error)
          // 尝试从localStorage加载旧格式数据
          const storedMenuItems = localStorage.getItem("menuItems")
          if (storedMenuItems) {
            try {
              const parsedItems = JSON.parse(storedMenuItems)
              setMenuItems(parsedItems)
            } catch (e) {
              setMenuItems([])
            }
          } else {
            setMenuItems([])
          }
        }
      } catch (error) {
        console.error("加载系统设置失败:", error)
        toast({
          title: "加载失败",
          description: "无法连接到服务器，请检查网络连接",
        variant: "destructive",
      })
      } finally {
        setIsLoading(false)
      }
    }

    if (mounted) {
      loadSettings()
    }
  }, [mounted, setTheme])

  /**
   * 保存系统设置
   * @param showDialog 是否显示对话框，默认为true
   * @param autoRefresh 是否自动刷新页面，默认为false
   */
  const handleSave = async (showDialog: boolean = true, autoRefresh: boolean = false) => {
    try {
      setIsSaving(true)

      // 验证必填字段
      if (!settings.siteName) {
        toast({
          title: "验证失败",
          description: "网站名称不能为空",
          variant: "destructive",
        })
        return
      }

      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...settings,
          theme: settings.theme || defaultSettings.theme,
          features: settings.features || defaultSettings.features,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "保存失败")
      }

      const data = await response.json()

      if (data.success) {
        // 更新本地状态
        setSettings(data.data)

        // 如果需要自动刷新页面
        if (autoRefresh) {
          window.location.reload()
        }

        // 如果需要显示对话框
        if (showDialog) {
          setDialogContent({
            title: "✅ 保存成功",
            description: "系统设置已更新，部分设置可能需要刷新页面后生效。",
            action: () => {
              // 刷新页面以应用新设置
              window.location.reload()
            }
          })
          setShowDialog(true)
        }
      } else {
        throw new Error(data.message || "保存失败")
      }
    } catch (error) {
      console.error("保存系统设置失败:", error)
      // 显示错误对话框
      setDialogContent({
        title: "❌ 保存失败",
        description: error instanceof Error ? error.message : "无法保存系统设置",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * 处理基本信息变更
   * @param field 变更的字段名
   * @param value 新的值
   */
  const handleBasicInfoChange = (field: keyof SystemSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  /**
   * 处理Logo上传
   * @param event 文件上传事件
   */
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "格式错误",
        description: "请上传JPG、PNG、GIF或WebP格式的图片",
        variant: "destructive",
      })
      return
    }

    // 验证文件大小，限制为2MB
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "文件过大",
        description: "Logo图片大小不能超过2MB",
        variant: "destructive",
      })
      return
    }

    // 不需要在客户端验证图片尺寸，因为服务器会自动处理成100x100px

    try {
      // 创建 FormData
      const formData = new FormData()
      formData.append('logo', file)

      // 显示上传中提示
      toast({
        title: "正在上传",
        description: "正在处理图片，请稍候...",
      })

      // 使用新的专用Logo上传API
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        handleBasicInfoChange('logo', data.url)
        toast({
          title: "上传成功",
          description: data.message || "Logo已更新并自动处理为100x100px",
        })

        // 自动保存设置
        handleSave(false, true)
      } else {
        throw new Error(data.message || "上传失败")
      }
    } catch (error) {
      console.error("上传Logo失败:", error)
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "无法上传Logo",
        variant: "destructive",
      })
    }
  }

  /**
   * 处理主题模式变更
   * @param mode 主题模式
   */
  const handleThemeModeChange = (mode: "light" | "dark" | "system") => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...(prev.theme || {}),
        mode
      }
    }))
    setTheme(mode)
  }

  /**
   * 处理主题颜色变更
   * @param color 主题颜色
   */
  const handleThemeColorChange = (color: string) => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...(prev.theme || {}),
        primaryColor: color
      }
    }))
  }

  /**
   * 处理功能开关变更
   * @param feature 功能名称
   * @param enabled 是否启用
   */
  const handleFeatureToggle = (feature: keyof SystemSettings['features'], enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      features: {
        ...(prev.features || {}),
        [feature]: enabled
      }
    }))
  }

  /**
   * 处理登录页背景图片上传
   * @param event 文件上传事件
   */
  const handleLoginBackgroundUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "格式错误",
        description: "请上传JPG、PNG、GIF或WebP格式的图片",
        variant: "destructive",
      })
      return
    }

    // 验证文件大小，限制为5MB
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "文件过大",
        description: "背景图片大小不能超过5MB",
        variant: "destructive",
      })
      return
    }

    try {
      // 显示上传中提示
      toast({
        title: "正在上传",
        description: "正在处理图片，请稍候...",
      })

      // 创建 FormData
      const formData = new FormData()
      formData.append('image', file)

      // 使用图片上传API
      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        // 更新登录页设置
        setSettings(prev => ({
          ...prev,
          loginPage: {
            ...prev.loginPage,
            backgroundImage: data.data.url
          }
        }))

        toast({
          title: "上传成功",
          description: "登录页背景图片已更新",
        })

        // 不刷新页面，但要保存设置
        const saveResponse = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...settings,
            loginPage: {
              ...settings.loginPage,
              backgroundImage: data.data.url
            }
          }),
        })

        if (!saveResponse.ok) {
          const saveError = await saveResponse.json()
          throw new Error(saveError.message || "保存设置失败")
        }
      } else {
        throw new Error(data.message || "上传失败")
      }
    } catch (error) {
      console.error("上传背景图片失败:", error)
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "无法上传背景图片",
        variant: "destructive",
      })
    }
  }

  /**
   * 处理登录页设置变更
   * @param field 字段名
   * @param value 新值
   * @param autoSave 是否自动保存，默认为否
   */
  const handleLoginPageChange = (field: string, value: any, autoSave: boolean = false) => {
    setSettings(prev => ({
      ...prev,
      loginPage: {
        ...(prev.loginPage || {}),
        [field]: value
      }
    }))

    // 如果需要自动保存，则调用保存函数
    if (autoSave) {
      // 使用setTimeout确保状态已更新
      setTimeout(() => {
        handleSave(false, true)
      }, 0)
    }
  }

  // 创建新菜单项
  const createNewItem = () => {
    if (!currentItem) return

    const path = currentItem.path || '';

    // 确保所有必要的字段都有默认值
    const newItem: MenuItem = {
      id: generateId(),
      title: currentItem.title || '',
      path: path,
      href: path, // 确保设置href
      icon: currentItem.icon || 'LayoutDashboard',
      isVisible: currentItem.isVisible ?? true,
      hasChildren: currentItem.hasChildren ?? false,
      children: []
    }

    setMenuItems(prev => [...prev, newItem])
    setNewItemDialogOpen(false)
    setCurrentItem(null)

    toast({
      title: "菜单项创建成功",
      description: `菜单项"${newItem.title}"已添加到菜单列表中。`,
    })
  }

  // 添加子菜单项
  const addChildItem = () => {
    if (!currentChild || !currentParent) return

    const path = currentChild.path || '';

    const newChild: MenuItem = {
      id: generateId(),
      title: currentChild.title || '',
      path: path,
      href: path, // 确保设置href
      icon: currentChild.icon || 'LayoutDashboard',
      isVisible: currentChild.isVisible ?? true,
      hasChildren: false,
      children: []
    }

    setMenuItems(prev =>
      prev.map(item =>
        item.id === currentParent.id
          ? {
              ...item,
              hasChildren: true,
              children: [...item.children, newChild]
            }
          : item
      )
    )

    setChildDialogOpen(false)
    setCurrentChild(null)
    setCurrentParent(null)

    toast({
      title: "子菜单项添加成功",
      description: `子菜单项"${newChild.title}"已添加到"${currentParent.title}"下。`,
    })
  }

  // 处理菜单项拖拽开始
  const handleDragStart = (e: React.DragEvent, id: string) => {
    setDraggingItemId(id)
    e.dataTransfer.setData('text/plain', id)
    // 设置拖拽时的视觉效果
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '0.4'
    }
  }

  // 处理拖拽结束
  const handleDragEnd = (e: React.DragEvent) => {
    setDraggingItemId(null)
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '1'
    }
  }

  // 处理拖拽进入目标区域
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.05)'
    }
  }

  // 处理离开目标区域
  const handleDragLeave = (e: React.DragEvent) => {
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = ''
    }
  }

  // 处理放置
  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault()
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = ''
    }

    const sourceId = e.dataTransfer.getData('text/plain')
    if (sourceId === targetId) return

    const updatedItems = [...menuItems]
    const sourceIndex = updatedItems.findIndex(item => item.id === sourceId)
    const targetIndex = updatedItems.findIndex(item => item.id === targetId)

    if (sourceIndex !== -1 && targetIndex !== -1) {
      // 获取源项目和目标项目的order值
      const sourceOrder = sourceIndex
      const targetOrder = targetIndex

      // 重新排序所有项目
      const [removed] = updatedItems.splice(sourceIndex, 1)
      updatedItems.splice(targetIndex, 0, removed)

      // 更新菜单项目
      setMenuItems(updatedItems)
    }
  }

  /**
   * 显示确认对话框
   * @param title 标题
   * @param description 描述
   * @returns 如果用户确认返回true，否则返回false
   */
  const confirmDialog = (title: string, description: string): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogContent({
        title,
        description,
        action: () => resolve(true)
      })
      setShowDialog(true)

      // 当对话框关闭时触发取消操作

      // 这里可以添加取消按钮的逻辑，但当前对话框组件可能不支持
      // 如果用户关闭对话框，默认为取消
      setTimeout(() => {
        const closeButton = document.querySelector('[data-dialog-close]')
        if (closeButton) {
          closeButton.addEventListener('click', () => resolve(false))
        }
      }, 100)
    })
  }

  /**
   * 保存菜单设置并应用
   * @param items 菜单项目数组
   */
  const saveMenuAndApply = async (items: MenuItem[]) => {
    try {
      setIsSaving(true)

      // 检查数据是否过期或即将过期
      if (isDataExpired) {
        // 显示确认对话框，但默认继续保存
        const shouldRefreshFirst = await confirmDialog(
          "菜单数据可能不是最新的",
          "您要先刷新获取最新数据，还是直接保存当前更改？"
        )

        if (shouldRefreshFirst) {
          // 如果用户选择先刷新
          const refreshSuccess = await refreshFromServer()
          if (!refreshSuccess) {
            toast({
              title: "刷新失败",
              description: "无法从服务器获取最新数据，将使用当前数据保存",
              variant: "destructive",
            })
          }
          // 注意：这里我们不需要合并本地更改，因为用户会在刷新后重新进行编辑
        }
      }

      // 将前端格式转换为数据库格式
      const dbMenus = frontendToDbMenu(items)

      // 调用API保存菜单设置（遵守NextAuth.js会话验证）
      const response = await fetch('/api/settings/menu', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 确保包含会话cookie，NextAuth.js会自动处理
        },
        body: JSON.stringify({ menuItems: dbMenus }),
      })

      if (!response.ok) {
        throw new Error('更新菜单设置失败')
      }

      const data = await response.json()

      if (data.success) {
        // 更新数据状态
        const now = Date.now()
        setIsDataFromServer(true)
        setLastUpdated(now)
        setIsDataExpired(false)

        // 保存成功后，更新localStorage，添加签名和过期时间
        // 设置为48小时过期，确保管理员有足够的时间使用缓存数据
        const signedData = signData(items, 48)
        localStorage.setItem("signedMenuItems", JSON.stringify(signedData))

        toast({
          title: "保存成功",
          description: "菜单设置已保存并应用",
          variant: "success",
        })

        // 刷新页面以应用新的菜单设置
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      } else {
        throw new Error(data.message || '更新菜单设置失败')
      }
    } catch (error) {
      console.error("保存菜单设置失败:", error)
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "无法保存菜单设置",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }


  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">系统设置</h1>
        <Button
          onClick={() => handleSave(true, true)}
          disabled={isSaving}
          className="flex items-center gap-2"
        >
          {isSaving ? (
            <>
              <Icons.spinner className="h-4 w-4 animate-spin" />
              保存中...
            </>
          ) : (
            "保存设置"
          )}
        </Button>
      </div>

      <Tabs defaultValue="basic" className="space-y-4" value={activeTab} onValueChange={(value) => {
        setActiveTab(value);       // 更新URL参数，但不刷新页面
        const url = new URL(window.location.href);
        url.searchParams.set('tab', value);
        window.history.pushState({}, '', url);
      }}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="theme">主题设置</TabsTrigger>
          <TabsTrigger value="features">功能开关</TabsTrigger>
          <TabsTrigger value="security">安全设置</TabsTrigger>
          <TabsTrigger value="loginPage">登录页设置</TabsTrigger>
          <TabsTrigger value="menu">菜单设置</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
            <Card>
              <CardHeader>
              <CardTitle>基本信息设置</CardTitle>
              <CardDescription>
                设置网站的基本信息，包括名称和Logo
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 网站名称设置 */}
                <div className="space-y-2">
                <Label htmlFor="siteName">网站名称</Label>
                <Input
                  id="siteName"
                  value={settings.siteName}
                  onChange={(e) => handleBasicInfoChange('siteName', e.target.value)}
                  placeholder="请输入网站名称"
                />
                <p className="text-sm text-gray-500">
                  网站名称将显示在浏览器标签页和系统界面中
                </p>
                </div>

              {/* Logo设置 */}
                <div className="space-y-2">
                <Label htmlFor="logo">网站Logo</Label>
                <div className="flex items-center gap-4">
                  <div className="relative h-20 w-20 border rounded-lg overflow-hidden">
                    <Image
                      src={settings.logo}
                      alt="网站Logo"
                      fill
                      className="object-contain"
                    />
                </div>
                  <div className="flex flex-col gap-2">
                        <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById('logo')?.click()}
                      className="flex items-center gap-2"
                    >
                      <Icons.upload className="h-4 w-4" />
                      上传新Logo
                    </Button>
                    <p className="text-sm text-gray-500">
                      请上传图片，将自动处理为100x100px的正方形，大小不超过2MB，支持JPG、PNG、GIF和WebP格式
                    </p>
                      </div>
                    </div>
                </div>

              {/* 页脚文本设置 */}
                <div className="space-y-2">
                <Label htmlFor="footerText">页脚文本</Label>
                <textarea
                  id="footerText"
                  value={settings.footerText || ''}
                  onChange={(e) => handleBasicInfoChange('footerText', e.target.value)}
                  placeholder="请输入页脚文本"
                  rows={3}
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                />
                <p className="text-sm text-gray-500">
                  页脚文本将显示在网站底部，支持基本的HTML标签
                </p>
                </div>

              {/* 网站元数据设置 */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">网站元数据设置</h3>
                <div className="space-y-4">
                  {/* 网站描述 */}
                  <div className="space-y-2">
                    <Label htmlFor="description">网站描述</Label>
                    <textarea
                      id="description"
                      value={settings.description || ''}
                      onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                      placeholder="请输入网站描述"
                      rows={2}
                      className="flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                    />
                    <p className="text-sm text-gray-500">网站描述将显示在搜索引擎结果中</p>
                  </div>

                  {/* 关键词 */}
                  <div className="space-y-2">
                    <Label htmlFor="keywords">关键词</Label>
                    <Input
                      id="keywords"
                      value={settings.keywords || ''}
                      onChange={(e) => handleBasicInfoChange('keywords', e.target.value)}
                      placeholder="请输入关键词，用逗号分隔"
                    />
                    <p className="text-sm text-gray-500">关键词用于搜索引擎优化，多个关键词用逗号分隔</p>
                  </div>

                  {/* 应用名称 */}
                  <div className="space-y-2">
                    <Label htmlFor="applicationName">应用名称</Label>
                    <Input
                      id="applicationName"
                      value={settings.applicationName || ''}
                      onChange={(e) => handleBasicInfoChange('applicationName', e.target.value)}
                      placeholder="请输入应用名称"
                    />
                    <p className="text-sm text-gray-500">应用名称用于浏览器标签页和移动设备上的显示</p>
                  </div>

                  {/* iOS应用名称 */}
                  <div className="space-y-2">
                    <Label htmlFor="appleMobileWebAppTitle">iOS应用名称</Label>
                    <Input
                      id="appleMobileWebAppTitle"
                      value={settings.appleMobileWebAppTitle || ''}
                      onChange={(e) => handleBasicInfoChange('appleMobileWebAppTitle', e.target.value)}
                      placeholder="请输入iOS应用名称"
                    />
                    <p className="text-sm text-gray-500">
                      当用户将网站添加到iOS设备主屏幕时显示的名称，建议使用简短易记的名称，如&quot;外呼系统&quot;
                    </p>
                  </div>
                </div>
              </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme">
            <Card>
              <CardHeader>
                <CardTitle>主题设置</CardTitle>
                <CardDescription>
                自定义系统的主题外观，包括主题模式和颜色
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 主题模式设置 */}
              <div className="space-y-2">
                <Label>主题模式</Label>
                <div className="grid grid-cols-3 gap-4">
                                        <Button
                    variant={settings.theme.mode === "light" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("light")}
                  >
                    <Icons.sun className="h-4 w-4 mr-2" />
                    明亮
                                        </Button>
                                          <Button
                    variant={settings.theme.mode === "dark" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("dark")}
                  >
                    <Icons.moon className="h-4 w-4 mr-2" />
                    暗黑
                                          </Button>
                                        <Button
                    variant={settings.theme.mode === "system" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("system")}
                  >
                    <Icons.laptop className="h-4 w-4 mr-2" />
                    跟随系统
                                        </Button>
                                      </div>
                <p className="text-sm text-gray-500">
                  选择适合您的主题模式，可以随时切换
                </p>
                      </div>

              {/* 主题颜色设置 */}
              <div className="space-y-2">
                <Label>主题颜色</Label>
                <div className="flex items-center gap-4">
                      <Input
                    type="color"
                    value={settings.theme.primaryColor}
                    onChange={(e) => handleThemeColorChange(e.target.value)}
                    className="w-20 h-10 p-1"
                  />
                      <Input
                    type="text"
                    value={settings.theme.primaryColor}
                    onChange={(e) => handleThemeColorChange(e.target.value)}
                    placeholder="#000000"
                    className="w-32"
                      />
                    </div>
                <p className="text-sm text-gray-500">
                  选择系统的主题颜色，将应用于按钮、链接等元素
                </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

        <TabsContent value="features">
            <Card>
              <CardHeader>
              <CardTitle>功能开关</CardTitle>
              <CardDescription>
                控制系统功能的开启和关闭
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 用户注册开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>用户注册</Label>
                  <p className="text-sm text-gray-500">
                    是否允许新用户注册账号
                  </p>
                    </div>
                    <Switch
                  checked={settings.features.enableRegistration}
                  onCheckedChange={(checked) => handleFeatureToggle('enableRegistration', checked)}
                    />
                  </div>

                  <Separator />

              {/* 密码重置开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>密码重置</Label>
                  <p className="text-sm text-gray-500">
                    是否允许用户重置密码
                  </p>
                    </div>
                <Switch
                  checked={settings.features.enablePasswordReset}
                  onCheckedChange={(checked) => handleFeatureToggle('enablePasswordReset', checked)}
                />
                  </div>

                  <Separator />

              {/* 系统通知开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>系统通知</Label>
                  <p className="text-sm text-gray-500">
                    是否启用系统通知功能
                    </p>
                  </div>
                <Switch
                  checked={settings.features.enableNotifications}
                  onCheckedChange={(checked) => handleFeatureToggle('enableNotifications', checked)}
                />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>
                配置系统的安全选项，包括密码策略和登录限制
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 密码最小长度 */}
                <div className="space-y-2">
                <Label htmlFor="passwordMinLength">密码最小长度</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="passwordMinLength"
                    type="number"
                    min="6"
                    max="20"
                    value={settings.security?.passwordMinLength || 8}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 6 && value <= 20) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            passwordMinLength: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">字符</span>
                </div>
                <p className="text-sm text-gray-500">
                  设置密码的最小长度要求，建议不少于8个字符
                </p>
                </div>

                <Separator />

              {/* 密码复杂度要求 */}
                <div className="space-y-4">
                <h3 className="text-sm font-medium">密码复杂度要求</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含特殊字符</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个特殊字符（如 @#$%）
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireSpecialChar || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireSpecialChar: checked
                        }
                      }))
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含数字</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个数字
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireNumber || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireNumber: checked
                        }
                      }))
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含大写字母</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个大写字母
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireUppercase || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireUppercase: checked
                        }
                      }))
                    }}
                  />
                </div>
                </div>

                <Separator />

              {/* 登录尝试限制 */}
                <div className="space-y-2">
                <Label htmlFor="loginAttempts">登录尝试限制</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="loginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    value={settings.security?.loginAttempts || 5}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 3 && value <= 10) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            loginAttempts: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">次</span>
                </div>
                <p className="text-sm text-gray-500">
                  超过指定次数的失败登录尝试后，账户将被临时锁定
                </p>
                </div>

                <Separator />

              {/* 会话超时时间 */}
                <div className="space-y-2">
                <Label htmlFor="sessionTimeout">会话超时时间</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="sessionTimeout"
                    type="number"
                    min="5"
                    max="120"
                    value={settings.security?.sessionTimeout || 30}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 5 && value <= 120) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            sessionTimeout: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">分钟</span>
                </div>
                <p className="text-sm text-gray-500">
                  用户无操作超过指定时间后，将自动退出登录
                </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="loginPage">
            <Card>
              <CardHeader>
                <CardTitle>登录页设置</CardTitle>
                <CardDescription>
                  自定义系统登录页的外观和内容
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 背景图片设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">背景图片</h3>
                  <div className="flex items-start gap-4">
                    <div className="w-1/3 aspect-video relative rounded-md overflow-hidden border">
                      <div
                        className="absolute inset-0 bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${settings.loginPage?.backgroundImage || defaultSettings.loginPage?.backgroundImage})`,
                        }}
                      />
                    </div>
                    <div className="flex-1 space-y-4">
                      <input
                        id="loginBackground"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleLoginBackgroundUpload}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('loginBackground')?.click()}
                        className="flex items-center gap-2"
                      >
                        <Icons.upload className="h-4 w-4" />
                        上传新背景
                      </Button>
                      <p className="text-sm text-gray-500">
                        建议上传宽度为1920px的图片，大小不超过5MB，支持JPG、PNG、GIF和WebP格式
                      </p>
                    </div>
                  </div>
                </div>

                {/* 背景特效说明 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">背景特效</h3>
                  <p className="text-sm text-gray-500">
                    登录页面已内置背景特效，无需额外设置。
                  </p>
                </div>

                {/* 文字内容设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">文字内容</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="loginTitle">标题</Label>
                      <Input
                        id="loginTitle"
                        value={settings.loginPage?.title || ''}
                        onChange={(e) => handleLoginPageChange('title', e.target.value)}
                        onBlur={() => handleSave(false, true)}
                        placeholder="请输入登录页标题"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="loginSubtitle">副标题</Label>
                      <Input
                        id="loginSubtitle"
                        value={settings.loginPage?.subtitle || ''}
                        onChange={(e) => handleLoginPageChange('subtitle', e.target.value)}
                        onBlur={() => handleSave(false, true)}
                        placeholder="请输入登录页副标题"
                      />
                    </div>
                  </div>
                </div>

                {/* 特性列表设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">特性列表</h3>
                  <div className="space-y-4">
                    {(settings.loginPage?.features || []).map((feature, index) => (
                      <div key={index} className="flex items-center gap-4">
                        <div className="w-1/4">
                          <Label htmlFor={`featureIcon${index}`}>图标</Label>
                          <Select
                            value={feature.icon}
                            onValueChange={(value) => {
                              const newFeatures = [...(settings.loginPage?.features || [])]
                              newFeatures[index] = { ...newFeatures[index], icon: value }
                              handleLoginPageChange('features', newFeatures, true)
                            }}
                          >
                            <SelectTrigger id={`featureIcon${index}`}>
                              <SelectValue placeholder="选择图标" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">用户</SelectItem>
                              <SelectItem value="lock">锁定</SelectItem>
                              <SelectItem value="mail">邮件</SelectItem>
                              <SelectItem value="settings">设置</SelectItem>
                              <SelectItem value="bell">通知</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex-1">
                          <Label htmlFor={`featureText${index}`}>文字</Label>
                          <Input
                            id={`featureText${index}`}
                            value={feature.text}
                            onChange={(e) => {
                              const newFeatures = [...(settings.loginPage?.features || [])]
                              newFeatures[index] = { ...newFeatures[index], text: e.target.value }
                              handleLoginPageChange('features', newFeatures)
                            }}
                            onBlur={() => handleSave(false, true)}
                            placeholder="请输入特性文字"
                          />
                        </div>

                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="self-end"
                          onClick={() => {
                            const newFeatures = [...(settings.loginPage?.features || [])]
                            newFeatures.splice(index, 1)
                            handleLoginPageChange('features', newFeatures, true)
                          }}
                        >
                          <Icons.trash className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}

                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const newFeatures = [...(settings.loginPage?.features || []), { icon: 'user', text: '新特性' }]
                        handleLoginPageChange('features', newFeatures, true)
                      }}
                    >
                      <Icons.plus className="h-4 w-4 mr-2" />
                      添加特性
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">
                    特性列表将显示在登录页左侧，建议不超过3个
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 添加菜单设置选项卡 */}
          <TabsContent value="menu">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle>菜单设置</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        // 强制从服务器刷新菜单数据
                        setIsLoading(true)
                        try {
                          await refreshFromServer()
                          toast({
                            title: "刷新成功",
                            description: "已从服务器获取最新菜单数据",
                          })
                        } catch (error) {
                          toast({
                            title: "刷新失败",
                            description: "无法从服务器获取菜单数据",
                            variant: "destructive",
                          })
                        } finally {
                          setIsLoading(false)
                        }
                      }}
                      disabled={isLoading}
                      title="从服务器刷新最新菜单数据"
                    >
                      {isLoading ? (
                        <Icons.spinner className="h-4 w-4 animate-spin" />
                      ) : (
                        <Icons.refresh className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <Button
                    onClick={() => saveMenuAndApply(menuItems)}
                    disabled={isSaving}
                    className="flex items-center gap-2"
                  >
                    {isSaving ? (
                      <>
                        <Icons.spinner className="h-4 w-4 mr-2 animate-spin" />
                        保存中...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        保存菜单设置
                      </>
                    )}
                  </Button>
                </div>
                <CardDescription>
                  自定义左侧导航菜单的显示项目
                  <span className="text-xs text-muted-foreground block mt-1">
                    {isDataFromServer ? "从服务器加载" : "从本地缓存加载"} ·
                    最后更新: {new Date(lastUpdated).toLocaleString()}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isDataExpired && (
                  <div className="bg-muted/50 text-sm p-2 rounded flex items-center justify-between mb-4">
                    <span>菜单数据可能已过期</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        setIsLoading(true)
                        try {
                          await refreshFromServer()
                          toast({
                            title: "刷新成功",
                            description: "已从服务器获取最新菜单数据",
                          })
                        } catch (error) {
                          toast({
                            title: "刷新失败",
                            description: "无法从服务器获取菜单数据",
                            variant: "destructive",
                          })
                        } finally {
                          setIsLoading(false)
                        }
                      }}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Icons.spinner className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        "刷新"
                      )}
                    </Button>
                  </div>
                )}
                <div className="space-y-6">
                  <div className="flex justify-between pb-4 border-b">
                    <h3 className="text-lg font-medium">菜单项管理</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCurrentItem({
                          title: "",
                          path: "",
                          icon: "LayoutDashboard",
                          isVisible: true,
                          hasChildren: false
                        });
                        setNewItemDialogOpen(true);
                      }}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      添加菜单项
                    </Button>
                  </div>

                  {/* 菜单项列表 */}
                  <div className="space-y-4">
                    {menuItems.map((item) => (
                      <div
                        key={item.id}
                        className="border rounded-md overflow-hidden"
                        draggable
                        onDragStart={(e) => handleDragStart(e, item.id)}
                        onDragEnd={handleDragEnd}
                        onDragOver={(e) => handleDragOver(e)}
                        onDragLeave={(e) => handleDragLeave(e)}
                        onDrop={(e) => handleDrop(e, item.id)}
                      >
                        <div className="flex items-center p-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleExpand(item.id)}
                            className="mr-2"
                          >
                            {expandedItems.includes(item.id) ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                          <div className="flex items-center gap-2 flex-1">
                            <div className="w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                              {renderIcon(item.icon, item.customIconUrl)}
                            </div>
                            <div>
                              <div className="font-medium">{item.title}</div>
                              <div className="text-sm text-muted-foreground">路径: {item.path}</div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                // 设置为当前编辑项
                                setCurrentItem({ ...item });
                                setEditDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {item.hasChildren && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCurrentParent(item);
                                  setCurrentChild({
                                    title: "",
                                    path: "",
                                    icon: "LayoutDashboard",
                                    isVisible: true,
                                    hasChildren: false
                                  });
                                  setIsEditingChild(false);
                                  setChildDialogOpen(true);
                                }}
                              >
                                <PlusCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* 子菜单项 */}
                        {item.hasChildren && item.children.length > 0 && expandedItems.includes(item.id) && (
                          <div className="px-4 py-2 space-y-2 bg-slate-50 dark:bg-slate-900">
                            {item.children.map((child) => (
                              <div key={child.id} className="flex items-center p-2 rounded-md">
                                <div className="flex items-center gap-2 flex-1">
                                  <div className="w-6 h-6 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                                    {renderIcon(child.icon, child.customIconUrl)}
                                  </div>
                                  <div>
                                    <div className="font-medium">{child.title}</div>
                                    <div className="text-xs text-muted-foreground">路径: {child.path}</div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    // 设置为当前编辑项
                                    setCurrentParent(item);
                                    setCurrentChild({ ...child });
                                    setIsEditingChild(true);
                                    setChildDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 对话框等保持不变 */}

              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 提示对话框 */}
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{dialogContent.title}</DialogTitle>
              <DialogDescription>
                {dialogContent.description}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                onClick={() => {
                  setShowDialog(false)
                  if (dialogContent.action) {
                    dialogContent.action()
                  }
                }}
              >
                确定
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 编辑菜单项对话框 */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑菜单项</DialogTitle>
              <DialogDescription>
                编辑菜单项的属性
              </DialogDescription>
            </DialogHeader>
            <div className="p-4">
              {/* 菜单项编辑表单 */}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    )
}