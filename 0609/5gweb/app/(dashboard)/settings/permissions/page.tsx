"use client"

import { useState, useEffect } from "react"
import { Plus } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTable } from "@/components/ui/data-table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import type { ResourceType, ActionType } from "@/types/role"
import { navConfig } from "@/config/nav"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, "名称至少2个字符").max(50, "名称最多50个字符"),
  code: z.string().min(2, "编码至少2个字符").max(50, "编码最多50个字符"),
  resourceType: z.string(),
  resource: z.string().min(1, "请输入资源标识"),
  action: z.string(),
  description: z.string().optional(),
  menuItems: z.array(z.string()).default([]), // 关联的菜单项
})

// 递归获取所有菜单项
const getAllMenuItems = (items: readonly any[]): string[] => {
  return items.reduce((acc: string[], item) => {
    if (item.href) {
      acc.push(item.href)
    }
    if (item.items) {
      acc = [...acc, ...getAllMenuItems(item.items)]
    }
    return acc
  }, [])
}

const allMenuItems = getAllMenuItems(navConfig.main)

// 权限管理页面
export default function PermissionsPage() {
  const [permissions, setPermissions] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [editingPermission, setEditingPermission] = useState<any>(null)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      code: "",
      resourceType: "menu",
      resource: "",
      action: "view",
      description: "",
      menuItems: [],
    }
  })

  // 加载权限列表
  const loadPermissions = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/permissions")
      const data = await response.json()

      if (data.success) {
        setPermissions(data.data)
      } else {
        toast.error("加载权限列表失败")
      }
    } catch (error) {
      console.error("加载权限列表失败:", error)
      toast.error("加载权限列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 创建或更新权限
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const url = editingPermission
        ? `/api/permissions/${editingPermission.id}`
        : "/api/permissions"

      const method = editingPermission ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(editingPermission ? "更新权限成功" : "创建权限成功")
        setShowDialog(false)
        form.reset()
        setEditingPermission(null)
        loadPermissions()
      } else {
        toast.error(data.message || (editingPermission ? "更新权限失败" : "创建权限失败"))
      }
    } catch (error) {
      console.error(editingPermission ? "更新权限失败:" : "创建权限失败:", error)
      toast.error(editingPermission ? "更新权限失败" : "创建权限失败")
    }
  }

  // 编辑权限
  const handleEdit = (permission: any) => {
    setEditingPermission(permission)
    form.reset({
      name: permission.name,
      code: permission.code,
      resourceType: permission.resourceType,
      resource: permission.resource,
      action: permission.action,
      description: permission.description,
      menuItems: permission.menuItems || [],
    })
    setShowDialog(true)
  }

  // 删除权限
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/permissions/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast.success("删除权限成功")
        loadPermissions()
      } else {
        toast.error(data.message || "删除权限失败")
      }
    } catch (error) {
      console.error("删除权限失败:", error)
      toast.error("删除权限失败")
    }
  }

  // 首次加载权限列表
  useEffect(() => {
    loadPermissions()
  }, [])

  // 表格列定义
  const columns = [
    {
      accessorKey: "name",
      header: "权限名称",
    },
    {
      accessorKey: "code",
      header: "权限编码",
    },
    {
      accessorKey: "resourceType",
      header: "资源类型",
      cell: ({ row }: { row: any }) => {
        const resourceType = row.original.resourceType
        return (
          <span className="capitalize">{resourceType}</span>
        )
      },
    },
    {
      accessorKey: "resource",
      header: "资源标识",
    },
    {
      accessorKey: "action",
      header: "操作类型",
      cell: ({ row }: { row: any }) => {
        const action = row.original.action
        return (
          <span className="capitalize">{action}</span>
        )
      },
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }: { row: any }) => {
        return (
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEdit(row.original)}
            >
              编辑
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDelete(row.original.id)}
            >
              删除
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">权限管理</h2>

        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setEditingPermission(null)
              form.reset({
                name: "",
                code: "",
                resourceType: "menu",
                resource: "",
                action: "view",
                description: "",
                menuItems: [],
              })
            }}>
              <Plus className="mr-2 h-4 w-4" />
              添加权限
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingPermission ? "编辑权限" : "添加权限"}
              </DialogTitle>
              <DialogDescription>
                {editingPermission
                  ? "编辑现有权限信息"
                  : "创建新的权限并分配给角色"}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>权限名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入权限名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>权限编码</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入权限编码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="resourceType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>资源类型</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择资源类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="menu">菜单</SelectItem>
                          <SelectItem value="api">API</SelectItem>
                          <SelectItem value="data">数据</SelectItem>
                          <SelectItem value="function">功能</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="resource"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>资源标识</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入资源标识" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="action"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>操作类型</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择操作类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="view">查看</SelectItem>
                          <SelectItem value="create">创建</SelectItem>
                          <SelectItem value="update">更新</SelectItem>
                          <SelectItem value="delete">删除</SelectItem>
                          <SelectItem value="export">导出</SelectItem>
                          <SelectItem value="import">导入</SelectItem>
                          <SelectItem value="all">所有操作</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="menuItems"
                  render={({ field }: { field: { value: string[], onChange: (value: string[]) => void } }) => (
                    <FormItem>
                      <FormLabel>关联菜单项</FormLabel>
                      <FormDescription>
                        选择此权限关联的菜单项（仅当资源类型为&quot;菜单&quot;时有效）
                      </FormDescription>
                      <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                        {allMenuItems.map((item) => (
                          <FormItem
                            key={item}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value?.includes(item)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    field.onChange([...field.value, item])
                                  } else {
                                    field.onChange(
                                      field.value?.filter(
                                        (value) => value !== item
                                      )
                                    )
                                  }
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {item}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入权限描述" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">
                    {editingPermission ? "更新" : "创建"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr>
              {columns.map((column) => (
                <th key={column.accessorKey || column.id} className="p-4 text-left font-medium">
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="p-4 text-center">
                  加载中...
                </td>
              </tr>
            ) : permissions.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="p-4 text-center">
                  暂无数据
                </td>
              </tr>
            ) : (
              permissions.map((row) => (
                <tr key={row.id}>
                  {columns.map((column) => (
                    <td key={column.accessorKey || column.id} className="p-4">
                      {column.cell ? column.cell({ row }) : row[column.accessorKey]}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
