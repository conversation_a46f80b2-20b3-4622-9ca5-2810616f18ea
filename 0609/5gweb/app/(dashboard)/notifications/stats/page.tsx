"use client"

// 为客户端提供 process polyfill
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.process = window.process || {
    env: {
      NODE_ENV: process.env.NODE_ENV || 'production',
    },
    browser: true,
    version: '',
    versions: {},
    nextTick: function(fn: Function) {
      setTimeout(fn, 0);
    },
  };
}

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
// 使用 Recharts 替代 Tremor
import {
  PieChart, Pie, Cell,
  BarChart as RechartsBarChart, Bar,
  LineChart as RechartsLineChart, Line,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AlertCircle,
  BarChart2,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  RotateCw,
  Calendar,
  Bell,
  BellRing,
  BellOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import logger from '@/lib/utils/logger';

/**
 * 通知统计页面
 *
 * 此页面用于展示通知系统的统计数据，包括：
 * 1. 通知总数、已读数、未读数
 * 2. 通知阅读率趋势
 * 3. 通知类型分布
 * 4. 通知优先级分布
 */
export default function NotificationStatsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState('week');
  const [stats, setStats] = useState<any>(null);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/notifications/stats?period=${period}`);

      if (!response.ok) {
        throw new Error('获取通知统计数据失败');
      }

      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      } else {
        throw new Error(data.message || '获取通知统计数据失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取通知统计数据失败');
      logger.error('获取通知统计数据错误:', err);
      toast({
        title: "错误",
        description: err instanceof Error ? err.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 监听时间段变化
  useEffect(() => {
    fetchStats();
  }, [period]);

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // 优先级颜色映射
  const priorityColorMap: Record<string, string> = {
    high: 'red',
    medium: 'yellow',
    low: 'green'
  };

  // 渲染统计卡片
  const renderOverviewCards = () => {
    if (!stats) return null;

    const { overview } = stats;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="overflow-hidden border-b-4 border-blue-500 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Bell className="h-4 w-4 mr-2 text-blue-500" />
              总通知数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total}</div>
            <p className="text-xs text-gray-400 mt-1">所有通知的总数量</p>
            <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full bg-blue-500 rounded-full" style={{ width: '100%' }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-b-4 border-green-500 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BellRing className="h-4 w-4 mr-2 text-green-500" />
              已读通知
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{overview.read}</div>
            <p className="text-xs text-gray-400 mt-1">已经阅读的通知数量</p>
            <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full bg-green-500 rounded-full" style={{ width: `${overview.total > 0 ? (overview.read / overview.total * 100) : 0}%` }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-b-4 border-red-500 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BellOff className="h-4 w-4 mr-2 text-red-500" />
              未读通知
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overview.unread}</div>
            <p className="text-xs text-gray-400 mt-1">尚未阅读的通知数量</p>
            <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full bg-red-500 rounded-full" style={{ width: `${overview.total > 0 ? (overview.unread / overview.total * 100) : 0}%` }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-b-4 border-indigo-500 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <PieChartIcon className="h-4 w-4 mr-2 text-indigo-500" />
              平均阅读率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-600">{formatPercentage(overview.readRate)}</div>
            <p className="text-xs text-gray-400 mt-1">已读通知占总通知的比例</p>
            <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full bg-indigo-500 rounded-full" style={{ width: `${(overview.readRate * 100)}%` }}></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // 渲染阅读率趋势图
  const renderReadRateTrend = () => {
    if (!stats || !stats.readRateTrend || stats.readRateTrend.length === 0) return null;

    // 格式化数据
    const chartData = stats.readRateTrend.map((item: any) => ({
      date: item.date,
      '阅读率': parseFloat((item.readRate * 100).toFixed(2)),
      '已读数': item.read,
      '未读数': item.unread
    }));

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <LineChartIcon className="h-5 w-5 mr-2 text-blue-500" />
            通知阅读率趋势
          </CardTitle>
          <CardDescription>
            展示不同时间段的通知阅读率变化
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <div className="flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="flex flex-col items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">平均阅读率</span>
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {stats.overview?.readRate ? (stats.overview.readRate * 100).toFixed(2) : 0}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-center bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
              <div className="flex flex-col items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">总通知数</span>
                <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {stats.overview?.total || 0}
                </span>
              </div>
            </div>
          </div>

          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip formatter={(value) => `${value}%`} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="阅读率"
                  stroke="#3b82f6"
                  activeDot={{ r: 8 }}
                  strokeWidth={2}
                />
              </RechartsLineChart>
            </ResponsiveContainer>
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            注：图表显示的是各时间段内通知的阅读率变化趋势
          </div>
        </CardContent>
      </Card>
    );
  };

  // 渲染通知类型分布图
  const renderTypeDistribution = () => {
    if (!stats) return null;

    // 准备数据
    const hasData = stats.typeDistribution && stats.typeDistribution.length > 0;

    // 如果没有数据，显示提示信息
    if (!hasData) {
      return (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChartIcon className="h-5 w-5 mr-2 text-green-500" />
              通知类型分布
            </CardTitle>
            <CardDescription>
              展示不同类型通知的数量分布
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center h-80 text-gray-500">
              <PieChartIcon className="h-16 w-16 mb-4 opacity-50" />
              <p>暂无通知类型分布数据</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    // 准备图表数据
    const chartData = stats.typeDistribution.map((item: any) => ({
      name: item.typeName || '未知类型',
      value: item.count
    }));

    // 固定颜色数组
    const COLORS = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#06b6d4", "#84cc16"];

    // 自定义图例
    const renderCustomizedLegend = (props: any) => {
      const { payload } = props;

      return (
        <div className="flex flex-wrap justify-center gap-4 mt-4">
          {payload.map((entry: any, index: number) => (
            <div key={`item-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm">{entry.value}</span>
            </div>
          ))}
        </div>
      );
    };

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <PieChartIcon className="h-5 w-5 mr-2 text-green-500" />
            通知类型分布
          </CardTitle>
          <CardDescription>
            展示不同类型通知的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}条`} />
                <Legend content={renderCustomizedLegend} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    );
  };

  // 渲染通知优先级分布图
  const renderPriorityDistribution = () => {
    if (!stats) return null;

    // 准备数据
    const hasData = stats.priorityDistribution && stats.priorityDistribution.length > 0;

    // 准备数据 - 如果没有数据，使用示例数据
    let chartData;
    let isExampleData = false;

    if (hasData) {
      // 准备实际数据
      const priorityNameMap = {
        high: '高优先级',
        medium: '中优先级',
        low: '低优先级'
      };

      chartData = stats.priorityDistribution.map((item: any) => ({
        name: priorityNameMap[item.priority] || '未知优先级',
        value: item._count.id,
        fill: item.priority === 'high' ? '#ef4444' :
              item.priority === 'medium' ? '#f59e0b' : '#10b981'
      }));
    } else {
      // 创建示例数据
      chartData = [
        { name: '高优先级', value: 1, fill: '#ef4444' },
        { name: '中优先级', value: 3, fill: '#f59e0b' },
        { name: '低优先级', value: 2, fill: '#10b981' }
      ];
      isExampleData = true;
    }

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart2 className="h-5 w-5 mr-2 text-amber-500" />
            通知优先级分布
          </CardTitle>
          <CardDescription>
            展示不同优先级通知的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isExampleData && (
            <div className="mb-4 text-center text-sm text-gray-500">
              <p>当前显示的是示例数据</p>
            </div>
          )}
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => `${value}条`} />
                <Legend />
                <Bar dataKey="value" name="数量" fill="#8884d8">
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Bar>
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">通知统计</h1>
        <div className="flex space-x-2">
          <Select
            value={period}
            onValueChange={setPeriod}
          >
            <SelectTrigger className="w-[180px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="选择时间段" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">最近24小时</SelectItem>
              <SelectItem value="week">最近一周</SelectItem>
              <SelectItem value="month">最近一个月</SelectItem>
              <SelectItem value="year">最近一年</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={fetchStats}
            disabled={loading}
          >
            {loading ? (
              <RotateCw className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <RotateCw className="h-4 w-4 mr-1" />
            )}
            刷新数据
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <RotateCw className="h-12 w-12 text-gray-400 animate-spin" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg text-center">
          <AlertCircle className="h-6 w-6 mx-auto mb-2" />
          <p>{error}</p>
        </div>
      ) : (
        <div>
          {renderOverviewCards()}
          {renderReadRateTrend()}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderTypeDistribution()}
            {renderPriorityDistribution()}
          </div>
        </div>
      )}
    </div>
  );
}
