export default function NotificationDetailLoading() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <div className="h-8 w-48 bg-muted rounded animate-pulse mb-2"></div>
        <div className="h-4 w-96 bg-muted rounded animate-pulse"></div>
      </div>

      <div className="border rounded-lg p-6">
        <div className="h-6 w-32 bg-muted rounded animate-pulse mb-4"></div>
        <div className="h-4 w-full bg-muted rounded animate-pulse mb-2"></div>
        <div className="h-4 w-3/4 bg-muted rounded animate-pulse mb-2"></div>
        <div className="h-4 w-1/2 bg-muted rounded animate-pulse"></div>
      </div>
    </div>
  )
}

