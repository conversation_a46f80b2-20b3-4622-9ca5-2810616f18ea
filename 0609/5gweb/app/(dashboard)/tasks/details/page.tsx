"use client"

import logger from '@/lib/utils/logger';

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { formatToChineseDateTime } from "@/lib/utils/date-formatter"
import { CalendarIcon, Download, Search, FileText, RefreshCw, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { toast } from "@/components/ui/use-toast"
import { EmptyState, ErrorState } from "@/components/shared"
import { videoCallService } from "@/lib/api/video-call-service"
import { useSession } from "next-auth/react"

export default function TaskDetailsPage() {
  // 获取当前用户会话
  const { data: session } = useSession()

  // 获取URL参数中的taskId
  const [taskId, setTaskId] = useState<string | null>(null)

  // 在客户端获取URL参数
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const id = urlParams.get('taskId')
      if (id) {
        setTaskId(id)
      }
    }
  }, [])

  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [phoneNumber, setPhoneNumber] = useState("")
  const [connectionType, setConnectionType] = useState("")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // 外呼详情状态
  const [callDetailsList, setCallDetailsList] = useState<any[]>([])
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  const [isFirstLoad, setIsFirstLoad] = useState(true)

  // 页面加载时获取外呼详情
  useEffect(() => {
    fetchCallDetails()
  }, [currentPage, session, taskId])

  // 获取外呼详情
  const fetchCallDetails = async () => {
    setIsLoading(true)
    setLoadError(null)

    try {
      // 检查用户角色
      const isAdmin = session?.user?.role === "super" || session?.user?.role === "admin";

      // 构建查询参数
      const params: any = {
        page: currentPage,
        pageSize: itemsPerPage
      }

      // 非管理员只能查看自己的任务
      if (!isAdmin) {
        params.userId = session?.user?.id;
      }

      // 如果有taskId参数，优先使用它
      if (taskId) {
        params.taskId = taskId;
      }

      // 添加筛选条件
      if (taskName) params.taskName = taskName
      if (content && content !== "all") params.content = content
      if (type && type !== "all") params.type = type
      if (startDateRange.from) params.startDate = format(startDateRange.from, "yyyy-MM-dd")
      if (startDateRange.to) params.endDate = format(startDateRange.to, "yyyy-MM-dd")
      if (phoneNumber) params.phone = phoneNumber
      if (connectionType && connectionType !== "all") params.connectionType = connectionType

      // 调用API获取外呼详情
      const response = await videoCallService.getCallDetails(params)

      if (response.success) {
        setCallDetailsList(response.data.list || [])
        setTotalItems(response.data.total || 0)
        setTotalPages(response.data.totalPages || 1)
        // 成功获取数据后清除错误状态
        setLoadError(null)
      } else {
        // 设置错误状态但不显示错误提示
        setLoadError("获取数据失败")
        // 只有在非首次加载时才记录到控制台，不显示给用户
        if (!isFirstLoad) {
          logger.error("获取外呼详情失败:", response.message)
        }
      }
    } catch (error) {
      // 设置错误状态但不显示错误提示
      setLoadError("获取数据失败")
      // 只有在非首次加载时才记录到控制台，不显示给用户
      if (!isFirstLoad) {
        console.log("获取外呼详情错误:", error)
      }
    } finally {
      setIsLoading(false)
      setIsFirstLoad(false)
    }
  }

  // 获取当前页的数据
  const currentDetails = callDetailsList

  // 导出外呼详情为CSV或Excel
  const exportToCSV = (format = "csv") => {
    if (callDetailsList.length === 0) {
      toast({
        title: "导出失败",
        description: "没有可导出的数据",
        variant: "destructive",
      })
      return
    }

    const headers = [
      "外呼任务名称",
      "外呼类型",
      "视频外呼内容",
      "客户名称",
      "被叫号码",
      "接通类型",
      "通话开始时间",
      "通话结束时间",
      "通话时长",
      "通话振铃时间",
      "意向程度",
    ]

    const csvData = [
      headers.join(","),
      ...callDetailsList.map((detail) =>
        [
          detail.taskName || "",
          detail.type || "",
          detail.content || "",
          detail.customerName || "",
          detail.phoneNumber || "",
          detail.connectionType || "",
          detail.startTime || "",
          detail.endTime || "",
          detail.duration || "",
          detail.ringTime || "",
          detail.intention || "",
        ].join(","),
      ),
    ].join("\n")

    try {
      const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.setAttribute("href", url)
      link.setAttribute("download", `外呼详情_${new Date().toISOString().split("T")[0]}.${format}`)
      link.style.visibility = "hidden"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "导出成功",
        description: `已成功导出 ${callDetailsList.length} 条记录`,
        variant: "success",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "导出过程中发生错误",
        variant: "destructive",
      })
    }
  }

  // 获取意向程度颜色
  const getIntentionColor = (intention: string) => {
    switch (intention) {
      case "A":
        return "bg-green-100 text-green-800"
      case "B":
        return "bg-blue-100 text-blue-800"
      case "C":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1) // 重置到第一页
    setIsFirstLoad(true) // 重置首次加载标志，避免显示错误提示
    fetchCallDetails()
  }

  return (
    <>
      <DashboardShell>
        <DashboardHeader
          heading={taskId ? `任务 ${taskName || taskId} 的外呼详情` : "外呼详情"}
          text="查看外呼任务的详细通话记录"
        />
        <Card>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音通话">5G语音通话</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => setStartDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => setCreateDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone-number">用户号码</Label>
                <Input
                  id="phone-number"
                  placeholder="输入用户号码"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="connection-type">接通类型</Label>
                <Select value={connectionType} onValueChange={setConnectionType}>
                  <SelectTrigger id="connection-type">
                    <SelectValue placeholder="选择接通类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="视频接通">视频接通</SelectItem>
                    <SelectItem value="语音接通">语音接通</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                查询
              </Button>
              <div className="relative">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button className="bg-green-600 hover:bg-green-700">
                      <Download className="mr-2 h-4 w-4" />
                      导出表格
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48">
                    <div className="flex flex-col space-y-2">
                      <Button variant="ghost" className="justify-start" onClick={() => exportToCSV("csv")}>
                        导出为 CSV
                      </Button>
                      <Button variant="ghost" className="justify-start" onClick={() => exportToCSV("xlsx")}>
                        导出为 Excel
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 修改表格部分，使用增强的表格样式 */}
        <div className="rounded-md border mt-6">
          <Table className="table-enhanced">
            <TableHeader>
              <TableRow>
                <TableHead>外呼任务名称</TableHead>
                <TableHead>外呼类型</TableHead>
                <TableHead>视频外呼内容</TableHead>
                <TableHead>客户名称</TableHead>
                <TableHead>被叫号码</TableHead>
                <TableHead>接通类型</TableHead>
                <TableHead>通话开始时间</TableHead>
                <TableHead>通话结束时间</TableHead>
                <TableHead>通话时长</TableHead>
                <TableHead>通话振铃时间</TableHead>
                <TableHead>意向程度</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={11} className="h-24 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : loadError && !isFirstLoad ? (
                <TableRow>
                  <TableCell colSpan={11} className="h-64 text-center">
                    <EmptyState
                      icon={<FileText />}
                      title="暂无外呼详情"
                      description="没有找到符合条件的外呼记录，您可以尝试修改搜索条件。"
                      action={
                        <div className="flex gap-2">
                          <Button onClick={() => fetchCallDetails()} variant="outline">
                            <RefreshCw className="mr-2 h-4 w-4" />
                            重新加载
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              // 重置所有筛选条件
                              setTaskName("");
                              setContent("");
                              setType("");
                              setStartDateRange({ from: undefined, to: undefined });
                              setCreateDateRange({ from: undefined, to: undefined });
                              setPhoneNumber("");
                              setConnectionType("");
                              // 然后重新加载
                              setIsFirstLoad(true);
                              fetchCallDetails();
                            }}
                          >
                            清除筛选条件
                          </Button>
                        </div>
                      }
                      size="lg"
                    />
                  </TableCell>
                </TableRow>
              ) : currentDetails.length > 0 ? (
                currentDetails.map((detail) => (
                  <TableRow key={detail.id}>
                    <TableCell className="font-medium">{detail.taskName}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex px-2 py-1 rounded-full text-xs ${
                          detail.type === "5G视频通知"
                            ? "bg-green-100 text-green-800"
                            : detail.type === "5G视频互动"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {detail.type}
                      </span>
                    </TableCell>
                    <TableCell>{detail.content}</TableCell>
                    <TableCell className="font-medium">{detail.customerName}</TableCell>
                    <TableCell>{detail.phoneNumber}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex px-2 py-1 rounded-full text-xs ${
                          detail.connectionType === "视频接通"
                            ? "bg-green-100 text-green-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {detail.connectionType}
                      </span>
                    </TableCell>
                    <TableCell>{typeof detail.startTime === 'string' ? formatToChineseDateTime(detail.startTime) : formatToChineseDateTime(detail.startTime)}</TableCell>
                    <TableCell>{typeof detail.endTime === 'string' ? formatToChineseDateTime(detail.endTime) : formatToChineseDateTime(detail.endTime)}</TableCell>
                    <TableCell className="font-medium text-blue-600">{detail.duration}</TableCell>
                    <TableCell>{detail.ringTime}</TableCell>
                    <TableCell>
                      <span
                        className={
                          detail.intention === "A"
                            ? "status-active"
                            : detail.intention === "B"
                              ? "status-processing"
                              : "status-pending"
                        }
                      >
                        {detail.intention}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={11} className="h-64 text-center">
                    <EmptyState
                      icon={<FileText />}
                      title="暂无外呼详情"
                      description="没有找到符合条件的外呼记录，您可以尝试修改搜索条件。"
                      size="lg"
                    />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页控件 */}
        {totalItems > 0 && (
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              共 {totalItems} 条记录，第 {currentPage} / {totalPages} 页
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (currentPage > 1) {
                        setCurrentPage(currentPage - 1)
                      }
                    }}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    disabled={isLoading}
                  />
                </PaginationItem>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // 显示当前页附近的页码
                  let pageToShow = i + 1;
                  if (totalPages > 5) {
                    if (currentPage > 3) {
                      pageToShow = currentPage - 3 + i;
                    }
                    if (currentPage > totalPages - 2) {
                      pageToShow = totalPages - 4 + i;
                    }
                  }
                  return (
                    <PaginationItem key={pageToShow}>
                      <PaginationLink
                        onClick={() => setCurrentPage(pageToShow)}
                        isActive={currentPage === pageToShow}
                        className="cursor-pointer"
                        disabled={isLoading}
                      >
                        {pageToShow}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (currentPage < totalPages) {
                        setCurrentPage(currentPage + 1)
                      }
                    }}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    disabled={isLoading}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>
    </>
  )
}