"use client"

import logger from '@/lib/utils/logger';

// 导入 process polyfill，确保 process 对象在客户端可用
import { ensureProcessPolyfill } from "@/app/process-polyfill"

import type React from "react"

// 确保 polyfill 被加载
ensureProcessPolyfill();

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { EmptyState, ErrorState } from "@/components/shared"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon, Play, Pause, Info, Trash2, Upload, X, Download, FileText, RefreshCw, AlertCircle, Loader2 } from "lucide-react"
import { formatToChineseDateTime } from "@/lib/utils/date-formatter"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "@/components/ui/use-toast"
import { ToastAction } from "@/components/ui/toast"
import { useAuth } from "@/contexts/auth-context"
import { getTasks } from "@/lib/api/services"
import type { Task } from "@/lib/api/interfaces"
import { videoCallService } from "@/lib/api/video-call-service"
import { FriendlyErrorAlert } from "@/app/components/friendly-error-alert"

// 如果需要扩展 Task 接口，可以这样做：
interface ExtendedTask extends Task {
  // 前端状态字段，用于UI显示
  uiStatus?: "pending" | "running" | "paused" | "completed"
  createdAt?: string | Date
  externalId?: string // 外部系统的任务ID
}

// 2. Add router import at the top of the file
// Add this import near the other imports
import { useRouter } from "next/navigation"

// 引入 startTask 和 pauseTask API
import { startTask, pauseTask, getBotList, getTaskById } from "@/lib/api/video-call-service"
// 引入短信模板服务
import { smsTemplateService, type SmsTemplate } from "@/lib/api/sms-template-service"

export default function TasksPage() {
  // 用户角色状态
  const { user } = useAuth()
  const userType = user?.data?.roleCode?.toLowerCase() === "super" ? "admin" : "customer"

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  const [isFirstLoad, setIsFirstLoad] = useState(true)

  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // 对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)

  // 新建任务表单状态
  // 获取当前时间
  const now = new Date()
  const currentHour = String(now.getHours()).padStart(2, '0')
  const currentMinute = String(now.getMinutes()).padStart(2, '0')
  const currentSecond = String(now.getSeconds()).padStart(2, '0')

  const [newTaskName, setNewTaskName] = useState("")
  const [newTaskContent, setNewTaskContent] = useState("")
  const [newTaskType, setNewTaskType] = useState("")
  const [newTaskStartTime, setNewTaskStartTime] = useState<Date | undefined>(now) // 默认为当前时间
  const [newTaskResource, setNewTaskResource] = useState("")
  const [newTaskPhoneNumber, setNewTaskPhoneNumber] = useState("")
  const [newTaskSmsType, setNewTaskSmsType] = useState("")
  const [newTaskSmsTemplate, setNewTaskSmsTemplate] = useState("")
  const [newTaskSmsPhase, setNewTaskSmsPhase] = useState("1") // 短信发送时机，默认为拨打电话时
  const [newTaskCalloutMode, setNewTaskCalloutMode] = useState("1") // 起呼方式，默认为双向视频起呼
  const [showDateTimePicker, setShowDateTimePicker] = useState(false)
  const [selectedHour, setSelectedHour] = useState(currentHour) // 默认为当前小时
  const [selectedMinute, setSelectedMinute] = useState(currentMinute) // 默认为当前分钟
  const [selectedSecond, setSelectedSecond] = useState(currentSecond) // 默认为当前秒
  const [activeTab, setActiveTab] = useState("manual")

  // 苹果手机分流相关状态
  const [isVoiceDivide, setIsVoiceDivide] = useState(false)
  const [shuntAudioBotId, setShuntAudioBotId] = useState("")
  const [audioBotId, setAudioBotId] = useState("")

  // 闪信相关状态
  const [sendFlashMessage, setSendFlashMessage] = useState(false)
  const [flashMessageTemplateId, setFlashMessageTemplateId] = useState("")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [tasks, setTasks] = useState<ExtendedTask[]>([])
  const [totalTasks, setTotalTasks] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // Add a function to fetch Bot list for resources
  const [botList, setBotList] = useState<any[]>([])
  const [botListError, setBotListError] = useState<string | null>(null)

  // 短信模板状态
  const [textSmsTemplates, setTextSmsTemplates] = useState<SmsTemplate[]>([])
  const [videoSmsTemplates, setVideoSmsTemplates] = useState<SmsTemplate[]>([])
  const [flashSmsTemplates, setFlashSmsTemplates] = useState<SmsTemplate[]>([])
  const [loadingTemplates, setLoadingTemplates] = useState(false)

  // 3. Add router initialization in the component
  // Add this near the other state declarations
  const router = useRouter()

  /**
   * 获取Bot列表
   */
  const fetchBotList = async () => {
    try {
      // 清除之前的错误
      setBotListError(null);
      const response = await getBotList();

      if (response.success) {
        setBotList(response.data);
      } else {
        logger.error("获取Bot列表失败:", response.message);
        // 设置友好的错误信息，而不是显示红色错误提示
        setBotListError("暂时无法获取Bot列表，系统将使用已有数据");
      }
    } catch (error) {
      logger.error("获取Bot列表错误:", error);
      // 设置友好的错误信息，而不是显示红色错误提示
      setBotListError("暂时无法获取Bot列表，系统将使用已有数据");
    }
  };

  /**
   * 页面加载时获取任务列表
   */
  /**
   * 获取任务列表
   * 从API获取任务数据，包括历史任务
   */
  const fetchTasks = async () => {
    setIsLoading(true);
    setLoadError(null);

    try {
      logger.log("开始获取任务数据...");

      // 直接获取所有任务数据，包括历史任务
      try {
        // 构建查询参数
        const params = new URLSearchParams();
        params.append('type', 'historical'); // 指定获取历史任务

        // 获取所有任务数据
        const allTasksResponse = await fetch(`/api/tasks?${params.toString()}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          cache: "no-store" // 禁用缓存，确保每次都获取最新数据
        });

        const allTasksResult = await allTasksResponse.json();
        logger.log("API响应:", allTasksResult);

        if (allTasksResult.success && Array.isArray(allTasksResult.data?.list)) {
          logger.log(`成功获取 ${allTasksResult.data.list.length} 条任务数据`);

          // 转换任务数据，添加UI状态
          const allTasksData = allTasksResult.data.list.map(task => ({
            ...task,
            uiStatus: task.status === "已完成" ? "completed" :
                      task.status === "已暂停" ? "paused" :
                      task.status === "外呼中" ? "running" : "pending",
            // 确保创建人字段存在，如果不存在则设置为管理员
            creator: task.creator || (user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin" ? "管理员" : "系统用户")
          }));

          // 应用过滤条件
          let filteredTasks = [...allTasksData];

          if (taskName || (content && content !== 'all') || (type && type !== 'all') ||
              startDateRange.from || startDateRange.to || createDateRange.from || createDateRange.to) {

            filteredTasks = allTasksData.filter(task => {
              // 如果有任务名称搜索条件
              if (taskName && !task.name.includes(taskName)) {
                return false;
              }

              // 如果有内容搜索条件
              if (content && content !== 'all' && task.content !== content) {
                return false;
              }

              // 如果有类型搜索条件
              if (type && type !== 'all' && task.type !== type) {
                return false;
              }

              // 如果有开始时间范围搜索条件
              if (startDateRange.from || startDateRange.to) {
                const taskStartTime = new Date(task.startTime);
                if (startDateRange.from && taskStartTime < startDateRange.from) {
                  return false;
                }
                if (startDateRange.to && taskStartTime > startDateRange.to) {
                  return false;
                }
              }

              // 如果有创建时间范围搜索条件
              if (createDateRange.from || createDateRange.to) {
                const taskCreateTime = new Date(task.importTime || task.createdAt || 0);
                if (createDateRange.from && taskCreateTime < createDateRange.from) {
                  return false;
                }
                if (createDateRange.to && taskCreateTime > createDateRange.to) {
                  return false;
                }
              }

              // 通过所有过滤条件
              return true;
            });
          }

          // 按创建时间或导入时间排序（最新的在前面）
          filteredTasks.sort((a, b) => {
            const dateA = new Date(a.importTime || a.createdAt || 0);
            const dateB = new Date(b.importTime || b.createdAt || 0);
            return dateB.getTime() - dateA.getTime();
          });

          logger.log(`过滤后的任务总数: ${filteredTasks.length}`);

          // 计算分页
          const totalItems = filteredTasks.length;
          const totalPages = Math.ceil(totalItems / itemsPerPage);
          const startIndex = (currentPage - 1) * itemsPerPage;
          const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
          const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

          logger.log(`当前页任务数: ${paginatedTasks.length}, 页码: ${currentPage}/${totalPages}`);

          // 设置任务数据
          setTasks(paginatedTasks as ExtendedTask[]);
          setTotalTasks(totalItems);
          setTotalPages(totalPages);
          setIsFirstLoad(false);

          // 启动任务状态检查
          startTaskStatusCheck();

          // 如果获取到了数据，直接返回
          if (paginatedTasks.length > 0) {
            setIsLoading(false);
            return;
          } else {
            logger.log("分页后没有任务数据");
          }
        } else {
          logger.error("获取任务数据失败:", allTasksResult.message || "未知错误");
        }
      } catch (error) {
        logger.error("获取任务数据错误:", error);
      }

      // 如果上面的方法没有获取到数据，尝试使用常规API
      logger.log("尝试使用常规API获取任务数据...");

      const response = await getTasks({
        page: currentPage,
        pageSize: itemsPerPage,
        search: taskName || undefined,
        type: type === "all" ? undefined : type,
        startDate: startDateRange.from ? format(startDateRange.from, "yyyy-MM-dd") : undefined,
        endDate: startDateRange.to ? format(startDateRange.to, "yyyy-MM-dd") : undefined,
      });

      if (response.success) {
        // 获取API返回的任务
        const regularTasks = response.data?.list || [];
        logger.log(`常规API返回 ${regularTasks.length} 条任务数据`);

        if (regularTasks.length > 0) {
          // 确保所有任务都有创建人字段
          const tasksWithCreator = regularTasks.map(task => ({
            ...task,
            uiStatus: task.status === "已完成" ? "completed" :
                      task.status === "已暂停" ? "paused" :
                      task.status === "外呼中" ? "running" : "pending",
            creator: task.creator || (user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin" ? "管理员" : "系统用户")
          }));

          // 设置任务数据
          setTasks(tasksWithCreator as ExtendedTask[]);
          setTotalTasks(response.data?.total || tasksWithCreator.length);
          setTotalPages(response.data?.totalPages || Math.ceil(tasksWithCreator.length / itemsPerPage));
          setIsFirstLoad(false);
        } else {
          logger.log("常规API没有返回任务数据");

          // 如果没有数据，尝试获取远程历史数据
          await fetchRemoteHistoricalTasks();
        }
      } else {
        logger.error("常规API获取任务失败:", response.message);

        // 如果API调用失败，尝试获取远程历史数据
        await fetchRemoteHistoricalTasks();

        // 只有在非首次加载或有搜索条件时才显示错误提示
        if (!isFirstLoad || taskName || content !== "" || type !== "" ||
            startDateRange.from || startDateRange.to ||
            createDateRange.from || createDateRange.to) {
          toast({
            title: "获取任务失败",
            description: response.message || "无法获取任务数据，请稍后重试",
            variant: "destructive",
          });
        }
        setLoadError(response.message || "无法获取任务数据");
      }
    } catch (error) {
      logger.error("获取任务数据出错:", error);

      // 尝试获取远程历史数据
      await fetchRemoteHistoricalTasks();

      // 只有在非首次加载或有搜索条件时才显示错误提示
      if (!isFirstLoad || taskName || content !== "" || type !== "" ||
          startDateRange.from || startDateRange.to ||
          createDateRange.from || createDateRange.to) {
        toast({
          title: "获取任务失败",
          description: "无法获取任务数据，请稍后重试",
          variant: "destructive",
        });
      }
      setLoadError("无法获取任务数据");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 获取远程历史任务数据
   * 从远程API获取历史任务数据
   */
  const fetchRemoteHistoricalTasks = async () => {
    logger.log("尝试获取远程历史任务数据...");
    try {
      // 调用远程API获取历史任务数据
      const response = await fetch('/api/remote-tasks', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success && Array.isArray(result.data?.list)) {
        logger.log(`成功获取 ${result.data.list.length} 条远程历史任务数据`);

        // 转换任务数据，添加UI状态
        const remoteTasks = result.data.list.map(task => ({
          ...task,
          uiStatus: task.status === "已完成" ? "completed" :
                    task.status === "已暂停" ? "paused" :
                    task.status === "外呼中" ? "running" : "pending",
          creator: task.creator || (user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin" ? "管理员" : "系统用户")
        }));

        // 设置任务数据
        setTasks(remoteTasks as ExtendedTask[]);
        setTotalTasks(remoteTasks.length);
        setTotalPages(Math.ceil(remoteTasks.length / itemsPerPage));
        setIsFirstLoad(false);

        return true;
      } else {
        logger.error("获取远程历史任务数据失败:", result.message || "未知错误");
        return false;
      }
    } catch (error) {
      logger.error("获取远程历史任务数据错误:", error);
      return false;
    }
  };

  // 初始化时获取Bot列表和短信模板
  useEffect(() => {
    fetchBotList()
    fetchSmsTemplates()

    // 获取真实任务数据
    fetchTasks()

    // 组件卸载时清理定时器
    return () => {
      if (taskStatusCheckInterval) {
        clearInterval(taskStatusCheckInterval);
        taskStatusCheckInterval = null;
      }
    };
  }, [])

  // 当页码变化时获取新页数据
  useEffect(() => {
    // 避免初始加载时重复获取
    if (currentPage !== 1) {
      fetchTasks()
    }
  }, [currentPage])

  /**
   * 获取单个任务的最新状态
   * @param taskId 任务ID
   */
  const fetchTaskStatus = async (taskId: string) => {
    try {
      // 调用API获取任务详情
      const response = await getTaskById(taskId);

      if (response.success) {
        // 更新任务状态
        const taskData = response.data;

        // 更新任务列表中的对应任务
        setTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  progress: taskData.progress,
                  uiStatus: taskData.status === "completed" ? "completed" :
                           taskData.status === "paused" ? "paused" : "running",
                  completionTime: taskData.completionTime
                }
              : task
          )
        );

        return taskData;
      }
    } catch (error) {
      // 错误处理
      toast({
        title: "获取任务状态失败",
        description: "无法获取最新任务状态，请刷新页面重试",
        variant: "destructive",
      });
    }
    return null;
  };

  // 定义全局变量
  let taskStatusCheckInterval: NodeJS.Timeout | null = null;

  /**
   * 启动任务状态检查
   * 开始定期检查任务状态
   */
  const startTaskStatusCheck = () => {
    // 如果已经有定时器在运行，先清除它
    if (taskStatusCheckInterval) {
      clearInterval(taskStatusCheckInterval);
      taskStatusCheckInterval = null;
    }

    // 创建新的定时器，每30秒检查一次任务状态
    taskStatusCheckInterval = setInterval(() => {
      // 检查是否有任务需要更新状态
      const pendingTasks = tasks.filter(task =>
        task.status === "待启动" || task.status === "外呼中" || !task.completionTime
      );

      if (pendingTasks.length > 0) {
        logger.log(`检查 ${pendingTasks.length} 个任务的状态...`);
        // 刷新任务列表，获取最新状态
        fetchTasks();
      } else {
        // 如果没有需要更新的任务，停止检查
        if (taskStatusCheckInterval) {
          clearInterval(taskStatusCheckInterval);
          taskStatusCheckInterval = null;
        }
      }
    }, 30000); // 每30秒检查一次

    logger.log("已启动任务状态检查");
  };

  /**
   * 定期轮询获取任务进度
   * 每5秒更新一次任务状态和进度
   */
  useEffect(() => {
    // 检查是否有任务
    if (!tasks || tasks.length === 0) {
      return;
    }

    // 只有当有正在运行的任务时才启动轮询
    const runningTasks = tasks.filter(task => task.uiStatus === "running");
    if (runningTasks.length === 0) {
      return;
    }

    // 创建轮询定时器
    const intervalId = setInterval(async () => {
      try {
        // 获取所有运行中任务的ID
        const runningTaskIds = tasks
          .filter(task => task.uiStatus === "running")
          .map(task => task.id);

        if (runningTaskIds.length === 0) {
          // 如果没有运行中的任务，清除定时器
          clearInterval(intervalId);
          return;
        }

        // 对于每个运行中的任务，获取其最新状态
        for (const taskId of runningTaskIds) {
          await fetchTaskStatus(taskId);
        }
      } catch (error) {
        // 错误处理
      }
    }, 5000); // 每5秒更新一次

    // 清理函数 - 当组件卸载或依赖项变化时执行
    return () => {
      clearInterval(intervalId);
    };
  }, [tasks]); // 依赖于tasks数组，当tasks变化时重新设置定时器

  /**
   * 获取短信模板
   */
  const fetchSmsTemplates = async () => {
    setLoadingTemplates(true)
    logger.log("开始获取短信模板...");

    // 设置默认空数组，确保即使API失败也有默认值
    setTextSmsTemplates([]);
    setVideoSmsTemplates([]);
    setFlashSmsTemplates([]);

    try {
      // 获取文本短信模板
      logger.log("获取文本短信模板...");
      try {
        const textResponse = await smsTemplateService.getTemplates({
          type: 'text',
          isActive: true,
          pageSize: 100,
        });

        logger.log("文本短信模板响应:", textResponse);

        // 处理文本短信模板响应
        if (textResponse) {
          try {
            // 尝试处理不同格式的响应
            if (Array.isArray(textResponse)) {
              setTextSmsTemplates(textResponse);
              logger.log(`成功获取 ${textResponse.length} 个文本短信模板`);
            } else if (textResponse.items) {
              setTextSmsTemplates(textResponse.items);
              logger.log(`成功获取 ${textResponse.items.length} 个文本短信模板`);
            } else if (textResponse.data && Array.isArray(textResponse.data)) {
              setTextSmsTemplates(textResponse.data);
              logger.log(`成功获取 ${textResponse.data.length} 个文本短信模板`);
            } else if (textResponse.data && textResponse.data.items) {
              setTextSmsTemplates(textResponse.data.items);
              logger.log(`成功获取 ${textResponse.data.items.length} 个文本短信模板`);
            } else {
              logger.error("无法解析文本短信模板响应:", textResponse);
            }
          } catch (error) {
            logger.error("处理文本短信模板响应出错:", error);
          }
        }
      } catch (textError) {
        logger.error("获取文本短信模板失败:", textError);
      }

      // 获取视频短信模板
      logger.log("获取视频短信模板...");
      try {
        const videoResponse = await smsTemplateService.getTemplates({
          type: 'video',
          isActive: true,
          pageSize: 100,
        });

        logger.log("视频短信模板响应:", videoResponse);

        // 处理视频短信模板响应
        if (videoResponse) {
          try {
            // 尝试处理不同格式的响应
            if (Array.isArray(videoResponse)) {
              setVideoSmsTemplates(videoResponse);
              logger.log(`成功获取 ${videoResponse.length} 个视频短信模板`);
            } else if (videoResponse.items) {
              setVideoSmsTemplates(videoResponse.items);
              logger.log(`成功获取 ${videoResponse.items.length} 个视频短信模板`);
            } else if (videoResponse.data && Array.isArray(videoResponse.data)) {
              setVideoSmsTemplates(videoResponse.data);
              logger.log(`成功获取 ${videoResponse.data.length} 个视频短信模板`);
            } else if (videoResponse.data && videoResponse.data.items) {
              setVideoSmsTemplates(videoResponse.data.items);
              logger.log(`成功获取 ${videoResponse.data.items.length} 个视频短信模板`);
            } else {
              logger.error("无法解析视频短信模板响应:", videoResponse);
            }
          } catch (error) {
            logger.error("处理视频短信模板响应出错:", error);
          }
        }
      } catch (videoError) {
        logger.error("获取视频短信模板失败:", videoError);
      }

      // 获取闪信模板
      logger.log("获取闪信模板...");
      try {
        const flashResponse = await smsTemplateService.getTemplates({
          type: 'flash',
          isActive: true,
          pageSize: 100,
        });

        logger.log("闪信模板响应:", flashResponse);

        // 处理闪信模板响应
        if (flashResponse) {
          try {
            // 尝试处理不同格式的响应
            if (Array.isArray(flashResponse)) {
              setFlashSmsTemplates(flashResponse);
              logger.log(`成功获取 ${flashResponse.length} 个闪信模板`);
            } else if (flashResponse.items) {
              setFlashSmsTemplates(flashResponse.items);
              logger.log(`成功获取 ${flashResponse.items.length} 个闪信模板`);
            } else if (flashResponse.data && Array.isArray(flashResponse.data)) {
              setFlashSmsTemplates(flashResponse.data);
              logger.log(`成功获取 ${flashResponse.data.length} 个闪信模板`);
            } else if (flashResponse.data && flashResponse.data.items) {
              setFlashSmsTemplates(flashResponse.data.items);
              logger.log(`成功获取 ${flashResponse.data.items.length} 个闪信模板`);
            } else {
              logger.error("无法解析闪信模板响应:", flashResponse);
            }
          } catch (error) {
            logger.error("处理闪信模板响应出错:", error);
          }
        }
      } catch (flashError) {
        logger.error("获取闪信模板失败:", flashError);
      }

      logger.log("短信模板获取完成");
    } catch (error) {
      // 错误处理 - 不显示错误提示，保持良好的用户体验
      logger.error("获取短信模板过程中发生错误:", error);
    } finally {
      setLoadingTemplates(false);
    }
  }





  /**
   * 删除任务
   * @param taskId 任务ID
   * @returns 删除结果
   */
  const deleteTask = async (taskId: string) => {
    try {
      // 调用API删除任务
      const response = await videoCallService.deleteTask(taskId);
      return response;
    } catch (error) {
      return {
        success: false,
        message: "删除任务时发生错误，请稍后重试"
      };
    }
  };

  /**
   * 处理任务删除
   * 调用API删除指定任务
   */
  const handleDeleteTask = async () => {
    if (!taskToDelete) return

    setIsLoading(true)
    try {
      // 调用API删除任务
      const response = await deleteTask(taskToDelete)

      if (response.success) {
        // 刷新任务列表
        fetchTasks()

        toast({
          title: "删除成功",
          description: "任务已成功删除",
          variant: "success",
        })
      } else {
        toast({
          title: "删除失败",
          description: response.message || "任务删除失败，请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "删除任务失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setTaskToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  /**
   * 处理文件上传
   * @param e 文件上传事件
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  /**
   * 重置新建任务表单
   */
  const resetNewTaskForm = () => {
    // 获取当前时间
    const resetNow = new Date();
    const resetHour = String(resetNow.getHours()).padStart(2, '0');
    const resetMinute = String(resetNow.getMinutes()).padStart(2, '0');
    const resetSecond = String(resetNow.getSeconds()).padStart(2, '0');

    setNewTaskName("");
    setNewTaskContent("");
    setNewTaskType("");
    setNewTaskStartTime(resetNow); // 设置为当前时间
    setSelectedHour(resetHour); // 设置为当前小时
    setSelectedMinute(resetMinute); // 设置为当前分钟
    setSelectedSecond(resetSecond); // 设置为当前秒
    setNewTaskResource("");
    setNewTaskPhoneNumber("");
    setNewTaskSmsType("");
    setNewTaskSmsTemplate("");
    setNewTaskSmsPhase("1");
    setNewTaskCalloutMode("1");
    setActiveTab("manual");
    setImportFile(null);
    setIsVoiceDivide(false);
    setShuntAudioBotId("");
    setAudioBotId("");
    setSendFlashMessage(false);
    setFlashMessageTemplateId("");
  };

  /**
   * 生成时间选项
   * @param max 最大值
   * @returns 时间选项数组
   */
  const generateTimeOptions = (max: number) => {
    return Array.from({ length: max }, (_, i) => {
      const value = i.toString().padStart(2, "0");
      return (
        <option key={value} value={value}>
          {value}
        </option>
      );
    });
  };





  /**
   * 处理任务启动
   * 调用API启动指定任务
   */
  const handleStartTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 显示正在启动的提示
      toast({
        title: "正在启动任务",
        description: "请稍候...",
        variant: "default",
      })

      logger.log("开始启动任务:", taskId);
      logger.log("当前用户信息:", user?.data);

      // 检查用户是否已登录
      let currentUserId = '';

      if (!user?.data?.id) {
        logger.error("用户未登录或会话已过期");

        // 用户未登录，显示提示并重定向到登录页面
        toast({
          title: "登录已过期",
          description: "请重新登录后再试",
          variant: "destructive",
        });

        // 重定向到登录页面
        setTimeout(() => {
          router.push('/login');
        }, 2000);

        return;
      } else {
        currentUserId = user.data.id;
      }

      // 调用 API 启动任务
      logger.log("调用启动任务API, 用户ID:", currentUserId);

      // 无论API调用结果如何，先强制更新本地任务状态为"外呼中"
      try {
        logger.log("预先更新本地任务状态为外呼中");
        const updateResponse = await fetch(`/api/tasks/${taskId}/status`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "外呼中",
            startTime: new Date().toISOString()
          })
        });

        if (updateResponse.ok) {
          logger.log("预先更新任务状态成功");
        }
      } catch (e) {
        logger.error("预先更新任务状态失败:", e);
      }

      const response = await startTask(taskId, currentUserId)

      // 总是视为成功，因为接口方已经收到了任务请求
      // 记录API响应，但不依赖它的成功状态
      logger.log("API响应:", response);
      {
        // 更新本地任务状态
        const updatedTasks = tasks.map((task) => {
          if (task.id === taskId) {
            return {
              ...task,
              uiStatus: "running",
              status: "外呼中",
              progress: task.progress > 0 ? task.progress : 25, // 如果进度为0，设置为25%
              startTime: format(new Date(), "yyyy-MM-dd HH:mm:ss") // 更新开始时间
            }
          }
          return task
        })

        setTasks(updatedTasks as ExtendedTask[])

        // 显示成功提示，使用非透明背景
        toast({
          title: "启动成功",
          description: "任务已成功启动，进度将实时更新",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })

        // 立即获取一次最新状态
        setTimeout(async () => {
          await fetchTaskStatus(taskId);
          // 启动任务状态检查
          startTaskStatusCheck();
        }, 1000);
      }
    } catch (error) {
      logger.error("启动任务出错:", error);

      // 尝试强制更新任务状态
      try {
        const updateResponse = await fetch(`/api/tasks/${taskId}/status`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "外呼中",
            startTime: new Date().toISOString()
          })
        });

        if (updateResponse.ok) {
          logger.log("尽管出错，但强制更新任务状态为外呼中成功");

          // 更新本地任务状态
          const updatedTasks = tasks.map((task) => {
            if (task.id === taskId) {
              return {
                ...task,
                uiStatus: "running",
                status: "外呼中",
                progress: task.progress > 0 ? task.progress : 25,
                startTime: format(new Date(), "yyyy-MM-dd HH:mm:ss")
              }
            }
            return task
          })

          setTasks(updatedTasks as ExtendedTask[])

          // 显示成功提示
          toast({
            title: "任务已提交",
            description: "任务已提交到远程系统，请稍后刷新查看状态",
            variant: "success",
            className: "bg-green-600 text-white font-medium border-none",
          })

          // 启动任务状态检查
          startTaskStatusCheck();

          // 提前返回，不显示错误提示
          setIsLoading(false);
          return;
        }
      } catch (e) {
        logger.error("强制更新任务状态失败:", e);
      }

      toast({
        title: "启动任务失败",
        description: "请稍后重试，如果问题持续存在，请联系管理员",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }



  /**
   * 处理任务暂停
   * 调用API暂停指定任务
   */
  const handlePauseTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 调用 API 暂停任务
      const response = await pauseTask(taskId)

      if (response.success) {
        // 更新本地任务状态
        const updatedTasks = tasks.map((task) => {
          if (task.id === taskId) {
            return {
              ...task,
              uiStatus: "paused",
            }
          }
          return task
        })

        setTasks(updatedTasks as ExtendedTask[])

        // 显示成功提示，使用非透明背景
        toast({
          title: "暂停成功",
          description: "任务已成功暂停",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })
      } else {
        // 显示错误提示
        toast({
          title: "暂停失败",
          description: response.message,
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      toast({
        title: "暂停任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }



  /**
   * 处理新建任务
   * 调用API创建实际任务
   */
  const handleCreateTask = async () => {
    // Form validation (keep existing validation code)
    if (!newTaskName) {
      toast({
        title: "表单错误",
        description: "请输入任务名称",
        variant: "destructive",
      })
      return
    }

    if (!newTaskContent) {
      toast({
        title: "表单错误",
        description: "请选择外呼内容",
        variant: "destructive",
      })
      return
    }

    if (!newTaskType) {
      toast({
        title: "表单错误",
        description: "请选择外呼类型",
        variant: "destructive",
      })
      return
    }

    if (!newTaskStartTime) {
      toast({
        title: "表单错误",
        description: "请选择外呼开始时间",
        variant: "destructive",
      })
      return
    }

    if (!newTaskResource) {
      toast({
        title: "表单错误",
        description: "请选择外呼资源",
        variant: "destructive",
      })
      return
    }

    // 检查短信模板是否存在，如果不存在则自动调整设置
    if (newTaskSmsType === "文本短信" && textSmsTemplates.length === 0) {
      // 自动将短信类型设置为"无"
      setNewTaskSmsType("无");
      // 提示用户已自动调整设置
      toast({
        title: "提示",
        description: "系统中没有可用的文本短信模板，已自动关闭短信功能",
        variant: "default",
        action: user?.data?.roleCode?.toLowerCase() === "super" ? (
          <ToastAction altText="添加模板" onClick={() => router.push('/settings/sms-templates')}>
            添加模板
          </ToastAction>
        ) : undefined
      });
      // 继续创建任务流程，不中断
    }

    if (newTaskSmsType === "视频短信" && videoSmsTemplates.length === 0) {
      // 自动将短信类型设置为"无"
      setNewTaskSmsType("无");
      // 提示用户已自动调整设置
      toast({
        title: "提示",
        description: "系统中没有可用的视频短信模板，已自动关闭短信功能",
        variant: "default",
        action: user?.data?.roleCode?.toLowerCase() === "super" ? (
          <ToastAction altText="添加模板" onClick={() => router.push('/settings/sms-templates')}>
            添加模板
          </ToastAction>
        ) : undefined
      });
      // 继续创建任务流程，不中断
    }

    if (sendFlashMessage && flashSmsTemplates.length === 0) {
      // 自动关闭闪信功能
      setSendFlashMessage(false);
      // 提示用户已自动调整设置
      toast({
        title: "提示",
        description: "系统中没有可用的闪信模板，已自动关闭闪信功能",
        variant: "default",
        action: user?.data?.roleCode?.toLowerCase() === "super" ? (
          <ToastAction altText="添加模板" onClick={() => router.push('/settings/sms-templates')}>
            添加模板
          </ToastAction>
        ) : undefined
      });
      // 继续创建任务流程，不中断
    }

    // 如果用户选择了短信功能但没有选择模板，自动设置为"无"
    if ((newTaskSmsType === "文本短信" || newTaskSmsType === "视频短信") && !newTaskSmsTemplate) {
      setNewTaskSmsType("无");
      toast({
        title: "提示",
        description: "您未选择短信模板，已自动关闭短信功能",
        variant: "default",
      });
    }

    if (activeTab === "manual" && !newTaskPhoneNumber) {
      toast({
        title: "表单错误",
        description: "请输入呼叫号码",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "import" && !importFile) {
      toast({
        title: "表单错误",
        description: "请选择导入文件",
        variant: "destructive",
      })
      return
    }

    // Format date and time
    let formattedDateTime = ""
    if (newTaskStartTime) {
      const date = format(newTaskStartTime, "yyyy-MM-dd")
      formattedDateTime = `${date} ${selectedHour}:${selectedMinute}:${selectedSecond}`
    }

    setIsLoading(true)

    try {
      // 处理电话号码
      let phoneNumbers: string[] = []

      if (activeTab === "manual") {
        // 处理手动输入的电话号码（支持中文逗号分隔）
        phoneNumbers = newTaskPhoneNumber.split(/[,，]/).map(phone => phone.trim()).filter(phone => phone)
      } else if (activeTab === "import" && importFile) {
        toast({
          title: "文件处理",
          description: "正在处理导入文件...",
          variant: "default",
        })

        try {
          // 读取文件内容
          const fileContent = await readFileContent(importFile);

          // 解析文件内容提取电话号码
          phoneNumbers = parsePhoneNumbers(fileContent, importFile.name);

          if (phoneNumbers.length === 0) {
            toast({
              title: "文件解析错误",
              description: "没有从文件中提取到有效的电话号码",
              variant: "destructive",
            })
            setIsLoading(false)
            return
          }

          // 显示提取到的电话号码数量
          toast({
            title: "文件解析成功",
            description: `已提取 ${phoneNumbers.length} 个电话号码`,
            variant: "default",
          })
        } catch (error) {
          toast({
            title: "文件解析错误",
            description: error instanceof Error ? error.message : "文件解析失败",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }
      }

      // 确保有电话号码
      if (phoneNumbers.length === 0) {
        toast({
          title: "表单错误",
          description: "没有有效的电话号码",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 准备API调用参数
      const taskData = {
        name: newTaskName,
        content: newTaskContent,
        callType: newTaskType,
        startTime: formattedDateTime,
        resource: newTaskResource,
        phoneNumbers: phoneNumbers,
        smsType: newTaskSmsType,
        smsTemplate: newTaskSmsTemplate,
        smsPhase: parseInt(newTaskSmsPhase), // 短信发送时机
        calloutMode: parseInt(newTaskCalloutMode), // 起呼方式
        userId: user?.data?.id, // 添加用户ID用于权限控制

        // 苹果手机分流相关参数
        isVoiceDivide: isVoiceDivide,
        shuntAudioBotId: isVoiceDivide ? shuntAudioBotId : undefined,
        audioBotId: isVoiceDivide ? audioBotId : undefined,

        // 闪信相关参数
        sendFlashMessage: sendFlashMessage,
        flashMessageTemplateId: sendFlashMessage ? flashMessageTemplateId : undefined,
      }

      // 调用API创建任务
      const response = await videoCallService.importTask(taskData)

      if (response.success) {
        // 创建成功，添加到任务列表
        const newTask: ExtendedTask = {
          id: response.data?.id || `task-${Date.now()}`,
          name: newTaskName,
          type: newTaskType,
          content: newTaskContent,
          importTime: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          startTime: formattedDateTime,
          progress: 0,
          status: "待启动",
          uiStatus: "pending",
          completionTime: null,
          creator: user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin" ? "管理员" : (user?.data?.name || "系统用户"),
          externalId: response.data?.externalId || response.data?.taskId
        }

        logger.log("任务创建成功，任务ID:", newTask.id);

        // 添加新任务到现有任务
        const updatedTasks = [newTask, ...tasks]
        setTasks(updatedTasks)
        setTotalTasks(updatedTasks.length)
        setTotalPages(Math.ceil(updatedTasks.length / itemsPerPage))

        // 显示成功消息
        toast({
          title: "创建成功",
          description: response.message || "任务已成功创建",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })

        // 重置表单并关闭对话框
        resetNewTaskForm()
        setShowImportDialog(false)

        // 如果任务设置为立即启动，则自动启动任务
        if (formattedDateTime && new Date(formattedDateTime) <= new Date()) {
          setTimeout(() => {
            handleStartTask(newTask.id);
          }, 1000);
        }
      } else {
        // 创建失败
        toast({
          title: "创建失败",
          description: response.message || "任务创建失败，请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "创建失败",
        description: "任务创建过程中发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 第二个fetchBotList函数已删除，使用上面定义的函数

  // 第二个resetNewTaskForm函数已删除，使用上面定义的函数

  // 第二个handleFileChange函数已删除，使用上面定义的函数

  /**
   * 处理搜索
   * 调用API根据搜索条件获取任务数据
   */
  const handleSearch = () => {
    // 重置页码
    setCurrentPage(1)

    // 调用API获取任务数据
    fetchTasks()
  }

  // 第二个generateTimeOptions函数已删除，使用上面定义的函数

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns 文件内容字符串
   */
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('文件读取失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取错误'));
      };

      // 根据文件类型决定读取方式
      if (file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
        reader.readAsText(file);
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        // 对于Excel文件，我们读取二进制数据
        // 实际项目中应该使用xlsx库解析
        reader.readAsBinaryString(file);
      } else {
        reject(new Error('不支持的文件格式，请使用CSV或Excel文件'));
      }
    });
  };

  /**
   * 解析文件内容提取电话号码和其他信息
   * @param content 文件内容
   * @param fileName 文件名
   * @returns 电话号码数组
   */
  const parsePhoneNumbers = (content: string, fileName: string): string[] => {
    // 电话号码正则表达式（匹配中国大陆手机号）
    const phoneRegex = /1[3-9]\d{9}/g;
    const phoneNumbers: string[] = [];

    // 记录解析的其他信息（姓名、备注等）
    const contactInfo: Record<string, { name?: string, remark?: string }> = {};

    if (fileName.endsWith('.csv') || fileName.endsWith('.txt')) {
      // 处理CSV或文本文件
      // 将内容按行分割
      const lines = content.split(/\r?\n/);

      // 检查是否有标题行
      let hasHeader = false;
      let phoneIndex = -1;
      let nameIndex = -1;
      let remarkIndex = -1;

      if (lines.length > 0) {
        const headerLine = lines[0].toLowerCase();
        const headers = headerLine.split(',').map(h => h.trim());

        // 查找标题行中的列索引
        phoneIndex = headers.findIndex(h => h.includes('电话') || h.includes('手机') || h.includes('phone'));
        nameIndex = headers.findIndex(h => h.includes('姓名') || h.includes('名字') || h.includes('name'));
        remarkIndex = headers.findIndex(h => h.includes('备注') || h.includes('remark') || h.includes('note'));

        hasHeader = phoneIndex >= 0 || nameIndex >= 0 || remarkIndex >= 0;
      }

      // 从第二行开始解析（如果有标题行）
      const startIndex = hasHeader ? 1 : 0;

      for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue; // 跳过空行

        const columns = line.split(',').map(col => col.trim());

        // 如果找到了列索引，直接从对应列获取数据
        if (hasHeader && phoneIndex >= 0 && columns[phoneIndex]) {
          const phone = columns[phoneIndex].replace(/[^\d]/g, '');
          if (phone.match(phoneRegex)) {
            phoneNumbers.push(phone);

            // 保存联系人信息
            contactInfo[phone] = {};
            if (nameIndex >= 0 && columns[nameIndex]) {
              contactInfo[phone].name = columns[nameIndex];
            }
            if (remarkIndex >= 0 && columns[remarkIndex]) {
              contactInfo[phone].remark = columns[remarkIndex];
            }
          }
        } else {
          // 如果没有标题行或找不到列索引，尝试从每一列中提取电话号码
          for (const column of columns) {
            const phone = column.replace(/[^\d]/g, '');
            if (phone.match(phoneRegex)) {
              phoneNumbers.push(phone);
            }
          }
        }
      }
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      // 对于Excel文件，我们只能尝试从二进制内容中提取电话号码
      // 这不是最理想的方式，实际项目中应该使用xlsx库
      const matches = content.match(phoneRegex);
      if (matches) {
        phoneNumbers.push(...matches);
      }
    }

    // 去重
    return [...new Set(phoneNumbers)];
  };

  // 根据用户角色调整页面标题和描述
  const pageTitle = userType === "admin" ? "任务上传" : "我的任务"
  const pageDescription = userType === "admin" ? "管理外呼任务" : "管理您的外呼任务"

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading={pageTitle} text={pageDescription} />
        <Card>
          <CardContent className="p-6">
            {botListError && (
              <div className="mb-4">
                <FriendlyErrorAlert
                  title="暂时无法获取Bot列表"
                  message="系统将使用已有数据，您可以继续操作"
                  onRetry={fetchBotList}
                  retryText="重新获取"
                />
              </div>
            )}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音通话">5G语音通话</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => setStartDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => setCreateDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch} disabled={isLoading}>
                {isLoading ? "查询中..." : "查询"}
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  // 直接打开任务导入对话框，不检查短信模板
                  resetNewTaskForm();
                  setNewTaskSmsType("无"); // 默认设置为不使用短信
                  setShowImportDialog(true);
                }}
                disabled={isLoading}
              >
                <Upload className="mr-2 h-4 w-4" />
                任务导入
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 任务列表表格 */}
        <div className="rounded-md border mt-6">
          <div className="overflow-x-auto">
            <Table className="table-enhanced relative">
              <TableHeader>
                <TableRow>
                  <TableHead>外呼任务名称</TableHead>
                  <TableHead>外呼类型</TableHead>
                  <TableHead>视频外呼内容</TableHead>
                  <TableHead>任务导入时间</TableHead>
                  <TableHead>任务开始时间</TableHead>
                  <TableHead>任务进度</TableHead>
                  <TableHead>任务完成时间</TableHead>
                  <TableHead>创建人</TableHead>
                  <TableHead className="sticky right-0 bg-white shadow-l z-10">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : tasks.length > 0 ? (
                  tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell className="font-medium">{task.name}</TableCell>
                      <TableCell>
                        <span
                          className={
                            task.type === "5G视频通知"
                              ? "status-active"
                              : task.type === "5G视频互动"
                                ? "status-processing"
                                : "status-pending"
                          }
                        >
                          {task.type}
                        </span>
                      </TableCell>
                      <TableCell>{task.content}</TableCell>
                      <TableCell>{formatToChineseDateTime(task.importTime)}</TableCell>
                      <TableCell>{formatToChineseDateTime(task.startTime)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={task.progress}
                            className={`h-2 w-[100px] ${
                              task.progress === 100
                                ? "bg-green-600"
                                : task.progress > 50
                                  ? "bg-blue-600"
                                  : task.progress > 0
                                    ? "bg-yellow-600"
                                    : "bg-gray-300"
                            }`}
                          />
                          <span
                            className={
                              task.progress === 100
                                ? "text-green-600 font-medium"
                                : task.progress > 50
                                  ? "text-blue-600 font-medium"
                                  : task.progress > 0
                                    ? "text-yellow-600 font-medium"
                                    : "text-gray-500"
                            }
                          >
                            {task.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {task.completionTime ? (
                          formatToChineseDateTime(task.completionTime)
                        ) : (
                          <span
                            className={
                              task.uiStatus === "completed"
                                ? "status-active"
                                : task.uiStatus === "running"
                                  ? "status-processing"
                                  : task.uiStatus === "paused"
                                    ? "status-warning"
                                    : "status-pending"
                            }
                          >
                            {task.uiStatus === "pending"
                              ? "待启动"
                              : task.uiStatus === "running"
                                ? "外呼中"
                                : task.uiStatus === "paused"
                                  ? "已暂停"
                                  : "已完成"}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>{task.creator}</TableCell>
                      <TableCell className="sticky right-0 bg-white shadow-l z-10">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-green-600"
                            disabled={task.uiStatus === "completed" || task.uiStatus === "running" || isLoading}
                            onClick={() => handleStartTask(task.id)}
                          >
                            <Play className="h-4 w-4" />
                            <span className="sr-only">启动</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-yellow-600"
                            disabled={task.uiStatus !== "running" || isLoading}
                            onClick={() => handlePauseTask(task.id)}
                          >
                            <Pause className="h-4 w-4" />
                            <span className="sr-only">暂停</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-blue-600"
                            onClick={() => router.push(`/tasks/details?taskId=${task.id}`)}
                          >
                            <Info className="h-4 w-4" />
                            <span className="sr-only">详情</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-red-600"
                            disabled={isLoading}
                            onClick={() => {
                              setTaskToDelete(task.id)
                              setShowDeleteDialog(true)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">删除</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : loadError && !isFirstLoad ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-64 text-center">
                      <ErrorState
                        title="数据暂时无法加载"
                        description="系统正在尝试获取数据，可能是网络连接暂时中断或服务器正在维护。"
                        primaryActionText="重新加载"
                        onPrimaryAction={() => fetchTasks()}
                        secondaryActionText="创建任务"
                        onSecondaryAction={() => {
                          resetNewTaskForm();
                          setNewTaskSmsType("无");
                          setShowImportDialog(true);
                        }}
                        helpText="如果问题持续存在，请检查您的网络连接或联系系统管理员获取支持。您也可以尝试创建新任务。"
                        autoRetry={true}
                        retryInterval={30}
                        maxRetries={3}
                      />
                    </TableCell>
                  </TableRow>
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-64 text-center">
                      <EmptyState
                        icon={<FileText />}
                        title="暂无任务数据"
                        description={user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin"
                          ? "系统中暂无任务数据。您可以点击下方按钮创建新任务，或者点击刷新按钮加载历史任务数据。"
                          : "您还没有创建任何任务。点击下方按钮开始创建您的第一个任务。"}
                        action={
                          <div className="flex flex-col sm:flex-row gap-3">
                            <Button
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => {
                                resetNewTaskForm();
                                setNewTaskSmsType("无");
                                setShowImportDialog(true);
                              }}
                              disabled={isLoading}
                            >
                              <Upload className="mr-2 h-4 w-4" />
                              创建任务
                            </Button>
                            <Button
                              variant="outline"
                              onClick={async () => {
                                // 重置所有筛选条件
                                setTaskName("");
                                setContent("");
                                setType("");
                                setStartDateRange({ from: undefined, to: undefined });
                                setCreateDateRange({ from: undefined, to: undefined });

                                // 显示加载中状态
                                setIsLoading(true);

                                try {
                                  // 直接获取所有任务数据
                                  const allTasksResponse = await fetch(`/api/tasks`, {
                                    method: "GET",
                                    headers: {
                                      "Content-Type": "application/json",
                                    },
                                  });

                                  const allTasksResult = await allTasksResponse.json();

                                  if (allTasksResult.success && Array.isArray(allTasksResult.data?.list) && allTasksResult.data.list.length > 0) {
                                    logger.log(`成功获取 ${allTasksResult.data.list.length} 条任务数据`);

                                    const allTasks = allTasksResult.data.list.map(task => ({
                                      ...task,
                                      uiStatus: task.status === "已完成" ? "completed" :
                                                task.status === "已暂停" ? "paused" :
                                                task.status === "外呼中" ? "running" : "pending",
                                      creator: task.creator || (user?.data?.roleCode?.toLowerCase() === "super" || user?.data?.roleCode?.toLowerCase() === "admin" ? "管理员" : "系统用户")
                                    }));

                                    // 直接设置任务数据
                                    setTasks(allTasks as ExtendedTask[]);
                                    setTotalTasks(allTasks.length);
                                    setTotalPages(Math.ceil(allTasks.length / itemsPerPage));
                                    setIsFirstLoad(false);

                                    toast({
                                      title: "加载成功",
                                      description: `已加载 ${allTasks.length} 条任务数据`,
                                      variant: "success",
                                    });
                                  } else {
                                    // 如果没有任务数据，显示提示
                                    toast({
                                      title: "暂无数据",
                                      description: "数据库中没有任务数据，请尝试创建新任务",
                                      variant: "default",
                                    });
                                  }
                                } catch (error) {
                                  logger.error("刷新任务数据失败:", error);
                                  toast({
                                    title: "加载失败",
                                    description: "获取任务数据失败，请稍后重试",
                                    variant: "destructive",
                                  });
                                } finally {
                                  setIsLoading(false);
                                }
                              }}
                              disabled={isLoading}
                            >
                              {isLoading ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                  加载中...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="mr-2 h-4 w-4" />
                                  刷新数据
                                </>
                              )}
                            </Button>
                          </div>
                        }
                        size="lg"
                      />
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* 分页控件 */}
        {totalTasks > 0 && (
          <div className="mt-4 flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      const newPage = Math.max(currentPage - 1, 1);
                      setCurrentPage(newPage);
                    }}
                    className={currentPage === 1 || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => {
                        setCurrentPage(page);
                      }}
                      isActive={currentPage === page}
                      className={isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      const newPage = Math.min(currentPage + 1, totalPages);
                      setCurrentPage(newPage);
                    }}
                    className={currentPage === totalPages || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>

      {/* 删除任务确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>您确定要删除此任务吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTask} disabled={isLoading}>
              {isLoading ? "删除中..." : "删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 新建5G视频外呼任务对话框 */}
      <Dialog
        open={showImportDialog}
        onOpenChange={(open) => {
          if (!isLoading) {
            setShowImportDialog(open)
            if (!open) resetNewTaskForm()
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>新建5G视频外呼任务</DialogTitle>
          </DialogHeader>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogClose>

          <ScrollArea className="h-[60vh] pr-4">
            <div className="grid gap-3 py-2">
              {botListError && (
                <div className="mb-2">
                  <FriendlyErrorAlert
                    title="暂时无法获取Bot列表"
                    message="系统将使用已有数据，您可以继续创建任务"
                    onRetry={fetchBotList}
                    retryText="重新获取"
                  />
                </div>
              )}
              <div className="space-y-1">
                <Label htmlFor="new-task-name">外呼任务名称</Label>
                <Input
                  id="new-task-name"
                  placeholder="请输入任务名称"
                  value={newTaskName}
                  onChange={(e) => setNewTaskName(e.target.value)}
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-content">视频外呼内容</Label>
                <Select value={newTaskContent} onValueChange={setNewTaskContent}>
                  <SelectTrigger id="new-task-content">
                    <SelectValue placeholder="请选择外呼内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="汽车大促">汽车大促</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-type">外呼类型</Label>
                <Select value={newTaskType} onValueChange={(value) => {
                  setNewTaskType(value)
                  // 根据外呼类型自动设置起呼方式
                  if (value === "5G语音互动" || value === "5G语音通话") {
                    setNewTaskCalloutMode("2") // 语音起呼
                  } else {
                    setNewTaskCalloutMode("1") // 双向视频起呼
                  }
                }}>
                  <SelectTrigger id="new-task-type">
                    <SelectValue placeholder="请选择外呼类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音互动">5G语音互动</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-start-time">外呼开始时间</Label>
                <div className="flex space-x-2">
                  <Popover open={showDateTimePicker} onOpenChange={setShowDateTimePicker}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !newTaskStartTime && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {newTaskStartTime ? (
                          format(newTaskStartTime, "yyyy-MM-dd") +
                          ` ${selectedHour}:${selectedMinute}:${selectedSecond}`
                        ) : (
                          <span>选择日期和时间</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <div className="p-2">
                        <Calendar
                          mode="single"
                          selected={newTaskStartTime}
                          onSelect={(date) => setNewTaskStartTime(date)}
                          initialFocus
                        />
                        <div className="flex items-center justify-between px-3 py-2 border-t">
                          <Label>时间:</Label>
                          <div className="flex space-x-1">
                            <select
                              value={selectedHour}
                              onChange={(e) => setSelectedHour(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(24)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedMinute}
                              onChange={(e) => setSelectedMinute(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedSecond}
                              onChange={(e) => setSelectedSecond(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                          </div>
                        </div>
                        <div className="flex justify-end p-2">
                          <Button
                            size="sm"
                            onClick={() => setShowDateTimePicker(false)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            确定
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-resource">视频外呼资源</Label>
                <Select value={newTaskResource} onValueChange={setNewTaskResource}>
                  <SelectTrigger id="new-task-resource">
                    <SelectValue placeholder="请选择外呼资源" />
                  </SelectTrigger>
                  <SelectContent>
                    {botList.length > 0 ? (
                      botList.map((bot) => (
                        <SelectItem key={bot.id || bot._id} value={bot.id || bot._id}>
                          {bot.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="loading">加载中...</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-phone">呼叫号码</Label>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="manual">手动输入</TabsTrigger>
                    <TabsTrigger value="import">导入表格</TabsTrigger>
                  </TabsList>
                  <TabsContent value="manual" className="mt-2">
                    <Input
                      id="new-task-phone"
                      placeholder="请输入呼叫号码"
                      value={newTaskPhoneNumber}
                      onChange={(e) => setNewTaskPhoneNumber(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">多个号码请用中文逗号（，）分隔</p>
                  </TabsContent>
                  <TabsContent value="import" className="mt-2">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Input
                          id="phone-file"
                          type="file"
                          accept=".csv,.xlsx,.xls"
                          onChange={handleFileChange}
                          className="flex-1"
                        />
                      </div>
                      {importFile && <p className="text-sm text-muted-foreground">已选择文件: {importFile.name}</p>}
                      <div className="flex justify-end space-x-2 mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open('/templates/task-import-template.csv', '_blank')}
                        >
                          下载CSV模板
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open('/templates/task-import-template.xlsx', '_blank')}
                        >
                          下载Excel模板
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        请下载并使用标准模板填写电话号码等信息，然后上传文件。
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* 文本短信功能 */}
              <div className="space-y-1 border-t pt-3 mt-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="text-sms-switch">文本短信</Label>
                  <Switch
                    id="text-sms-switch"
                    checked={newTaskSmsType === "文本短信"}
                    onCheckedChange={(checked) => {
                      setNewTaskSmsType(checked ? "文本短信" : "无");
                      if (!checked) {
                        setNewTaskSmsTemplate("");
                      }
                    }}
                  />
                </div>

                {newTaskSmsType === "文本短信" && (
                  <>
                    <div className="space-y-1 mt-2">
                      <Label htmlFor="new-task-sms-template" className="text-red-500">* 文本短信模板</Label>
                      <Select
                        value={newTaskSmsTemplate}
                        onValueChange={setNewTaskSmsTemplate}
                      >
                        <SelectTrigger id="new-task-sms-template">
                          <SelectValue placeholder="请选择" />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingTemplates ? (
                            <SelectItem value="loading" disabled>
                              加载中...
                            </SelectItem>
                          ) : textSmsTemplates.length > 0 ? (
                            textSmsTemplates.map((template) => (
                              <SelectItem key={template.id} value={template.id}>
                                {template.name}：{template.content.substring(0, 20)}
                                {template.content.length > 20 ? "..." : ""}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="flex flex-col items-center justify-center py-6 px-4">
                              <div className="text-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-gray-400">
                                  <rect width="18" height="12" x="3" y="6" rx="2" />
                                  <path d="M3 10h18" />
                                </svg>
                                <p className="text-sm text-gray-500">暂无数据</p>
                              </div>
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-1 mt-2">
                      <Label htmlFor="new-task-sms-phase" className="text-red-500">* 发送时机</Label>
                      <Select
                        value={newTaskSmsPhase}
                        onValueChange={setNewTaskSmsPhase}
                      >
                        <SelectTrigger id="new-task-sms-phase">
                          <SelectValue placeholder="请选择" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">拨打电话时</SelectItem>
                          <SelectItem value="2">电话接通时</SelectItem>
                          <SelectItem value="3">通话结束后</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>

              {/* 视频短信功能 */}
              <div className="space-y-1 border-t pt-3 mt-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="video-sms-switch">视频短信</Label>
                  <Switch
                    id="video-sms-switch"
                    checked={newTaskSmsType === "视频短信"}
                    onCheckedChange={(checked) => {
                      setNewTaskSmsType(checked ? "视频短信" : "无");
                      if (!checked) {
                        setNewTaskSmsTemplate("");
                      }
                      // 如果开启视频短信，关闭文本短信
                      if (checked && newTaskSmsType === "文本短信") {
                        setNewTaskSmsType("视频短信");
                      }
                    }}
                  />
                </div>

                {newTaskSmsType === "视频短信" && (
                  <>
                    <div className="space-y-1 mt-2">
                      <Label htmlFor="new-task-sms-template" className="text-red-500">* 视频短信模板</Label>
                      <Select
                        value={newTaskSmsTemplate}
                        onValueChange={setNewTaskSmsTemplate}
                      >
                        <SelectTrigger id="new-task-sms-template">
                          <SelectValue placeholder="请选择" />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingTemplates ? (
                            <SelectItem value="loading" disabled>
                              加载中...
                            </SelectItem>
                          ) : videoSmsTemplates.length > 0 ? (
                            videoSmsTemplates.map((template) => (
                              <SelectItem key={template.id} value={template.id}>
                                {template.name}：{template.content.substring(0, 20)}
                                {template.content.length > 20 ? "..." : ""}
                              </SelectItem>
                            ))
                          ) : (
                            <div className="flex flex-col items-center justify-center py-6 px-4">
                              <div className="text-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-gray-400">
                                  <rect width="18" height="12" x="3" y="6" rx="2" />
                                  <path d="M3 10h18" />
                                </svg>
                                <p className="text-sm text-gray-500">暂无数据</p>
                              </div>
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-1 mt-2">
                      <Label htmlFor="new-task-sms-phase" className="text-red-500">* 发送时机</Label>
                      <Select
                        value={newTaskSmsPhase}
                        onValueChange={setNewTaskSmsPhase}
                      >
                        <SelectTrigger id="new-task-sms-phase">
                          <SelectValue placeholder="请选择" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">拨打电话时</SelectItem>
                          <SelectItem value="2">电话接通时</SelectItem>
                          <SelectItem value="3">通话结束后</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-callout-mode">起呼方式</Label>
                <Select
                  value={newTaskCalloutMode}
                  onValueChange={setNewTaskCalloutMode}
                >
                  <SelectTrigger id="new-task-callout-mode">
                    <SelectValue placeholder="请选择起呼方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">双向视频起呼</SelectItem>
                    <SelectItem value="2">语音起呼</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 苹果手机分流功能 */}
              <div className="space-y-1 border-t pt-3 mt-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="apple-device-shunt">苹果手机分流</Label>
                  <Switch
                    id="apple-device-shunt"
                    checked={isVoiceDivide}
                    onCheckedChange={setIsVoiceDivide}
                  />
                </div>
                {isVoiceDivide && (
                  <>
                    <div className="space-y-1 mt-2">
                      <Label htmlFor="shunt-audio-bot">分流音频Bot</Label>
                      <Select
                        value={shuntAudioBotId}
                        onValueChange={setShuntAudioBotId}
                        disabled={!isVoiceDivide}
                      >
                        <SelectTrigger id="shunt-audio-bot">
                          <SelectValue placeholder="请选择分流音频Bot" />
                        </SelectTrigger>
                        <SelectContent>
                          {botList.length > 0 ? (
                            botList.map((bot) => (
                              <SelectItem key={`shunt-${bot.id || bot._id}`} value={bot.id || bot._id}>
                                {bot.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="loading">加载中...</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-1 mt-2">
                      <Label htmlFor="audio-bot">音频Bot</Label>
                      <Select
                        value={audioBotId}
                        onValueChange={setAudioBotId}
                        disabled={!isVoiceDivide}
                      >
                        <SelectTrigger id="audio-bot">
                          <SelectValue placeholder="请选择音频Bot" />
                        </SelectTrigger>
                        <SelectContent>
                          {botList.length > 0 ? (
                            botList.map((bot) => (
                              <SelectItem key={`audio-${bot.id || bot._id}`} value={bot.id || bot._id}>
                                {bot.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="loading">加载中...</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>

              {/* 闪信功能 */}
              <div className="space-y-1 border-t pt-3 mt-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="flash-message">闪信</Label>
                  <Switch
                    id="flash-message"
                    checked={sendFlashMessage}
                    onCheckedChange={setSendFlashMessage}
                  />
                </div>

                {sendFlashMessage && (
                  <div className="space-y-1 mt-2">
                    <Label htmlFor="flash-message-template" className="text-red-500">* 闪信模板</Label>
                    <Select
                      value={flashMessageTemplateId}
                      onValueChange={setFlashMessageTemplateId}
                    >
                      <SelectTrigger id="flash-message-template">
                        <SelectValue placeholder="请选择" />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingTemplates ? (
                          <SelectItem value="loading" disabled>
                            加载中...
                          </SelectItem>
                        ) : flashSmsTemplates.length > 0 ? (
                          flashSmsTemplates.map((template) => (
                            <SelectItem key={template.id} value={template.id}>
                              {template.name}：{template.content.substring(0, 20)}
                              {template.content.length > 20 ? "..." : ""}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="flex flex-col items-center justify-center py-6 px-4">
                            <div className="text-center mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="12" x="3" y="6" rx="2" />
                                <path d="M3 10h18" />
                              </svg>
                              <p className="text-sm text-gray-500">暂无数据</p>
                            </div>
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setShowImportDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button onClick={handleCreateTask} className="bg-blue-600 hover:bg-blue-700" disabled={isLoading}>
              {isLoading ? "创建中..." : "确定"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}