"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ChevronLeft, ChevronRight, CalendarIcon, Download, ChevronDown, Search } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { zhCN } from "date-fns/locale"

// 充值记录类型
interface RechargeRecord {
  id: string
  amount: number
  paymentMethod: string
  date: string
  operator: string
  remark: string
}

// 生成演示数据
const generateDemoData = (count: number): RechargeRecord[] => {
  const paymentMethods = ["微信支付", "支付宝", "银行转账", "现金", "余额支付"]
  const operators = ["系统管理员", "财务部门", "李会计", "王出纳", "张经理"]
  const remarks = ["月度充值", "季度充值", "年度充值", "充值备注 1", "充值备注 2", "充值成功", "充值处理中", "", "-"]

  return Array.from({ length: count }).map((_, index) => {
    // 生成随机日期，最近3个月内
    const date = new Date()
    date.setMonth(date.getMonth() - Math.floor(Math.random() * 3))
    date.setDate(Math.floor(Math.random() * 28) + 1)
    date.setHours(Math.floor(Math.random() * 24))
    date.setMinutes(Math.floor(Math.random() * 60))

    return {
      id: `CZ${String(7762700000 + index).padStart(10, "0")}`,
      amount: Number.parseFloat((Math.random() * 5000 + 100).toFixed(2)),
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      date: format(date, "yyyy-MM-dd HH:mm:ss"),
      operator: operators[Math.floor(Math.random() * operators.length)],
      remark: remarks[Math.floor(Math.random() * remarks.length)],
    }
  })
}

// 导出为CSV
const exportToCSV = (data: RechargeRecord[]) => {
  const headers = ["充值单号", "充值金额", "支付方式", "充值日期", "操作人", "备注"]
  const csvContent = [
    headers.join(","),
    ...data.map((record) =>
      [
        record.id,
        record.amount,
        record.paymentMethod,
        record.date,
        record.operator,
        `"${record.remark.replace(/"/g, '""')}"`,
      ].join(","),
    ),
  ].join("\n")

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.setAttribute("href", url)
  link.setAttribute("download", `充值记录_${format(new Date(), "yyyyMMdd")}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export default function RechargeRecords() {
  // 生成100条演示数据
  const allRecords = generateDemoData(100)

  const [records, setRecords] = useState<RechargeRecord[]>(allRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })

  // 定义日期选择器的类型
  type DateRange = {
    from: Date | undefined
    to?: Date | undefined
  }

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const recordsPerPage = 10
  const totalPages = Math.ceil(records.length / recordsPerPage)

  // 当前页的记录
  const currentRecords = records.slice((currentPage - 1) * recordsPerPage, currentPage * recordsPerPage)

  // 筛选记录
  useEffect(() => {
    let filtered = allRecords

    // 文字搜索
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (record) =>
          record.id.toLowerCase().includes(term) ||
          record.paymentMethod.toLowerCase().includes(term) ||
          record.operator.toLowerCase().includes(term) ||
          record.remark.toLowerCase().includes(term),
      )
    }

    // 日期范围筛选
    if (dateRange.from || dateRange.to) {
      filtered = filtered.filter((record) => {
        const recordDate = new Date(record.date)

        if (dateRange.from && dateRange.to) {
          // 设置结束日期为当天的23:59:59
          const endDate = new Date(dateRange.to)
          endDate.setHours(23, 59, 59)
          return recordDate >= dateRange.from && recordDate <= endDate
        }

        if (dateRange.from) {
          return recordDate >= dateRange.from
        }

        if (dateRange.to) {
          // 设置结束日期为当天的23:59:59
          const endDate = new Date(dateRange.to)
          endDate.setHours(23, 59, 59)
          return recordDate <= endDate
        }

        return true
      })
    }

    setRecords(filtered)
    setCurrentPage(1) // 重置到第一页
  }, [searchTerm, dateRange, allRecords])

  // 清除日期筛选
  const clearDateFilter = () => {
    setDateRange({ from: undefined, to: undefined })
  }

  return (
    <div className="space-y-4">
      <div className="text-muted-foreground text-sm">查看客户账户的充值和扣费记录</div>

      <div className="flex flex-col sm:flex-row justify-between gap-4">
        {/* 搜索框 */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索充值记录..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          {/* 日期范围选择 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "justify-start text-left font-normal",
                  (dateRange.from || dateRange.to) && "text-primary",
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from || dateRange.to ? (
                  <>
                    {dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : "开始日期"}
                    {" 至 "}
                    {dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : "结束日期"}
                  </>
                ) : (
                  "选择日期范围"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={(value) => {
                  if (value) {
                    setDateRange({
                      from: value.from,
                      to: value.to || undefined
                    })
                  }
                }}
                numberOfMonths={2}
                locale={zhCN}
              />
              <div className="flex justify-end p-2 border-t">
                <Button variant="ghost" size="sm" onClick={clearDateFilter} disabled={!dateRange.from && !dateRange.to}>
                  清除
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* 导出按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                <Download className="mr-2 h-4 w-4" />
                导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => exportToCSV(records)}>导出为CSV</DropdownMenuItem>
              <DropdownMenuItem onClick={() => alert("Excel导出功能开发中...")}>导出为Excel</DropdownMenuItem>
              <DropdownMenuItem onClick={() => alert("PDF导出功能开发中...")}>导出为PDF</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 充值记录表格 */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>充值单号</TableHead>
              <TableHead>充值金额</TableHead>
              <TableHead>支付方式</TableHead>
              <TableHead>充值日期</TableHead>
              <TableHead>操作人</TableHead>
              <TableHead>备注</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentRecords.length > 0 ? (
              currentRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.id}</TableCell>
                  <TableCell>¥{record.amount.toFixed(2)}</TableCell>
                  <TableCell>{record.paymentMethod}</TableCell>
                  <TableCell>{record.date}</TableCell>
                  <TableCell>{record.operator}</TableCell>
                  <TableCell>{record.remark || "-"}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6">
                  没有找到匹配的充值记录
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页控件 */}
      {records.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            共 {records.length} 条记录，第 {currentPage} 页 / 共 {totalPages} 页
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            {totalPages > 7 ? (
              <>
                {[...Array(Math.min(5, totalPages))].map((_, i) => {
                  let pageNum: number

                  if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  if (pageNum > 0 && pageNum <= totalPages) {
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    )
                  }
                  return null
                })}
                {currentPage < totalPages - 2 && <span className="mx-1">...</span>}
                {currentPage < totalPages - 1 && (
                  <Button variant="outline" size="sm" onClick={() => setCurrentPage(totalPages)}>
                    {totalPages}
                  </Button>
                )}
              </>
            ) : (
              [...Array(totalPages)].map((_, i) => (
                <Button
                  key={i + 1}
                  variant={currentPage === i + 1 ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(i + 1)}
                >
                  {i + 1}
                </Button>
              ))
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              下一页
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

