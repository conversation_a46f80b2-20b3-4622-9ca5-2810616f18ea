"use client"

import Link from "next/link"
import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Building, User, Mail, Phone, Wallet, Percent, CalendarIcon, CreditCard, Clock, FileCheck, KeyRound, Loader2, MapPin, Send } from "lucide-react"
import { ResetPasswordDialog } from "@/components/reset-password-dialog"
import { toast } from "@/components/ui/use-toast"
import ClientRechargeRecords from "./client-recharge-records"
import { VerificationDetails } from "@/app/components/customer/verification-details"
import { getProvinceName, getCityName, getDistrictName } from "@/app/lib/data/area-data-adapter"
import { formatDateString } from "@/lib/utils"

interface CustomerDetailContentProps {
  customer: {
    id: string
    name: string
    loginName: string
    email: string
    phone: string
    accountType: string
    status: string
    createdAt: string
    industry: string
    address: string
    province?: string
    city?: string
    district?: string
    description: string
    balance: number
    creditLimit: number
    feeRate: string
    disabledAt?: string
    disableReason?: string
    personalVerification?: 'pending' | 'approved' | 'rejected'
    enterpriseVerification?: 'pending' | 'approved' | 'rejected'
  }
  defaultTab?: string
}

export function CustomerDetailContent({ customer, defaultTab = 'basic' }: CustomerDetailContentProps) {
  const router = useRouter()
  const [isResettingPassword, setIsResettingPassword] = useState(false)
  const [showResetPasswordDialog, setShowResetPasswordDialog] = useState(false)
  const [isEnablingAccount, setIsEnablingAccount] = useState(false)
  const [isProcessingEmails, setIsProcessingEmails] = useState(false)

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    let badgeClassName = ""
    if (status === "active") {
      badgeClassName = "bg-green-500 hover:bg-green-600"
    } else if (status === "inactive") {
      badgeClassName = "bg-red-500 hover:bg-red-600"
    } else if (status === "pending") {
      badgeClassName = "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300"
    } else {
      badgeClassName = "bg-secondary"
    }

    return (
      <Badge className={badgeClassName}>
        {status === "active" ? "启用" : status === "inactive" ? "停用" : status === "pending" ? "待审核" : "未知"}
      </Badge>
    )
  }

  // 重置密码
  const handleResetPassword = () => {
    setShowResetPasswordDialog(true)
  }

  // 处理重置密码成功
  const handleResetPasswordSuccess = (newPassword: string) => {
    // 创建一个可选择的密码框
    const passwordElement = document.createElement('textarea')
    passwordElement.value = newPassword
    passwordElement.setAttribute('readonly', '')
    passwordElement.style.position = 'absolute'
    passwordElement.style.left = '-9999px'
    document.body.appendChild(passwordElement)
    passwordElement.select()
    document.execCommand('copy')
    document.body.removeChild(passwordElement)

    toast({
      title: "密码重置成功",
      description: `新密码已复制到剪贴板并通过邮件发送给用户，临时密码为: ${newPassword}`,
      variant: "success"
    })
  }

  // 启用账户
  const handleEnableAccount = async () => {
    if (isEnablingAccount) return

    try {
      setIsEnablingAccount(true)

      const response = await fetch(`/api/customers/${customer.id}/enable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '启用账户失败')
      }

      toast({
        title: "账户已启用",
        description: "用户账户已成功启用，用户现在可以正常登录系统。",
        variant: "success"
      })

      // 刷新页面以显示最新状态
      router.refresh()
    } catch (error) {
      console.error('启用账户错误:', error)
      toast({
        title: "启用账户失败",
        description: error instanceof Error ? error.message : "未知错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setIsEnablingAccount(false)
    }
  }

  // 处理邮件队列
  const handleProcessEmails = async () => {
    if (isProcessingEmails) return

    try {
      setIsProcessingEmails(true)

      const response = await fetch('/api/email-processor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '处理邮件失败')
      }

      const data = await response.json()

      toast({
        title: "邮件处理完成",
        description: `已处理 ${data.processed || 0} 封邮件，成功 ${data.success || 0} 封，失败 ${data.failed || 0} 封。`,
        variant: "success"
      })
    } catch (error) {
      console.error('处理邮件错误:', error)
      toast({
        title: "处理邮件失败",
        description: error instanceof Error ? error.message : "未知错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setIsProcessingEmails(false)
    }
  }

  // 获取账户类型标签
  const getAccountTypeBadge = (type: string) => {
    switch (type) {
      case "enterprise":
        return <Badge className="bg-blue-500 hover:bg-blue-600">企业用户</Badge>
      case "personal":
        return <Badge className="bg-purple-500 hover:bg-purple-600">个人用户</Badge>
      default:
        return <Badge variant="secondary">未知类型</Badge>
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={`查看 ${customer.name} 的用户信息`}>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/accounts/customer">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/accounts/customer/${customer.loginName || customer.id}/edit`}>编辑</Link>
          </Button>
          <Button
            variant="outline"
            className="border-blue-300 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
            onClick={handleResetPassword}
            disabled={isResettingPassword}
          >
            {isResettingPassword ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                重置中...
              </>
            ) : (
              <>
                <KeyRound className="mr-2 h-4 w-4" />
                重置密码
              </>
            )}
          </Button>
          <Button
            variant="outline"
            className="border-purple-300 text-purple-600 hover:bg-purple-50 hover:text-purple-700"
            onClick={handleProcessEmails}
            disabled={isProcessingEmails}
          >
            {isProcessingEmails ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                处理邮件队列
              </>
            )}
          </Button>
          {customer.status === "active" ? (
            <Button asChild variant="destructive">
              <Link href={`/accounts/customer/${customer.loginName || customer.id}/disable`}>停用用户</Link>
            </Button>
          ) : customer.status === "inactive" && (
            <Button
              variant="default"
              className="bg-green-600 hover:bg-green-700 text-white"
              onClick={handleEnableAccount}
              disabled={isEnablingAccount}
            >
              {isEnablingAccount ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  启用中...
                </>
              ) : (
                "启用用户"
              )}
            </Button>
          )}
        </div>
      </DashboardHeader>

      <Tabs defaultValue={defaultTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="finance">财务信息</TabsTrigger>
          <TabsTrigger value="records">余额记录</TabsTrigger>
          <TabsTrigger value="verification" className="flex items-center">
            <FileCheck className="h-4 w-4 mr-2" />
            认证资料
          </TabsTrigger>
        </TabsList>

        {/* 基本信息 */}
        <TabsContent value="basic">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>查看客户账户的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-1">
                  <Label className="text-muted-foreground">账户名称</Label>
                  <div className="font-medium flex items-center">
                    <Building className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.name}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">登录名称</Label>
                  <div className="font-medium flex items-center">
                    <User className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.loginName}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">邮箱</Label>
                  <div className="font-medium flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.email}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">手机号</Label>
                  <div className="font-medium flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.phone}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">账户类型</Label>
                  <div className="font-medium">{getAccountTypeBadge(customer.accountType)}</div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">状态</Label>
                  <div className="font-medium">{getStatusBadge(customer.status)}</div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">个人认证状态</Label>
                  <div className="font-medium">
                    {customer.personalVerification ? (
                      customer.personalVerification === 'approved' ? (
                        <Badge className="bg-green-500 hover:bg-green-600">已认证</Badge>
                      ) : customer.personalVerification === 'rejected' ? (
                        <Badge className="bg-red-500 hover:bg-red-600">已拒绝</Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300">审核中</Badge>
                      )
                    ) : (
                      <Badge variant="outline">未提交</Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">企业认证状态</Label>
                  <div className="font-medium">
                    {customer.enterpriseVerification ? (
                      customer.enterpriseVerification === 'approved' ? (
                        <Badge className="bg-green-500 hover:bg-green-600">已认证</Badge>
                      ) : customer.enterpriseVerification === 'rejected' ? (
                        <Badge className="bg-red-500 hover:bg-red-600">已拒绝</Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300">审核中</Badge>
                      )
                    ) : (
                      <Badge variant="outline">未提交</Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">启用时间</Label>
                  <div className="font-medium flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2 text-blue-500" />
                    {formatDateString(customer.createdAt)}
                  </div>
                </div>

                {customer.status === "inactive" && customer.disabledAt && (
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">停用时间</Label>
                    <div className="font-medium flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-2 text-red-500" />
                      {formatDateString(customer.disabledAt)}
                    </div>
                  </div>
                )}

                <div className="space-y-1">
                  <Label className="text-muted-foreground">行业</Label>
                  <div className="font-medium">{customer.industry}</div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">地址</Label>
                  <div className="font-medium flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.province && getProvinceName(customer.province)}{' '}
                    {customer.city && getCityName(customer.province || '', customer.city)}{' '}
                    {customer.district && getDistrictName(customer.city || '', customer.district)}{' '}
                    {customer.address}
                  </div>
                </div>
              </div>

              {customer.status === "inactive" && customer.disableReason && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
                  <h4 className="text-sm font-medium text-red-800 mb-1">停用原因</h4>
                  <p className="text-sm text-red-700">{customer.disableReason}</p>
                </div>
              )}

              <div className="mt-6">
                <Label className="text-muted-foreground">描述</Label>
                <p className="mt-1">{customer.description}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 财务信息 */}
        <TabsContent value="finance">
          <Card>
            <CardHeader>
              <CardTitle>财务信息</CardTitle>
              <CardDescription>查看客户账户的财务相关信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="text-sm font-medium text-green-800 mb-1 flex items-center">
                    <Wallet className="h-4 w-4 mr-2" />
                    账户余额
                  </h4>
                  <p className="text-2xl font-bold text-green-700">¥{(customer.balance || 0).toLocaleString()}</p>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="text-sm font-medium text-blue-800 mb-1 flex items-center">
                    <CreditCard className="h-4 w-4 mr-2" />
                    授信额度
                  </h4>
                  <p className="text-2xl font-bold text-blue-700">¥{(customer.creditLimit || 0).toLocaleString()}</p>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">费率</Label>
                  <div className="font-medium flex items-center">
                    <Percent className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.feeRate}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-muted-foreground">最近活动时间</Label>
                  <div className="font-medium flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-blue-500" />
                    {customer.status === "inactive" ? formatDateString(customer.disabledAt) : formatDateString("2023-07-01 15:30:00")}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 余额记录 */}
        <TabsContent value="records">
          <Card>
            <CardHeader>
              <CardTitle>余额记录</CardTitle>
              <CardDescription>查看客户账户的充值、扣费和授信额度变动记录</CardDescription>
            </CardHeader>
            <CardContent>
              <ClientRechargeRecords customerId={customer.id} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 认证资料 */}
        <TabsContent value="verification">
          <Card>
            <CardHeader>
              <CardTitle>认证资料</CardTitle>
              <CardDescription>查看和审核用户提交的认证资料</CardDescription>
            </CardHeader>
            <CardContent>
              <VerificationDetails userId={customer.id} userName={customer.name || customer.loginName} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 重置密码对话框 */}
      <ResetPasswordDialog
        userId={customer.id}
        username={customer.name || customer.loginName}
        isOpen={showResetPasswordDialog}
        onClose={() => setShowResetPasswordDialog(false)}
        onSuccess={handleResetPasswordSuccess}
      />
    </DashboardShell>
  )
}