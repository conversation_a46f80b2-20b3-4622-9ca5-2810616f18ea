"use client"

import logger from '@/lib/utils/logger';

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, AlertTriangle, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { toast } from "@/components/ui/use-toast"

// 定义客户类型
interface Customer {
  id: string;
  name: string;
  loginName: string;
  email: string;
  status: string;
}

export default function CustomerDisablePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const customerId = params.id

  // 状态
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [disableReason, setDisableReason] = useState("")
  const [showAlert, setShowAlert] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // 获取客户数据
  useEffect(() => {
    async function fetchCustomer() {
      try {
        setLoading(true)
        const response = await fetch(`/api/customers/${customerId}`)

        if (!response.ok) {
          throw new Error('获取客户信息失败')
        }

        const data = await response.json()

        if (data.success) {
          setCustomer(data.data)
        } else {
          throw new Error(data.message || '获取客户信息失败')
        }
      } catch (error) {
        logger.error('获取客户信息错误:', error)
        toast({
          title: "获取客户信息失败",
          description: error instanceof Error ? error.message : '获取客户信息时发生错误',
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchCustomer()
  }, [customerId])

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="加载客户信息">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">正在加载客户信息...</p>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  // 如果找不到客户，显示错误信息
  if (!customer && !loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="用户不存在">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground mb-4">未找到ID为 {customerId} 的用户信息</p>
            <Button onClick={() => router.push("/accounts/customer")}>返回用户列表</Button>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }



  // 确认停用账户
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!disableReason.trim()) {
      setShowAlert(true)
      return
    } else {
      setShowAlert(false)
    }

    try {
      setSubmitting(true)

      // 确保使用用户的实际ID而不是用户名
      const userId = customer?.id;
      logger.log('使用用户ID:', userId);

      if (!userId) {
        throw new Error('无法获取用户ID，请刷新页面重试');
      }

      // 调用API停用用户
      const response = await fetch(`/api/customers/${userId}/disable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: disableReason })
      })

      if (!response.ok) {
        throw new Error('停用用户失败')
      }

      const data = await response.json()

      if (data.success) {
        toast({
          title: "停用成功",
          description: `已成功停用 ${customer?.name || customerId} 的账户`,
          variant: "success"
        })
        router.push(`/accounts/customer/${customerId}`)
      } else {
        throw new Error(data.message || '停用用户失败')
      }
    } catch (error) {
      console.error('停用用户错误:', error)
      toast({
        title: "停用失败",
        description: error instanceof Error ? error.message : '停用用户时发生错误',
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={`停用 ${customer?.name || customerId} 的用户`}>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
      </DashboardHeader>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>停用用户</CardTitle>
            <CardDescription>请确认您要停用此用户，并提供停用原因</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>警告：停用用户后，该用户将无法登录系统和使用相关服务。</AlertDescription>
            </Alert>

            {showAlert && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>请输入停用原因。</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="disableReason" className="font-medium">
                停用原因 <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="disableReason"
                value={disableReason}
                onChange={(e) => setDisableReason(e.target.value)}
                className="min-h-[100px]"
                placeholder="请输入停用该账户的原因..."
                required
              />
              <p className="text-sm text-muted-foreground">请详细说明停用原因，该信息将被记录在系统中。</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              取消
            </Button>
            <Button type="submit" variant="destructive" disabled={submitting}>
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  正在停用...
                </>
              ) : (
                "确认停用"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </DashboardShell>
  )
}

