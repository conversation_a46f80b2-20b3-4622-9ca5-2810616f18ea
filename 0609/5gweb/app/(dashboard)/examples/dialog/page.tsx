'use client'

import DialogExample from '@/app/components/examples/dialog-example'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

// 注意：metadata需要在服务器组件中定义，不能在'use client'组件中定义
// 这个页面的元数据应该在layout.tsx中定义

export default function DialogExamplePage() {
  return (
    <div className="container mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">对话框示例</h1>
        <p className="text-gray-500">
          展示如何使用对话框工具函数替代原生 alert 函数，提供更好的用户体验
        </p>
      </div>

      <div className="grid gap-8">
        <DialogExample />

        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
            <CardDescription>如何在项目中使用对话框工具函数</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">1. 导入对话框工具函数</h3>
                <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  <code>{`import dialog from '@/lib/utils/dialog'`}</code>
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">2. 使用对话框工具函数</h3>
                <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  <code>{`// 显示普通提示信息
dialog.showMessage('这是一个提示信息')

// 显示成功信息
dialog.showSuccess('操作成功！')

// 显示错误信息
dialog.showError('操作失败：网络连接错误')`}</code>
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">3. 替换原生 alert 函数</h3>
                <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  <code>{`// 替换前
alert('操作成功！')

// 替换后
dialog.showSuccess('操作成功！')`}</code>
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
