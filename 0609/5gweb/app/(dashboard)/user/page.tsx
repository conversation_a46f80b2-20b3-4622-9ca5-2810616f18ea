"use client"

import { useState, useEffect } from "react"
import "./process-polyfill"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ProfileForm } from "./components/ProfileForm"
import { PasswordForm } from "./components/PasswordForm"
import { VerificationForm } from "./components/VerificationForm"
import { NotificationSettings } from "./components/NotificationSettings"
import { LoginHistory } from "./components/LoginHistory"
import { BalanceTransactions } from "./components/BalanceTransactions"
import { useSearchParams, useRouter } from "next/navigation"
import { UserHeader } from "./components/UserHeader"
import { UserShell } from "./components/UserShell"
import dynamic from "next/dynamic"

// 动态导入费率查询组件
const RatesInfo = dynamic(() => import("../user/rates/page"), { ssr: false })

export default function UserProfilePage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("profile")
  // 不再需要从useProfile中获取方法

  // 注意: 用户资料获取已移动到各个组件中
  // 这里不再需要获取用户资料

  // 处理URL参数中的tab
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // 处理标签切换
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    router.push(`/user?tab=${value}`, { scroll: false })
  }

  return (
    <div className="container mx-auto px-4 overflow-hidden">
      <UserShell className="bg-gradient-to-br from-white via-blue-50 to-blue-100 p-6 rounded-xl shadow-sm border border-blue-100 overflow-hidden">
      <div className="bg-white bg-opacity-70 p-4 rounded-lg shadow-sm border border-blue-100 mb-6">
        <UserHeader heading="个人中心" text="管理您的个人资料和账户设置" />
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full overflow-hidden">
        <TabsList className="grid sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2 mb-8 p-1 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg w-full max-w-full overflow-hidden shadow-inner border border-blue-200">
          <TabsTrigger
            value="profile"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-blue-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-blue-500 hover:bg-blue-50 transition-all duration-200"
          >
            个人资料
          </TabsTrigger>
          <TabsTrigger
            value="password"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-indigo-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-indigo-500 hover:bg-blue-50 transition-all duration-200"
          >
            修改密码
          </TabsTrigger>
          <TabsTrigger
            value="verification"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-purple-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-purple-500 hover:bg-blue-50 transition-all duration-200"
          >
            认证资料
          </TabsTrigger>
          <TabsTrigger
            value="notification"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-cyan-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-cyan-500 hover:bg-blue-50 transition-all duration-200"
          >
            通知设置
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-teal-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-teal-500 hover:bg-blue-50 transition-all duration-200"
          >
            登录历史
          </TabsTrigger>
          <TabsTrigger
            value="balance"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-green-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-green-500 hover:bg-blue-50 transition-all duration-200"
          >
            余额记录
          </TabsTrigger>
          <TabsTrigger
            value="rates"
            className="data-[state=active]:bg-gradient-to-b data-[state=active]:from-white data-[state=active]:to-blue-50 data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-t-2 data-[state=active]:border-orange-500 hover:bg-blue-50 transition-all duration-200"
          >
            费率查询
          </TabsTrigger>
        </TabsList>

        <div className="bg-white bg-opacity-90 p-6 rounded-xl shadow-md border border-blue-100 overflow-hidden mx-auto w-full max-w-5xl">
          <TabsContent value="profile" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <ProfileForm />
          </TabsContent>

          <TabsContent value="password" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <PasswordForm />
          </TabsContent>

          <TabsContent value="verification" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <VerificationForm />
          </TabsContent>

          <TabsContent value="notification" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <NotificationSettings />
          </TabsContent>

          <TabsContent value="history" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <LoginHistory />
          </TabsContent>

          <TabsContent value="balance" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <BalanceTransactions />
          </TabsContent>

          <TabsContent value="rates" className="mx-auto w-full animate-in fade-in-50 duration-300 overflow-hidden">
            <RatesInfo />
          </TabsContent>
        </div>
      </Tabs>
    </UserShell>
    </div>
  )
}
