"use client"

import { useState, useEffect } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { HelpCircle } from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface Rate {
  id: string
  amount: number
  period: number | null
  billingIncrement: string
  billingIncrementName: string
  billingUnitName: string
  billingUnitSeconds: number
  businessType: string
  createdAt: string
  updatedAt: string
}

export default function UserRatesPage() {
  const [rates, setRates] = useState<Rate[]>([])
  const [loading, setLoading] = useState(true)

  // 获取用户费率
  useEffect(() => {
    const fetchRates = async () => {
      try {
        setLoading(true)
        const response = await fetch("/api/user/rates")
        const data = await response.json()

        if (data.success) {
          setRates(data.data)
        } else {
          toast({
            title: "获取费率失败",
            description: data.message,
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error("获取费率错误:", error)
        toast({
          title: "获取费率失败",
          description: "请稍后重试",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchRates()
  }, [])

  // 格式化业务类型
  const formatBusinessType = (type: string) => {
    switch (type) {
      case "VIDEO_CALL":
        return "视频通话"
      case "AUDIO_CALL":
        return "语音通话"
      case "VIDEO_MESSAGE":
        return "视频短信"
      case "TEXT_MESSAGE":
        return "文本短信"
      default:
        return type
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // 计算每分钟费用（用于比较不同计费周期的实际费用）
  const calculatePerMinuteCost = (rate: Rate) => {
    if (rate.billingIncrement === "PER_MINUTE") {
      return rate.amount
    } else if (rate.billingIncrement === "PER_SIX_SECOND") {
      return rate.amount * 10 // 一分钟有10个6秒
    }
    return rate.amount
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="我的费率"
        text="查看您当前的费率信息和计费规则"
      />

      <Card>
        <CardHeader>
          <CardTitle>费率信息</CardTitle>
          <CardDescription>
            以下是您当前的费率信息，不同业务类型可能有不同的费率和计费规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : rates.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              暂无费率信息
            </div>
          ) : (
            <div className="space-y-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>业务类型</TableHead>
                    <TableHead>费率</TableHead>
                    <TableHead>计费周期</TableHead>
                    <TableHead>
                      <div className="flex items-center">
                        每分钟费用
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-1 h-4 w-4 text-muted-foreground cursor-pointer" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs">
                                此列显示不同计费周期下的每分钟实际费用，便于比较。
                                例如：按6秒计费的费率为0.12元/单位，每分钟实际费用为1.2元。
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </TableHead>
                    <TableHead>更新时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rates.map((rate) => (
                    <TableRow key={rate.id}>
                      <TableCell>
                        <Badge variant="outline">{formatBusinessType(rate.businessType)}</Badge>
                      </TableCell>
                      <TableCell>
                        {rate.amount.toFixed(2)}元/{rate.billingUnitName}
                      </TableCell>
                      <TableCell>{rate.billingIncrementName}</TableCell>
                      <TableCell>{calculatePerMinuteCost(rate).toFixed(2)}元/分钟</TableCell>
                      <TableCell>{formatDate(rate.updatedAt)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="bg-muted p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-2">计费说明</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 按分钟计费：通话时长不足60秒的部分，按60秒计算。</li>
                  <li>• 按6秒计费：通话时长不足6秒的部分，按6秒计算。</li>
                  <li>• 计费精度：系统默认使用1秒精度进行计费。</li>
                  <li>• 费用计算：系统根据通话时长、适用的费率和计费周期类型计算费用，并从余额中扣减。</li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  )
}
