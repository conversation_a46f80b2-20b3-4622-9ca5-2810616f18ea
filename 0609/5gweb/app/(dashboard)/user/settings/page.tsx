"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Lock, Globe, Clock, Shield, BellRing, UserCog } from "lucide-react"

export default function AccountSettingsPage() {
  const [language, setLanguage] = useState("zh_CN")
  const [timezone, setTimezone] = useState("Asia/Shanghai")
  const [dateFormat, setDateFormat] = useState("YYYY-MM-DD")
  const [timeFormat, setTimeFormat] = useState("24h")
  const [loginAlerts, setLoginAlerts] = useState(true)
  const [twoFactorAuth, setTwoFactorAuth] = useState(false)
  const [autoLogout, setAutoLogout] = useState(30)
  const [darkMode, setDarkMode] = useState(false)
  const [highContrast, setHighContrast] = useState(false)
  const [fontSize, setFontSize] = useState("medium")
  const [sessionTimeout, setSessionTimeout] = useState("30")

  const handleSavePreferences = () => {
    toast({
      title: "偏好设置已保存",
      description: "您的账户偏好设置已成功更新",
      variant: "success",
    })
  }

  const handleSaveSecurity = () => {
    toast({
      title: "安全设置已保存",
      description: "您的账户安全设置已成功更新",
      variant: "success",
    })
  }

  const handleSaveAccessibility = () => {
    toast({
      title: "无障碍设置已保存",
      description: "您的无障碍设置已成功更新",
      variant: "success",
    })
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="账户设置" text="管理您的账户偏好和安全设置" />

      <Tabs defaultValue="preferences" className="mt-6">
        <TabsList className="grid w-full grid-cols-3 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 p-1 rounded-lg">
          <TabsTrigger
            value="preferences"
            className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-300 rounded-md transition-all"
          >
            <Globe className="mr-2 h-4 w-4" />
            偏好设置
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:text-green-600 dark:data-[state=active]:text-green-300 rounded-md transition-all"
          >
            <Lock className="mr-2 h-4 w-4" />
            安全设置
          </TabsTrigger>
          <TabsTrigger
            value="accessibility"
            className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:text-purple-600 dark:data-[state=active]:text-purple-300 rounded-md transition-all"
          >
            <UserCog className="mr-2 h-4 w-4" />
            无障碍设置
          </TabsTrigger>
        </TabsList>

        {/* 偏好设置 */}
        <TabsContent value="preferences">
          <Card className="border-blue-200 dark:border-blue-800 shadow-md">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 rounded-t-lg border-b border-blue-100 dark:border-blue-800">
              <CardTitle className="text-blue-700 dark:text-blue-300 flex items-center">
                <Globe className="mr-2 h-5 w-5" />
                偏好设置
              </CardTitle>
              <CardDescription className="text-blue-600 dark:text-blue-400">
                自定义您的语言、时区和日期格式等偏好设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="language" className="text-blue-700 dark:text-blue-300">
                    语言
                  </Label>
                  <select
                    id="language"
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="w-full rounded-md border border-blue-200 dark:border-blue-800 bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  >
                    <option value="zh_CN">简体中文</option>
                    <option value="zh_TW">繁體中文</option>
                    <option value="en_US">English (US)</option>
                    <option value="ja_JP">日本語</option>
                    <option value="ko_KR">한국어</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone" className="text-blue-700 dark:text-blue-300">
                    时区
                  </Label>
                  <select
                    id="timezone"
                    value={timezone}
                    onChange={(e) => setTimezone(e.target.value)}
                    className="w-full rounded-md border border-blue-200 dark:border-blue-800 bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  >
                    <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                    <option value="Asia/Hong_Kong">香港时间 (UTC+8)</option>
                    <option value="Asia/Taipei">台北时间 (UTC+8)</option>
                    <option value="Asia/Tokyo">东京时间 (UTC+9)</option>
                    <option value="America/New_York">纽约时间 (UTC-5/UTC-4)</option>
                    <option value="Europe/London">伦敦时间 (UTC+0/UTC+1)</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-format" className="text-blue-700 dark:text-blue-300">
                    日期格式
                  </Label>
                  <select
                    id="date-format"
                    value={dateFormat}
                    onChange={(e) => setDateFormat(e.target.value)}
                    className="w-full rounded-md border border-blue-200 dark:border-blue-800 bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  >
                    <option value="YYYY-MM-DD">YYYY-MM-DD (2023-07-15)</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY (15/07/2023)</option>
                    <option value="MM/DD/YYYY">MM/DD/YYYY (07/15/2023)</option>
                    <option value="YYYY年MM月DD日">YYYY年MM月DD日 (2023年07月15日)</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time-format" className="text-blue-700 dark:text-blue-300">
                    时间格式
                  </Label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="time-format"
                        value="24h"
                        checked={timeFormat === "24h"}
                        onChange={() => setTimeFormat("24h")}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">24小时制 (14:30)</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="time-format"
                        value="12h"
                        checked={timeFormat === "12h"}
                        onChange={() => setTimeFormat("12h")}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">12小时制 (2:30 PM)</span>
                    </label>
                  </div>
                </div>
              </div>

              <Separator className="bg-blue-100 dark:bg-blue-800" />

              <div className="space-y-2">
                <Label htmlFor="session-timeout" className="text-blue-700 dark:text-blue-300 flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  会话超时时间（分钟）
                </Label>
                <div className="flex items-center space-x-4">
                  <Input
                    id="session-timeout"
                    type="number"
                    min="5"
                    max="120"
                    value={sessionTimeout}
                    onChange={(e) => setSessionTimeout(e.target.value)}
                    className="w-24 border-blue-200 dark:border-blue-800 focus-visible:ring-blue-500"
                  />
                  <span className="text-sm text-blue-600 dark:text-blue-400">设置为0表示永不超时（不推荐）</span>
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">超时后，您将需要重新登录系统</p>
              </div>
            </CardContent>
            <CardFooter className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 rounded-b-lg border-t border-blue-100 dark:border-blue-800">
              <Button onClick={handleSavePreferences} className="bg-blue-600 hover:bg-blue-700 text-white">
                保存偏好设置
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* 安全设置 */}
        <TabsContent value="security">
          <Card className="border-green-200 dark:border-green-800 shadow-md">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 rounded-t-lg border-b border-green-100 dark:border-green-800">
              <CardTitle className="text-green-700 dark:text-green-300 flex items-center">
                <Lock className="mr-2 h-5 w-5" />
                安全设置
              </CardTitle>
              <CardDescription className="text-green-600 dark:text-green-400">
                管理您的账户安全选项和登录设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="flex items-center justify-between p-4 rounded-lg bg-green-50 dark:bg-green-900/30 border border-green-100 dark:border-green-800">
                <div className="space-y-0.5">
                  <div className="flex items-center">
                    <BellRing className="mr-2 h-4 w-4 text-green-600 dark:text-green-400" />
                    <Label htmlFor="login-alerts" className="text-green-700 dark:text-green-300">
                      登录提醒
                    </Label>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    当有新设备登录您的账户时，发送电子邮件通知
                  </p>
                </div>
                <Switch
                  id="login-alerts"
                  checked={loginAlerts}
                  onCheckedChange={setLoginAlerts}
                  className="data-[state=checked]:bg-green-600"
                />
              </div>

              <Separator className="bg-green-100 dark:bg-green-800" />

              <div className="flex items-center justify-between p-4 rounded-lg bg-green-50 dark:bg-green-900/30 border border-green-100 dark:border-green-800">
                <div className="space-y-0.5">
                  <div className="flex items-center">
                    <Shield className="mr-2 h-4 w-4 text-green-600 dark:text-green-400" />
                    <Label htmlFor="two-factor-auth" className="text-green-700 dark:text-green-300">
                      双因素认证
                    </Label>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    启用双因素认证，通过手机验证码增强账户安全
                  </p>
                </div>
                <Switch
                  id="two-factor-auth"
                  checked={twoFactorAuth}
                  onCheckedChange={setTwoFactorAuth}
                  className="data-[state=checked]:bg-green-600"
                />
              </div>

              <Separator className="bg-green-100 dark:bg-green-800" />

              <div className="space-y-2">
                <Label htmlFor="auto-logout" className="text-green-700 dark:text-green-300 flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-green-600 dark:text-green-400" />
                  自动登出时间（分钟）
                </Label>
                <div className="flex items-center space-x-4">
                  <Input
                    id="auto-logout"
                    type="number"
                    min="5"
                    max="120"
                    value={autoLogout}
                    onChange={(e) => setAutoLogout(Number.parseInt(e.target.value))}
                    className="w-24 border-green-200 dark:border-green-800 focus-visible:ring-green-500"
                  />
                  <span className="text-sm text-green-600 dark:text-green-400">设置为0表示永不自动登出（不推荐）</span>
                </div>
                <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                  在指定的不活动时间后，系统将自动登出您的账户
                </p>
              </div>
            </CardContent>
            <CardFooter className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 rounded-b-lg border-t border-green-100 dark:border-green-800">
              <Button onClick={handleSaveSecurity} className="bg-green-600 hover:bg-green-700 text-white">
                保存安全设置
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* 无障碍设置 */}
        <TabsContent value="accessibility">
          <Card className="border-purple-200 dark:border-purple-800 shadow-md">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950 dark:to-indigo-950 rounded-t-lg border-b border-purple-100 dark:border-purple-800">
              <CardTitle className="text-purple-700 dark:text-purple-300 flex items-center">
                <UserCog className="mr-2 h-5 w-5" />
                无障碍设置
              </CardTitle>
              <CardDescription className="text-purple-600 dark:text-purple-400">
                自定义界面外观和无障碍选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="flex items-center justify-between p-4 rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-100 dark:border-purple-800">
                <div className="space-y-0.5">
                  <Label htmlFor="dark-mode" className="text-purple-700 dark:text-purple-300">
                    深色模式
                  </Label>
                  <p className="text-sm text-purple-600 dark:text-purple-400">启用深色模式，减少眼睛疲劳</p>
                </div>
                <Switch
                  id="dark-mode"
                  checked={darkMode}
                  onCheckedChange={setDarkMode}
                  className="data-[state=checked]:bg-purple-600"
                />
              </div>

              <Separator className="bg-purple-100 dark:bg-purple-800" />

              <div className="flex items-center justify-between p-4 rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-100 dark:border-purple-800">
                <div className="space-y-0.5">
                  <Label htmlFor="high-contrast" className="text-purple-700 dark:text-purple-300">
                    高对比度模式
                  </Label>
                  <p className="text-sm text-purple-600 dark:text-purple-400">增强界面对比度，提高可读性</p>
                </div>
                <Switch
                  id="high-contrast"
                  checked={highContrast}
                  onCheckedChange={setHighContrast}
                  className="data-[state=checked]:bg-purple-600"
                />
              </div>

              <Separator className="bg-purple-100 dark:bg-purple-800" />

              <div className="space-y-2">
                <Label htmlFor="font-size" className="text-purple-700 dark:text-purple-300">
                  字体大小
                </Label>
                <select
                  id="font-size"
                  value={fontSize}
                  onChange={(e) => setFontSize(e.target.value)}
                  className="w-full rounded-md border border-purple-200 dark:border-purple-800 bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2"
                >
                  <option value="small">小</option>
                  <option value="medium">中</option>
                  <option value="large">大</option>
                  <option value="x-large">特大</option>
                </select>
                <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">调整界面文字大小，提高可读性</p>
              </div>
            </CardContent>
            <CardFooter className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950 dark:to-indigo-950 rounded-b-lg border-t border-purple-100 dark:border-purple-800">
              <Button onClick={handleSaveAccessibility} className="bg-purple-600 hover:bg-purple-700 text-white">
                保存无障碍设置
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

