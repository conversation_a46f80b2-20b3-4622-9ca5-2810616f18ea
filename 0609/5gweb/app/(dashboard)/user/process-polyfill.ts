"use client"

import logger from '@/lib/utils/logger';

// 为客户端提供 process polyfill
if (typeof window !== 'undefined') {
  try {
    // 检查是否已经存在 process 对象
    if (!window.process) {
      // @ts-ignore
      window.process = {
        env: {
          // 安全地获取 NODE_ENV，避免循环引用
          NODE_ENV: 'production',
        },
        browser: true,
        version: '',
        versions: {},
        nextTick: function(fn: Function) {
          setTimeout(fn, 0);
        },
      };

      logger.log('Process polyfill 已加载');
    }
  } catch (error) {
    logger.error('加载 process polyfill 失败:', error);
  }
}
