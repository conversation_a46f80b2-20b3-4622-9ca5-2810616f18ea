"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { HeaderActions } from "@/components/shared/header-actions"

interface UserHeaderProps {
  heading: string
  text?: string
  children?: React.ReactNode
  className?: string
}

export function UserHeader({ heading, text, children, className }: UserHeaderProps) {
  return (
    <div className={cn("flex items-center justify-between px-2", className)}>
      <div className="flex items-center">
        <div className="grid gap-1">
          <h1 className="font-heading text-3xl md:text-4xl">{heading}</h1>
          {text && <p className="text-lg text-muted-foreground">{text}</p>}
        </div>
      </div>
      <div className="flex items-center gap-4">
        {children}
        <HeaderActions />
      </div>
    </div>
  )
}
