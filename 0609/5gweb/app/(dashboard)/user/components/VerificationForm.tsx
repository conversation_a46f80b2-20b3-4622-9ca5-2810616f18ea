"use client"

import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"
import { useVerification } from "../hooks/useVerification"

export function VerificationForm() {
  const {
    verificationType,
    setVerificationType,
    verificationStatus,
    verificationChange,
    verificationData,
    setVerificationData,
    uploadingFile,
    uploadProgress,
    submittingVerification,
    fetchVerificationData,
    uploadVerificationFile,
    uploadMultipleFiles,
    submitVerification
  } = useVerification()

  // 组件挂载时获取认证资料
  useEffect(() => {
    fetchVerificationData()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-4xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">认证资料</CardTitle>
        <CardDescription>提交您的个人或企业认证资料，享受更多服务权益</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="space-y-6">
          <div className="flex flex-col space-y-2">
            <Label>认证类型</Label>
            <div className="flex space-x-2">
              <Button
                variant={verificationType === "personal" ? "default" : "outline"}
                onClick={() => setVerificationType("personal")}
                className={verificationType === "personal" ? "bg-blue-600 hover:bg-blue-700" : ""}
                disabled={verificationStatus !== "none" && verificationType !== "personal"}
              >
                个人认证
              </Button>
              <Button
                variant={verificationType === "enterprise" ? "default" : "outline"}
                onClick={() => setVerificationType("enterprise")}
                className={verificationType === "enterprise" ? "bg-blue-600 hover:bg-blue-700" : ""}
                disabled={verificationStatus !== "none" && verificationType !== "enterprise"}
              >
                企业认证
              </Button>
            </div>
          </div>

          {/* 认证类型变更提醒 */}
          {verificationChange && (
            <Alert className="bg-amber-50 border-amber-200">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <AlertTitle className="text-amber-800">认证类型已变更</AlertTitle>
              <AlertDescription className="text-amber-700">
                <p>管理员已将您的认证类型从
                  <strong>{verificationChange.originalType === 'personal' ? '个人认证' : '企业认证'}</strong> 变更为
                  <strong>{verificationChange.newType === 'personal' ? '个人认证' : '企业认证'}</strong>。
                </p>
                <p className="mt-1">变更原因：{verificationChange.reason}</p>
                <p className="mt-1 font-semibold">请在 {new Date(verificationChange.deadline).toLocaleDateString('zh-CN')} 前补充相关资料，否则将恢复到原始认证状态。</p>
              </AlertDescription>
            </Alert>
          )}

          {/* 认证状态显示 */}
          {verificationStatus !== "none" && (
            <div className="rounded-lg border p-4 bg-gray-50">
              <div className="flex items-center space-x-2">
                <Label>认证状态：</Label>
                {verificationStatus === "pending" && (
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
                    {verificationType === "personal" ? "个人认证" : "企业认证"} 审核中
                  </Badge>
                )}
                {verificationStatus === "approved" && (
                  <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                    {verificationType === "personal" ? "个人认证" : "企业认证"} 已认证
                  </Badge>
                )}
                {verificationStatus === "rejected" && (
                  <div className="flex flex-col gap-1">
                    <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
                      {verificationType === "personal" ? "个人认证" : "企业认证"} 未通过
                    </Badge>
                    {verificationData.remark && (
                      <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
                        <span className="font-semibold">未通过原因：</span>
                        {verificationData.remark}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 个人认证表单 */}
          {verificationType === "personal" && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="realName" className="text-blue-700">真实姓名</Label>
                  <Input
                    id="realName"
                    placeholder="请输入您的真实姓名"
                    value={verificationData.realName}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      realName: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="idCardNumber" className="text-blue-700">身份证号码</Label>
                  <Input
                    id="idCardNumber"
                    placeholder="请输入您的身份证号码"
                    value={verificationData.idCardNumber}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      idCardNumber: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-blue-700">身份证正面照片</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer relative">
                  <input
                    type="file"
                    id="idCardFront"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        uploadVerificationFile(file, "idCardFront");
                      }
                    }}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification || uploadingFile}
                  />
                  {verificationData.idCardFront ? (
                    <div className="relative">
                      <img
                        src={verificationData.idCardFront}
                        alt="身份证正面"
                        className="max-h-40 mx-auto rounded-lg"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setVerificationData(prev => ({
                            ...prev,
                            idCardFront: ""
                          }));
                        }}
                        disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                      >
                        删除
                      </Button>
                    </div>
                  ) : (
                    <div className="py-4">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>点击上传身份证正面照片</span>
                        <span className="text-xs mt-1">支持 JPG、JPEG、PNG 格式，最大 5MB</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-blue-700">身份证反面照片</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer relative">
                  <input
                    type="file"
                    id="idCardBack"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        uploadVerificationFile(file, "idCardBack");
                      }
                    }}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification || uploadingFile}
                  />
                  {verificationData.idCardBack ? (
                    <div className="relative">
                      <img
                        src={verificationData.idCardBack}
                        alt="身份证反面"
                        className="max-h-40 mx-auto rounded-lg"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setVerificationData(prev => ({
                            ...prev,
                            idCardBack: ""
                          }));
                        }}
                        disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                      >
                        删除
                      </Button>
                    </div>
                  ) : (
                    <div className="py-4">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>点击上传身份证反面照片</span>
                        <span className="text-xs mt-1">支持 JPG、JPEG、PNG 格式，最大 5MB</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-blue-700">手持身份证照片</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer relative">
                  <input
                    type="file"
                    id="idCardHolding"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        uploadVerificationFile(file, "idCardHolding");
                      }
                    }}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification || uploadingFile}
                  />
                  {verificationData.idCardHolding ? (
                    <div className="relative">
                      <img
                        src={verificationData.idCardHolding}
                        alt="手持身份证"
                        className="max-h-40 mx-auto rounded-lg"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setVerificationData(prev => ({
                            ...prev,
                            idCardHolding: ""
                          }));
                        }}
                        disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                      >
                        删除
                      </Button>
                    </div>
                  ) : (
                    <div className="py-4">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>点击上传手持身份证照片</span>
                        <span className="text-xs mt-1">支持 JPG、JPEG、PNG 格式，最大 5MB</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="remark" className="text-blue-700">备注说明</Label>
                <Textarea
                  id="remark"
                  placeholder="请输入其他需要说明的信息"
                  value={verificationData.remark || ''}
                  onChange={(e) => setVerificationData(prev => ({
                    ...prev,
                    remark: e.target.value
                  }))}
                  disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          )}

          {/* 企业认证表单 */}
          {verificationType === "enterprise" && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName" className="text-blue-700">企业名称</Label>
                  <Input
                    id="companyName"
                    placeholder="请输入企业名称"
                    value={verificationData.companyName}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      companyName: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="socialCreditCode" className="text-blue-700">统一社会信用代码</Label>
                  <Input
                    id="socialCreditCode"
                    placeholder="请输入统一社会信用代码"
                    value={verificationData.socialCreditCode}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      socialCreditCode: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="legalPerson" className="text-blue-700">法人姓名</Label>
                  <Input
                    id="legalPerson"
                    placeholder="请输入法人姓名"
                    value={verificationData.legalPerson}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      legalPerson: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="legalPersonIdCard" className="text-blue-700">法人身份证号</Label>
                  <Input
                    id="legalPersonIdCard"
                    placeholder="请输入法人身份证号"
                    value={verificationData.legalPersonIdCard}
                    onChange={(e) => setVerificationData(prev => ({
                      ...prev,
                      legalPersonIdCard: e.target.value
                    }))}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-blue-700">营业执照照片</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer relative">
                  <input
                    type="file"
                    id="businessLicense"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        uploadVerificationFile(file, "businessLicense");
                      }
                    }}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification || uploadingFile}
                  />
                  {verificationData.businessLicense ? (
                    <div className="relative">
                      <img
                        src={verificationData.businessLicense}
                        alt="营业执照"
                        className="max-h-40 mx-auto rounded-lg"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setVerificationData(prev => ({
                            ...prev,
                            businessLicense: ""
                          }));
                        }}
                        disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                      >
                        删除
                      </Button>
                    </div>
                  ) : (
                    <div className="py-4">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>点击上传营业执照照片</span>
                        <span className="text-xs mt-1">支持 JPG、JPEG、PNG 格式，最大 5MB</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-blue-700">其他资质和附件</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors cursor-pointer relative">
                  <input
                    type="file"
                    id="otherDocuments"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".jpg,.jpeg,.png,.pdf"
                    multiple
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        uploadMultipleFiles(e.target.files, "otherDocuments");
                      }
                    }}
                    disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification || uploadingFile}
                  />
                  <div className="py-4">
                    <div className="flex flex-col items-center justify-center text-gray-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      <span>点击上传其他资质文件</span>
                      <span className="text-xs mt-1">支持 JPG、JPEG、PNG、PDF 格式，最大 10MB</span>
                    </div>
                  </div>
                </div>

                {verificationData.otherDocuments.length > 0 && (
                  <div className="mt-4">
                    <Label className="text-blue-700 mb-2 block">已上传文件</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {verificationData.otherDocuments.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span className="truncate max-w-[150px]">附件 {index + 1}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => {
                              setVerificationData(prev => ({
                                ...prev,
                                otherDocuments: prev.otherDocuments.filter((_, i) => i !== index)
                              }));
                            }}
                            disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                          >
                            删除
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="remark" className="text-blue-700">备注说明</Label>
                <Textarea
                  id="remark"
                  placeholder="请输入其他需要说明的信息"
                  value={verificationData.remark || ''}
                  onChange={(e) => setVerificationData(prev => ({
                    ...prev,
                    remark: e.target.value
                  }))}
                  disabled={verificationStatus === "pending" || verificationStatus === "approved" || submittingVerification}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          )}

          {/* 上传进度条 */}
          {uploadingFile && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>上传进度</Label>
                <span className="text-sm text-blue-600">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="bg-white border-t flex justify-end space-x-2 py-4">
        <Button
          variant="outline"
          disabled={submittingVerification || verificationStatus === "pending" || verificationStatus === "approved"}
          onClick={() => {
            // 重置表单
            setVerificationData({
              realName: "",
              idCardNumber: "",
              idCardFront: "",
              idCardBack: "",
              idCardHolding: "",
              companyName: "",
              legalPerson: "",
              legalPersonIdCard: "",
              socialCreditCode: "",
              businessLicense: "",
              otherDocuments: [],
              remark: ""
            });
          }}
        >
          重置
        </Button>
        <Button
          onClick={submitVerification}
          disabled={submittingVerification || verificationStatus === "pending" || verificationStatus === "approved"}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {submittingVerification ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              提交中...
            </>
          ) : (
            "提交认证"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
