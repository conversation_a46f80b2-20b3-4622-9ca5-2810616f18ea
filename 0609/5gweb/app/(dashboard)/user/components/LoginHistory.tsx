"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Search, Calendar as CalendarIcon, Download, FileText, FileSpreadsheet, Loader2 } from "lucide-react"
import { format } from "date-fns"
import { useLoginHistory } from "../hooks/useLoginHistory"

export function LoginHistory() {
  const {
    loginHistory,
    loadingHistory,
    historySearchTerm,
    setHistorySearchTerm,
    historyDateRange,
    setHistoryDateRange,
    historyStatusFilter,
    setHistoryStatusFilter,
    fetchLoginHistory,
    exportLoginHistory
  } = useLoginHistory()

  // 组件挂载时获取登录历史
  useEffect(() => {
    fetchLoginHistory()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-6xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">登录历史</CardTitle>
        <CardDescription>查看您的账户最近的登录活动和安全状态</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="space-y-4 mb-6">
          {/* 搜索和筛选标题 */}
          <h3 className="text-lg font-medium text-blue-800">搜索和筛选</h3>

          {/* 搜索框 */}
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-600" />
            <Input
              placeholder="搜索IP、位置或设备..."
              className="pl-10 py-5 bg-white border-blue-100 focus:border-blue-300 text-base rounded-lg w-full"
              value={historySearchTerm}
              onChange={(e) => {
                setHistorySearchTerm(e.target.value)
                fetchLoginHistory(1, e.target.value)
              }}
            />
          </div>

          {/* 筛选和导出工具栏 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 日期选择 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
                  <CalendarIcon className="mr-2 h-5 w-5 text-blue-600" />
                  {historyDateRange?.from ? (
                    historyDateRange.to ? (
                      <>
                        {format(historyDateRange.from, "yyyy-MM-dd")} ~{" "}
                        {format(historyDateRange.to, "yyyy-MM-dd")}
                      </>
                    ) : (
                      format(historyDateRange.from, "yyyy-MM-dd")
                    )
                  ) : (
                    <span>选择日期范围</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 border-blue-100 shadow-lg" align="start">
                <Calendar
                  mode="range"
                  selected={historyDateRange}
                  onSelect={(range) => {
                    setHistoryDateRange(range || { from: undefined, to: undefined })
                    fetchLoginHistory(1, historySearchTerm, historyStatusFilter, range?.from?.toISOString(), range?.to?.toISOString())
                  }}
                  initialFocus
                />
                <div className="p-3 border-t border-blue-100">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setHistoryDateRange({ from: undefined, to: undefined })
                      fetchLoginHistory(1, historySearchTerm, historyStatusFilter)
                    }}
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                  >
                    清除日期筛选
                  </Button>
                </div>
              </PopoverContent>
            </Popover>

            {/* 状态筛选 */}
            <Select
              value={historyStatusFilter}
              onValueChange={(value) => {
                setHistoryStatusFilter(value)
                fetchLoginHistory(1, historySearchTerm, value)
              }}
            >
              <SelectTrigger className="w-full border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
                <SelectValue placeholder="登录状态" />
              </SelectTrigger>
              <SelectContent className="border-blue-100 shadow-lg">
                <SelectItem value="all" className="py-2">全部状态</SelectItem>
                <SelectItem value="success" className="py-2 text-green-600">成功</SelectItem>
                <SelectItem value="failed" className="py-2 text-red-600">失败</SelectItem>
              </SelectContent>
            </Select>

            {/* 导出按钮 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="w-full bg-green-600 hover:bg-green-700 text-white py-5 rounded-lg">
                  <Download className="mr-2 h-5 w-5 text-white" />
                  导出记录
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-blue-100 shadow-lg">
                <DropdownMenuItem onClick={() => exportLoginHistory('csv')} className="py-2 hover:bg-blue-50">
                  <FileText className="mr-2 h-5 w-5 text-blue-600" />
                  导出为CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportLoginHistory('excel')} className="py-2 hover:bg-blue-50">
                  <FileSpreadsheet className="mr-2 h-5 w-5 text-green-600" />
                  导出为Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="border border-blue-100 rounded-lg overflow-hidden shadow-sm">
          {loadingHistory ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
              <span className="ml-3 text-lg font-medium text-blue-600">加载中...</span>
            </div>
          ) : (
            <Table>
              <TableHeader className="bg-blue-50">
                <TableRow className="hover:bg-blue-50/80">
                  <TableHead className="text-blue-800 font-semibold py-4">时间</TableHead>
                  <TableHead className="text-blue-800 font-semibold py-4">IP地址</TableHead>
                  <TableHead className="text-blue-800 font-semibold py-4">位置</TableHead>
                  <TableHead className="text-blue-800 font-semibold py-4">设备</TableHead>
                  <TableHead className="text-blue-800 font-semibold py-4">状态</TableHead>
                  <TableHead className="text-blue-800 font-semibold py-4">失败原因</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loginHistory.items.length > 0 ? (
                  loginHistory.items.map((item) => (
                    <TableRow key={item.id} className="hover:bg-blue-50/30">
                      <TableCell className="font-medium text-blue-900">{new Date(item.createdAt).toLocaleString('zh-CN')}</TableCell>
                      <TableCell>{item.ipAddress}</TableCell>
                      <TableCell>{item.location || '-'}</TableCell>
                      <TableCell className="max-w-[300px] truncate" title={item.device}>{item.device || '-'}</TableCell>
                      <TableCell>
                        <Badge
                          variant={item.status === 'success' ? 'default' : 'destructive'}
                          className={`${item.status === 'success' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-red-100 text-red-800 hover:bg-red-100'} font-medium px-3 py-1 rounded-full`}
                        >
                          {item.status === 'success' ? '成功' : '失败'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-red-600">{item.failReason || '-'}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-10">
                      <span className="text-gray-500 text-base">没有找到匹配的登录记录</span>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>

        {loginHistory.items.length > 0 && (
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-blue-600">
              显示 {loginHistory.total} 条记录中的{" "}
              {(loginHistory.page - 1) * loginHistory.pageSize + 1} -{" "}
              {Math.min(loginHistory.page * loginHistory.pageSize, loginHistory.total)} 条
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => fetchLoginHistory(loginHistory.page - 1)}
                    className={`${loginHistory.page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} text-blue-600 hover:text-blue-700`}
                  />
                </PaginationItem>

                {Array.from(
                  { length: Math.min(5, loginHistory.totalPages) },
                  (_, i) => {
                    let pageNum
                    if (loginHistory.totalPages <= 5) {
                      pageNum = i + 1
                    } else if (loginHistory.page <= 3) {
                      pageNum = i + 1
                    } else if (loginHistory.page >= loginHistory.totalPages - 2) {
                      pageNum = loginHistory.totalPages - 4 + i
                    } else {
                      pageNum = loginHistory.page - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => fetchLoginHistory(pageNum)}
                          isActive={loginHistory.page === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  }
                )}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => fetchLoginHistory(loginHistory.page + 1)}
                    className={`${loginHistory.page === loginHistory.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"} text-blue-600 hover:text-blue-700`}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
