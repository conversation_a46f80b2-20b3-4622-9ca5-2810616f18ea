"use client"

/**
 * 全局 Providers 组件
 * 为应用提供全局状态和功能的顶层组件
 *
 * 特性：
 * - 主题管理：使用 next-themes 提供主题切换功能
 * - 客户端渲染：使用

指令确保在客户端执行
 * - 类型安全：使用 TypeScript 类型定义
 *
 * @param props - 组件属性
 * @param props.children - 子组件，将被包裹在providers中
 *
 * @example
 * ```tsx
 * // 在根布局中使用
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <Providers>{children}</Providers>
 *       </body>
 *     </html>
 *   )
 * }
 * ```
 */

"use client"

import { ThemeProvider } from "next-themes"
import { SessionProvider } from "next-auth/react"
import { ReactNode } from "react"
import { AuthProvider } from "@/contexts/auth-context"
import { TooltipProvider } from "@/components/ui/tooltip"
import { Toaster } from "@/components/ui/toaster"
import { SidebarProvider } from "@/components/ui/sidebar/use-sidebar"

/**
 * 全局提供者组件
 * 集成了NextAuth会话管理、自定义认证状态管理以及主题配置
 */
export function Providers({
  children,
  session
}: {
  children: React.ReactNode
  session: any
}) {
  return (
    <SessionProvider session={session}>
      <AuthProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <SidebarProvider>
            <TooltipProvider>
              {children}
              <Toaster />
            </TooltipProvider>
          </SidebarProvider>
        </ThemeProvider>
      </AuthProvider>
    </SessionProvider>
  )
}