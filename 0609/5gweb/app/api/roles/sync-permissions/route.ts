import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 同步角色菜单权限到jCasbin
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[角色API:${requestId}] 同步角色菜单权限`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "同步角色菜单权限API");
    if (response) {
      console.log(`[角色API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以同步角色菜单权限",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const roleCode = body.roleCode

    // 如果提供了角色代码，只同步该角色的权限
    if (roleCode) {
      // 获取角色
      const role = await prisma.role.findUnique({
        where: { code: roleCode },
        include: {
          menus: true,
          operations: true
        }
      })

      if (!role) {
        return NextResponse.json({
          success: false,
          message: "角色不存在",
          requestId
        }, { status: 404 })
      }

      // 获取角色当前的所有权限
      const currentPermissions = await CasbinService.getRolePermissions(roleCode)

      // 删除所有菜单和操作权限
      for (const permission of currentPermissions) {
        if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
          await CasbinService.removePermissionForRole(roleCode, permission[1], permission[2])
        }
      }

      // 添加菜单权限
      for (const menu of role.menus) {
        await CasbinService.addPermissionForRole(roleCode, `menu:${menu.code}`, 'view')
      }

      // 添加操作权限
      for (const operation of role.operations) {
        await CasbinService.addPermissionForRole(roleCode, `operation:${operation.code}`, 'execute')
      }

      return NextResponse.json({
        success: true,
        message: `角色 ${role.name} (${roleCode}) 的权限同步成功`,
        data: {
          roleCode,
          menuCount: role.menus.length,
          operationCount: role.operations.length
        },
        requestId
      })
    } else {
      // 同步所有角色的权限
      const roles = await prisma.role.findMany({
        include: {
          menus: true,
          operations: true
        }
      })

      for (const role of roles) {
        // 获取角色当前的所有权限
        const currentPermissions = await CasbinService.getRolePermissions(role.code)

        // 删除所有菜单和操作权限
        for (const permission of currentPermissions) {
          if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
            await CasbinService.removePermissionForRole(role.code, permission[1], permission[2])
          }
        }

        // 添加菜单权限
        for (const menu of role.menus) {
          await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
        }

        // 添加操作权限
        for (const operation of role.operations) {
          await CasbinService.addPermissionForRole(role.code, `operation:${operation.code}`, 'execute')
        }
      }

      return NextResponse.json({
        success: true,
        message: `所有角色的权限同步成功`,
        data: {
          roleCount: roles.length
        },
        requestId
      })
    }
  } catch (error) {
    console.error(`[角色API:${requestId}] 同步角色菜单权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "同步角色菜单权限失败",
      requestId
    }, { status: 500 })
  }
}
