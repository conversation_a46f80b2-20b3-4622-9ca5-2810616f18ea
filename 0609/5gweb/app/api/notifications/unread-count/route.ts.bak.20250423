import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import * as jose from 'jose';
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

/**
 * 获取未读通知数量
 *
 * @route GET /api/notifications/unread-count
 * @access 所有已登录用户可访问
 */
export async function GET(req: NextRequest) {
  console.log("获取未读通知数量API被调用");

  try {
    // 首先尝试获取NextAuth会话
    const session = await getServerSession(authOptions);
    console.log('未读通知数量API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID
    let userId = session?.user?.id;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!userId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true }
        });
        if (user) {
          userId = user.id;
          console.log('从数据库中查询到用户ID:', userId);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 如果从NextAuth会话中无法获取用户ID，尝试使用JWT token
    if (!userId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies();
      const token = cookieStore.get("token")?.value;

      if (!token) {
        console.log("获取未读通知数量 - 未找到token");
        return NextResponse.json({
          success: false,
          message: '未授权访问',
        }, { status: 401 });
      }

      // 验证token
      console.log("验证token...");
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET);
        userId = payload.userId as string || payload.sub as string;
        console.log('从令牌中提取的用户ID:', userId, '载荷:', payload);
      } catch (error) {
        console.error("令牌验证失败:", error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息',
        }, { status: 401 });
      }
    } else {
      console.log('从NextAuth会话中提取的用户ID:', userId);
    }

    if (!userId) {
      throw new Error('无效的用户ID');
    }

    // 查询未读通知数量 - 使用原始 SQL 以确保准确性
    console.log(`查询用户 ${userId} 的未读通知数量...`);

    // 打印调试信息，查看所有通知
    const allNotifications = await prisma.notification.findMany({
      select: {
        id: true,
        title: true,
        type: true,
        sendToAll: true,
        recipients: true,
        status: true,
        userNotifications: {
          where: {
            userId: userId as string
          },
          select: {
            read: true
          }
        }
      }
    });

    console.log('所有通知:', JSON.stringify(allNotifications, null, 2));

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: userId },
      select: { roleCode: true }
    });

    // 检查用户的通知设置
    const userSettings = await prisma.userNotificationSettings.findUnique({
      where: { userId: userId as string }
    });

    // 如果用户关闭了应用内通知，返回0
    if (userSettings && userSettings.appEnabled === false) {
      console.log(`用户 ${userId} 已关闭应用内通知，返回未读数量为0`);
      return NextResponse.json({
        success: true,
        message: '获取未读通知数量成功',
        data: {
          total: 0
        }
      });
    }

    const isAdmin = userRole?.roleCode === 'ADMIN';
    console.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    let whereClause;

    if (isAdmin) {
      // 管理员只计算全局通知和发送给管理员的未读通知，但不包括账户启用和禁用的通知
      whereClause = `n.status = 'published' AND (un.id IS NULL OR un.read = FALSE) AND (n."sendToAll" = true OR (n."sendToAll" = false AND (n.recipients::text ILIKE '%"${userId}"%' OR n.recipients::text ILIKE '%${userId}%'))) AND (n.title != '账户已启用' AND n.title != '账户已禁用')`;
      console.log('管理员用户，只计算全局通知和发送给管理员的未读通知，但不包括账户启用和禁用的通知');
    } else {
      // 普通用户只计算发送给自己的未读通知
      whereClause = `n.status = 'published' AND (un.id IS NULL OR un.read = FALSE) AND (n."sendToAll" = true OR (n."sendToAll" = false AND (n.recipients::text ILIKE '%"${userId}"%' OR n.recipients::text ILIKE '%${userId}%')))`;
      console.log('普通用户，只计算发送给自己的未读通知');
    }

    const query = `
      SELECT COUNT(*) as count
      FROM notification n
      LEFT JOIN "user_notification" un
        ON n.id = un."notificationId"
        AND un."userId" = '${userId}'
      WHERE ${whereClause}
    `;

    console.log('执行的SQL查询:', query);
    const result = await prisma.$queryRawUnsafe(query);


    const count = Number((result as any[])[0]?.count || 0);
    console.log(`用户 ${userId} 的未读通知数量: ${count}`);

    return NextResponse.json({
      success: true,
      message: '获取未读通知数量成功',
      data: {
        total: count
      }
    });
  } catch (error) {
    console.error("获取未读通知数量错误:", error);
    return NextResponse.json({
      success: false,
      message: '获取未读通知数量失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}