import { NextRequest, NextResponse } from "next/server";
import { checkPermission } from "@/lib/casbin/enforcer";
import { prisma, Prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";
import { sendEmail } from "@/app/lib/email";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import logger from '@/lib/utils/logger';

// 定义通知类型接口
interface CreateNotificationBody {
  title: string;
  content: string;
  type: string;
  priority?: 'low' | 'normal' | 'high';
  recipients?: string[];
  sendToAll?: boolean;
}

/**
 * 检查用户是否有指定权限
 */

/**
 * 获取通知列表
 *
 * @route GET /api/notifications
 * @access 所有已登录用户可访问
 */
export async function GET(req: NextRequest) {
  try {
    // 获取分页和筛选参数
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const readFilter = searchParams.get('read');
    const type = searchParams.get('type');
    const search = searchParams.get('search');

    // 验证分页参数
    if (isNaN(page) || page < 1 || isNaN(pageSize) || pageSize < 1) {
      return NextResponse.json({
        success: false,
        message: '无效的分页参数',
      }, { status: 400 });
    }

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "通知列表API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问',
      }, { status: 401 });
    }

    const userId = user.id;
    const roleCode = user.roleCode;

    // 检查权限 - 所有登录用户都应该能查看通知列表
    // 如果是管理员或者有特定权限，则可以查看所有通知
    // 如果是普通用户，则只能查看发送给自己的通知
    let hasViewPermission = true; // 默认允许所有登录用户查看通知

    // 检查是否为管理员
    const isAdminUser = roleCode?.toUpperCase() === 'ADMIN';
    logger.log('用户角色:', roleCode, '是否为管理员:', isAdminUser);

    // 构建基本查询条件
    logger.log('构建通知查询条件，用户ID:', userId);

    // 查询用户通知关联
    logger.log('查询用户通知关联，用户ID:', userId);
    const userNotifications = await prisma.userNotification.findMany({
      where: {
        userId: userId
      },
      select: {
        notificationId: true
      }
    });

    logger.log('用户通知关联:', userNotifications.map(un => un.notificationId));

    // 使用更简单的方式查询用户的通知
    // 构建查询条件：全局通知或者用户特定通知
    logger.log('当前登录用户ID:', userId);

    // 查询所有通知，包括全局通知和指定用户通知
    const allNotifications = await prisma.notification.findMany({
      where: {
        status: 'published',
      },
      select: {
        id: true,
        title: true,
        sendToAll: true,
        recipients: true,
      }
    });

    logger.debug('所有通知:', allNotifications.map(n => ({
      id: n.id,
      title: n.title,
      sendToAll: n.sendToAll,
      recipients: n.recipients
    })));

    // 检查用户的通知设置
    const userSettings = await prisma.userNotificationSettings.findUnique({
      where: { userId: userId as string }
    });

    // 如果用户关闭了应用内通知，返回空列表
    if (userSettings && userSettings.appEnabled === false) {
      logger.log(`用户 ${userId} 已关闭应用内通知，返回空通知列表`);
      return NextResponse.json({
        success: true,
        message: '获取通知列表成功',
        data: {
          notifications: [],
          pagination: {
            total: 0,
            totalPages: 1,
            page,
            pageSize
          }
        }
      });
    }

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: userId },
      select: { roleCode: true }
    });

    const isAdmin = userRole?.roleCode === 'ADMIN';
    logger.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    // 构建查询条件
    let whereClause;

    if (isAdmin) {
      // 管理员不应该看到与用户相关的特定通知
      whereClause = Prisma.sql`
        n.status = 'published' AND
        (n.title NOT IN ('账户已启用', '账户已禁用', '认证申请未通过', '认证申请已通过', '密码已重置', '您的个人认证已通过', '您的企业认证已通过', '您的个人认证未通过', '您的企业认证未通过')) AND
        (nt.code != 'VERIFICATION') AND
        (nt.code != 'ACCOUNT')
      `;
      logger.log('管理员显示通知，但不包括用户特定的通知（如账户启用/禁用、认证结果、密码重置等）');
    } else {
      // 普通用户只能看到全局通知和发送给自己的通知
      whereClause = Prisma.sql`
        n.status = 'published' AND (
          n."sendToAll" = true OR
          (n."sendToAll" = false AND (n.recipients::text ILIKE ${'%"' + userId + '"%'} OR n.recipients::text ILIKE ${'%' + userId + '%'}))
        )
      `;
      logger.log('普通用户显示全局通知和用户特定通知');
    }
    logger.log('当前用户ID:', userId);

    // 添加已读/未读筛选
    if (readFilter === 'true' || readFilter === 'false') {
      whereClause = Prisma.sql`${whereClause} AND (CASE WHEN un.id IS NULL THEN false ELSE COALESCE(un.read, false) END = ${readFilter === 'true'})`;
    }

    // 添加类型筛选
    if (type && type !== 'all') {
      whereClause = Prisma.sql`${whereClause} AND nt.code = ${type}`;
    }

    // 添加搜索条件
    if (search) {
      const searchPattern = `%${search}%`;
      whereClause = Prisma.sql`${whereClause} AND (n.title ILIKE ${searchPattern} OR n.content ILIKE ${searchPattern})`;
    }

    // 查询总记录数
    const countQuery = await prisma.$queryRaw`
        SELECT COUNT(*) as total
        FROM notification n
        LEFT JOIN "user_notification" un
          ON n.id = un."notificationId"
          AND un."userId" = ${userId}
        LEFT JOIN "notificationType" nt
          ON n."typeId" = nt.id
        WHERE ${whereClause}
    `;

    const total = Number((countQuery as any)[0].total);

    // 已在前面查询过用户通知关联，这里不需要重复查询

    // 查询通知列表
    logger.log('执行通知查询，用户ID:', userId);

    const notifications = await prisma.$queryRaw`
        SELECT
          n.id,
          n.title,
          n.content,
          n."typeId",
          nt.code as "typeCode",
          nt.name as "typeName",
          n.priority,
          n.status,
          n."createdAt",
          n."updatedAt",
          n."publishedAt",
          n."sendToAll",
          n.recipients,
          CASE
            WHEN un.id IS NULL THEN false
            ELSE COALESCE(un.read, false)
          END as read,
          un."readAt"
        FROM notification n
        LEFT JOIN "user_notification" un
          ON n.id = un."notificationId"
          AND un."userId" = ${userId}
        LEFT JOIN "notificationType" nt
          ON n."typeId" = nt.id
        WHERE ${whereClause}
        ORDER BY n."createdAt" DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
    `;

    logger.log('查询到', (notifications as any[]).length, '条通知');

    // 处理通知数据
    const processedNotifications = (notifications as any[]).map(notification => {
      // 输出调试信息
      logger.debug('处理通知:', {
        id: notification.id,
        title: notification.title,
        sendToAll: notification.sendToAll,
        recipients: notification.recipients
      });

      return {
        ...notification,
        read: notification.read === true,
        createdAt: notification.createdAt?.toISOString(),
        updatedAt: notification.updatedAt?.toISOString(),
        publishedAt: notification.publishedAt?.toISOString(),
        readAt: notification.readAt?.toISOString()
      };
    });

    // 计算总页数 - 将 BigInt 转换为 number
    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      success: true,
      message: '获取通知列表成功',
      data: {
        notifications: processedNotifications,
        pagination: {
          page,
          pageSize,
          total,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error("获取通知列表错误:", error);
    return NextResponse.json({
      success: false,
      message: '获取通知列表失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}

/**
 * 创建新通知
 *
 * @route POST /api/notifications
 * @access 仅管理员可访问
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "创建通知API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问',
      }, { status: 401 });
    }

    // 验证用户权限 - 管理员自动有所有权限
    if (admin.roleCode && admin.roleCode.toUpperCase() === 'ADMIN') {
      logger.log('用户是管理员，自动授予创建通知权限');
    } else {
      const hasPermission = await checkPermission(req, 'notifications:create');
      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          message: '无权限创建通知'
        }, { status: 403 });
      }
    }

    // 解析请求体
    const body = await req.json() as CreateNotificationBody;
    const { title, content, type, priority = 'normal', recipients, sendToAll = true } = body;

    logger.debug('创建通知请求数据:', {
      title,
      type,
      priority,
      sendToAll,
      recipients
    });

    // 验证必填字段
    if (!title?.trim() || !content?.trim()) {
      return NextResponse.json({
        success: false,
        message: '标题和内容不能为空',
      }, { status: 400 });
    }

    // 验证类型ID是否为数字
    const typeId = parseInt(type);
    if (isNaN(typeId)) {
      return NextResponse.json({
        success: false,
        message: '通知类型ID必须是数字',
      }, { status: 400 });
    }

    // 验证通知类型是否存在
    const notificationType = await prisma.notificationType.findUnique({
      where: { id: typeId }
    });

    if (!notificationType) {
      return NextResponse.json({
        success: false,
        message: '无效的通知类型',
      }, { status: 400 });
    }

    // 创建通知
    logger.log('开始创建通知...');
    const notification = await prisma.notification.create({
      data: {
        title,
        content,
        typeId,
        priority,
        status: 'published',
        createdBy: 'system',
        publishedAt: new Date(),
        sendToAll,
        recipients: sendToAll ? undefined : Array.isArray(recipients) ? JSON.stringify(recipients) : recipients,
      },
    });

    logger.log('通知创建成功，ID:', notification.id, '是否发送给所有用户:', sendToAll, '指定用户:', recipients);

    // 创建用户通知关联
    if (sendToAll) {
      // 为所有用户创建通知关联，并获取用户的通知设置
      const allUsers = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          notificationSettings: true
        },
      });

      // 创建用户通知关联
      await prisma.userNotification.createMany({
        data: allUsers.map(user => ({
          id: crypto.randomUUID(),
          userId: user.id,
          notificationId: notification.id,
          read: false,
        })),
      });

      // 发送电子邮件通知给启用了电子邮件通知的用户
      for (const user of allUsers) {
        try {
          logger.log(`检查用户 ${user.id} 的邮件通知设置:`, user.notificationSettings);
          // 检查用户是否启用了电子邮件通知
          if (user.notificationSettings?.emailEnabled === true) {
            // 检查通知类型是否在用户设置的接收类型中
            const typeCode = notificationType.code || '';
            if (!typeCode || !user.notificationSettings.types ||
                user.notificationSettings.types.includes(typeCode)) {
              // 将优先级转换为中文
              const priorityInChinese = {
                'low': '低',
                'normal': '中',
                'medium': '中', // 添加medium的映射
                'high': '高',
                'urgent': '紧急'
              };
              logger.log('原始优先级值:', notification.priority);
              const priorityText = priorityInChinese[notification.priority as keyof typeof priorityInChinese] || notification.priority;
              logger.log('转换后的优先级文本:', priorityText);

              // 发送电子邮件
              await sendEmail({
                to: user.email,
                subject: `${notificationType.name || '系统'} 通知: ${notification.title}`,
                html: `
                  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                    <h2 style="color: #333; text-align: center;">${notification.title}</h2>
                    <div style="background: #f5f5f5; padding: 15px; margin: 20px 0;">
                      <div style="font-size: 16px; color: #333; line-height: 1.6;">
                        ${notification.content}
                      </div>
                    </div>
                    <p style="color: #666; font-size: 14px;">
                      优先级: ${priorityText}
                    </p>
                    <p style="color: #666; font-size: 14px;">
                      发布时间: ${notification.publishedAt ? new Date(notification.publishedAt).toLocaleString() : '未发布'}
                    </p>
                    <p style="color: #999; font-size: 12px; margin-top: 30px;">
                      此邮件由系统自动发送，请勿回复。如需关闭邮件通知，请在系统设置中修改通知偏好。
                    </p>
                  </div>
                `
              });

              // 更新用户通知关联，记录邮件已发送
              await prisma.userNotification.updateMany({
                where: {
                  userId: user.id,
                  notificationId: notification.id
                },
                data: {
                  emailSent: true,
                  emailSentAt: new Date()
                }
              });

              logger.log(`已发送电子邮件通知给用户 ${user.id}`);
            }
          }
        } catch (error) {
          logger.error(`发送电子邮件通知给用户 ${user.id} 失败:`, error);
        }
      }
    } else if (recipients && recipients.length > 0) {
      // 为指定用户创建通知关联
      logger.log('为指定用户创建通知关联:', recipients);

      try {
        // 处理recipients字段，确保它是一个数组
        let recipientArray: string[] = [];

        if (typeof recipients === 'string') {
          try {
            // 尝试解析JSON字符串
            recipientArray = JSON.parse(recipients);
            logger.log('解析recipients JSON字符串:', recipientArray);
          } catch (e) {
            // 如果不是JSON，将其作为单个用户ID
            recipientArray = [recipients];
            logger.log('将recipients作为单个用户ID:', recipientArray);
          }
        } else if (Array.isArray(recipients)) {
          recipientArray = recipients;
          logger.log('使用recipients数组:', recipientArray);
        }

        logger.log('最终的recipients数组:', recipientArray);

        // 直接创建用户通知关联，不需要验证用户是否存在
        // 因为外键约束会确保只有有效的用户ID才能创建成功
        const userNotificationData = recipientArray.map((userId: string) => ({
          id: crypto.randomUUID(),
          userId,
          notificationId: notification.id,
          read: false,
        }));

        logger.log('用户通知关联数据:', userNotificationData);

        if (userNotificationData.length > 0) {
          // 使用事务确保数据一致性
          const result = await prisma.$transaction(async (tx) => {
            // 首先创建用户通知关联
            await tx.userNotification.createMany({
              data: userNotificationData,
              skipDuplicates: true, // 跳过重复数据
            });

            // 查询创建的关联记录
            const createdRecords = await tx.userNotification.findMany({
              where: {
                notificationId: notification.id,
              },
              select: {
                userId: true,
              },
            });

            return createdRecords;
          });

          logger.log('用户通知关联创建成功，共创建', result.length, '条记录');

          // 获取指定用户的详细信息，包括通知设置
          const usersWithSettings = await prisma.user.findMany({
            where: {
              id: { in: recipientArray }
            },
            select: {
              id: true,
              email: true,
              notificationSettings: true
            }
          });

          // 发送电子邮件通知给启用了电子邮件通知的用户
          for (const user of usersWithSettings) {
            try {
              logger.log(`检查指定用户 ${user.id} 的邮件通知设置:`, user.notificationSettings);
              // 检查用户是否启用了电子邮件通知
              if (user.notificationSettings?.emailEnabled === true) {
                // 检查通知类型是否在用户设置的接收类型中
                const typeCode = notificationType.code || '';
                if (!typeCode || !user.notificationSettings.types ||
                    user.notificationSettings.types.includes(typeCode)) {
                  // 将优先级转换为中文
                  const priorityInChinese = {
                    'low': '低',
                    'normal': '中',
                    'high': '高',
                    'urgent': '紧急'
                  };
                  const priorityText = priorityInChinese[notification.priority as keyof typeof priorityInChinese] || notification.priority;

                  // 发送电子邮件
                  await sendEmail({
                    to: user.email,
                    subject: `${notificationType.name || '系统'} 通知: ${notification.title}`,
                    html: `
                      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                        <h2 style="color: #333; text-align: center;">${notification.title}</h2>
                        <div style="background: #f5f5f5; padding: 15px; margin: 20px 0;">
                          <div style="font-size: 16px; color: #333; line-height: 1.6;">
                            ${notification.content}
                          </div>
                        </div>
                        <p style="color: #666; font-size: 14px;">
                          优先级: ${priorityText}
                        </p>
                        <p style="color: #666; font-size: 14px;">
                          发布时间: ${notification.publishedAt ? new Date(notification.publishedAt).toLocaleString() : '未发布'}
                        </p>
                        <p style="color: #999; font-size: 12px; margin-top: 30px;">
                          此邮件由系统自动发送，请勿回复。如需关闭邮件通知，请在系统设置中修改通知偏好。
                        </p>
                      </div>
                    `
                  });

                  // 更新用户通知关联，记录邮件已发送
                  await prisma.userNotification.updateMany({
                    where: {
                      userId: user.id,
                      notificationId: notification.id
                    },
                    data: {
                      emailSent: true,
                      emailSentAt: new Date()
                    }
                  });

                  logger.log(`已发送电子邮件通知给用户 ${user.id}`);
                }
              }
            } catch (error) {
              logger.error(`发送电子邮件通知给用户 ${user.id} 失败:`, error);
            }
          }
        } else {
          logger.error('没有有效的用户通知关联数据可创建');
        }
      } catch (error) {
        logger.error('创建用户通知关联时出错:', error);
      }
    }

    return NextResponse.json({
      success: true,
      message: '创建通知成功',
      data: { id: notification.id }
    });
  } catch (error) {
    logger.error('创建通知错误:', error);
    return NextResponse.json({
      success: false,
      message: '创建通知失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}

/**
 * 获取用户的通知列表
 * @param userId 用户ID
 * @returns 通知列表
 */
async function getNotifications(userId: string): Promise<any[]> {
  try {
    logger.log(`正在获取用户 ${userId} 的通知列表...`);
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    // 由于存在NOT NULL约束错误，避免使用INSERT操作，直接查询通知
    logger.log(`查询用户 ${userId} 的所有通知...`);

    // 简化SQL查询以避免NOT NULL约束错误
    const notifications: any[] = await prisma.$queryRawUnsafe(`
      SELECT
        n.id,
        n.title,
        n.content,
        n.type,
        n.priority,
        n."createdAt",
        n."updatedAt",
        COALESCE(un.read, FALSE) as read
      FROM
        "notification" n
      LEFT JOIN
        "user_notification" un ON n.id = un."notificationId" AND un."userId" = $1
      ORDER BY n."createdAt" DESC
    `, userId);

    logger.log(`查询到 ${notifications.length} 条通知`);

    // 关闭Prisma客户端连接
    await prisma.$disconnect();

    // 安全处理通知内容
    return notifications.map(notification => ({
      ...notification,
      content: typeof notification.content === 'string' && notification.content.length > 5000
        ? notification.content.substring(0, 5000) + '...(内容过长已截断)'
        : notification.content
    }));
  } catch (error) {
    logger.error('获取通知列表数据库查询错误:', error);
    return [];
  }
}

/**
 * 删除通知
 *
 * @route DELETE /api/notifications
 * @access 管理员可访问
 */
export async function DELETE(req: NextRequest) {
  logger.log("删除通知API被调用");

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "删除通知API");
    if (response) {
      logger.log("删除通知 - 未授权访问");
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    const userId = admin.id;

    // 解析请求体
    const data = await req.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json({
        success: false,
        message: '缺少通知ID',
      }, { status: 400 });
    }

    // 检查用户是否有权限删除通知
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        role: {
          select: {
            code: true,
            resources: {
              select: {
                code: true,
                operations: {
                  select: {
                    code: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user || !user.role) {
      throw new Error('用户不存在或没有角色');
    }

    // 检查用户是否有管理通知的权限
    const hasPermission = user.role.resources.some(resource => {
      return resource.code === 'notifications' &&
        resource.operations.some(operation =>
          operation.code === 'manage' ||
          operation.code === 'delete' ||
          operation.code === 'edit'
        );
    });

    if (!hasPermission) {
      logger.log("用户没有管理通知的权限");
      return NextResponse.json({
        success: false,
        message: '权限不足，只有管理员可以管理通知',
      }, { status: 403 });
    }

    logger.log(`删除通知 ID: ${id}`);

    // 先删除通知与用户的关联
    logger.log("删除通知与用户的关联...");
    await prisma.$executeRaw`
      DELETE FROM "user_notification" WHERE "notificationId" = ${id}
    `;

    // 然后删除通知本身
    await prisma.notification.delete({
      where: {
        id
      }
    });

    // 关闭Prisma客户端连接
    await prisma.$disconnect();

    logger.log(`通知 ${id} 删除成功`);

    return NextResponse.json({
      success: true,
      message: '删除通知成功',
    });
  } catch (error) {
    logger.error("删除通知错误:", error);
    return NextResponse.json({
      success: false,
      message: '删除通知失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}