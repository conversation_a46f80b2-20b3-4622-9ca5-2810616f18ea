import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuthMiddleware } from '@/lib/middleware/auth-middleware';
import logger from '@/lib/utils/logger';

/**
 * 通知统计API
 *
 * 此API用于获取通知系统的统计数据，包括：
 * 1. 通知总数、已读数、未读数
 * 2. 通知阅读率趋势
 * 3. 通知类型分布
 * 4. 通知优先级分布
 *
 * 仅管理员可访问此API
 */

export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "获取通知统计数据API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '无权访问通知统计数据'
      }, { status: 403 });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'week'; // 'day', 'week', 'month', 'year'

    // 计算日期范围
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 7); // 默认一周
    }

    // 获取通知总数
    const totalNotifications = await prisma.notification.count({
      where: {
        createdAt: {
          gte: startDate
        }
      }
    });

    // 获取已读通知数
    const readNotifications = await prisma.notification.count({
      where: {
        createdAt: {
          gte: startDate
        },
        readCount: {
          gt: 0
        }
      }
    });

    // 获取通知类型分布
    logger.log('获取通知类型分布数据...');
    const notificationTypeDistribution = await prisma.notification.groupBy({
      by: ['typeId'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    });
    logger.log('通知类型分布原始数据:', notificationTypeDistribution);

    // 获取通知类型信息
    const notificationTypes = await prisma.notificationType.findMany({
      select: {
        id: true,
        name: true,
        code: true
      }
    });
    logger.log('通知类型信息:', notificationTypes);

    // 将类型ID映射到类型名称
    const typeMap = notificationTypes.reduce((map, type) => {
      map[type.id] = { name: type.name, code: type.code };
      return map;
    }, {} as Record<string, { name: string, code: string }>);
    logger.log('类型映射:', typeMap);

    // 如果没有类型分布数据，则使用所有类型并设置计数为0
    let typeDistribution;
    if (notificationTypeDistribution.length === 0 && notificationTypes.length > 0) {
      logger.log('没有类型分布数据，使用所有类型并设置计数为0');
      typeDistribution = notificationTypes.map(type => ({
        typeId: type.id,
        typeName: type.name,
        typeCode: type.code,
        count: 0
      }));
    } else {
      // 格式化类型分布数据
      typeDistribution = notificationTypeDistribution.map(item => ({
        typeId: item.typeId,
        typeName: typeMap[item.typeId]?.name || '未知类型',
        typeCode: typeMap[item.typeId]?.code || 'unknown',
        count: item._count.id
      }));
    }
    logger.log('格式化后的类型分布数据:', typeDistribution);

    // 获取通知优先级分布
    const priorityDistribution = await prisma.notification.groupBy({
      by: ['priority'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    });

    // 获取阅读率趋势数据
    // 根据时间段划分不同的时间间隔
    let interval: 'day' | 'week' | 'month' = 'day';
    if (period === 'year') {
      interval = 'month';
    } else if (period === 'month') {
      interval = 'week';
    } else {
      interval = 'day';
    }

    // 获取每个时间间隔的通知数和已读数
    const readRateTrend = await getReadRateTrend(startDate, now, interval);

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          total: totalNotifications,
          read: readNotifications,
          unread: totalNotifications - readNotifications,
          readRate: totalNotifications > 0 ? (readNotifications / totalNotifications) : 0
        },
        typeDistribution,
        priorityDistribution,
        readRateTrend
      }
    });
  } catch (error) {
    logger.error('获取通知统计数据错误:', error);
    return NextResponse.json({
      success: false,
      message: '获取通知统计数据失败'
    }, { status: 500 });
  }
}

/**
 * 获取阅读率趋势数据
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param interval 时间间隔 ('day' | 'week' | 'month')
 */
async function getReadRateTrend(startDate: Date, endDate: Date, interval: 'day' | 'week' | 'month') {
  // 根据时间间隔生成时间点
  const timePoints = generateTimePoints(startDate, endDate, interval);

  // 获取每个时间点的通知数据
  const result = [];

  for (let i = 0; i < timePoints.length - 1; i++) {
    const currentStart = timePoints[i];
    const currentEnd = timePoints[i + 1];

    // 获取该时间段内的通知总数
    const totalCount = await prisma.notification.count({
      where: {
        createdAt: {
          gte: currentStart,
          lt: currentEnd
        }
      }
    });

    // 获取该时间段内的已读通知数
    const readCount = await prisma.notification.count({
      where: {
        createdAt: {
          gte: currentStart,
          lt: currentEnd
        },
        readCount: {
          gt: 0
        }
      }
    });

    // 计算阅读率
    const readRate = totalCount > 0 ? readCount / totalCount : 0;

    // 格式化日期
    const dateLabel = formatDate(currentStart, interval);

    result.push({
      date: dateLabel,
      total: totalCount,
      read: readCount,
      unread: totalCount - readCount,
      readRate
    });
  }

  return result;
}

/**
 * 生成时间点数组
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param interval 时间间隔 ('day' | 'week' | 'month')
 */
function generateTimePoints(startDate: Date, endDate: Date, interval: 'day' | 'week' | 'month') {
  const timePoints = [new Date(startDate)];
  let currentDate = new Date(startDate);

  while (currentDate < endDate) {
    const nextDate = new Date(currentDate);

    switch (interval) {
      case 'day':
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case 'week':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'month':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
    }

    // 确保不超过结束日期
    if (nextDate > endDate) {
      timePoints.push(new Date(endDate));
    } else {
      timePoints.push(new Date(nextDate));
    }

    currentDate = nextDate;
  }

  return timePoints;
}

/**
 * 格式化日期
 * @param date 日期
 * @param interval 时间间隔 ('day' | 'week' | 'month')
 */
function formatDate(date: Date, interval: 'day' | 'week' | 'month') {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  switch (interval) {
    case 'day':
      return `${year}-${month}-${day}`;
    case 'week':
      return `${year}-${month}-${day}`;
    case 'month':
      return `${year}-${month}`;
    default:
      return `${year}-${month}-${day}`;
  }
}
