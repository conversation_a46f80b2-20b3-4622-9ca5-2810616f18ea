import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { options } from "../../../auth/[...nextauth]/options"
import { hash } from "bcryptjs"

/**
 * 重置用户密码
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[管理员用户API:${requestId}] 重置用户密码: ${params.id}`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)

    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以重置用户密码",
        requestId
      }, { status: 403 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 生成随机密码
    const randomPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)

    // 加密密码
    const hashedPassword = await hash(randomPassword, 10)

    // 更新用户密码
    await prisma.user.update({
      where: { id: params.id },
      data: {
        password: hashedPassword
      }
    })

    // TODO: 发送邮件通知用户密码已重置

    return NextResponse.json({
      success: true,
      message: '密码重置成功',
      data: {
        newPassword: randomPassword
      },
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 重置用户密码失败:`, error)
    return NextResponse.json({
      success: false,
      message: "重置用户密码失败",
      requestId
    }, { status: 500 })
  }
}
