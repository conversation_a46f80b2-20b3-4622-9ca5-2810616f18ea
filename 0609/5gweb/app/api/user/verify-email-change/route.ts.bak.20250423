import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { verifyVerificationCode } from "@/lib/services/verification"
// 直接在这里实现 verifyToken 函数
import * as jose from 'jose';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

async function verifyToken(token: string) {
  try {
    console.log('开始验证令牌:', token.substring(0, 20) + '...');
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    console.log('令牌验证成功, 载荷:', payload);
    return payload;
  } catch (error) {
    console.error('令牌验证失败:', error);
    throw error;
  }
}
import { cookies } from "next/headers"

/**
 * 验证邮箱修改响应接口
 */
interface VerifyEmailChangeResponse {
  code: number
  success: boolean
  message: string
  data?: any
}

/**
 * 验证邮箱修改请求验证 schema
 */
const verifyEmailChangeSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  code: z.string().min(6, '验证码格式不正确').max(6, '验证码格式不正确'),
})

/**
 * 处理验证邮箱修改请求
 * POST /api/user/verify-email-change
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 验证邮箱修改响应
 */
export async function POST(request: NextRequest): Promise<NextResponse<VerifyEmailChangeResponse>> {
  try {
    console.log('收到验证邮箱修改请求')
    console.log('请求头:', request.headers)
    console.log('请求方法:', request.method)
    console.log('请求URL:', request.url)

    // 验证用户是否已登录
    console.log('检查用户认证状态...')

    // 获取所有cookie
    const cookieStore = cookies()
    const allCookies = cookieStore.getAll()
    console.log('所有cookie:', allCookies.map(c => c.name))

    // 尝试从不同的cookie名称获取token
    let token = cookieStore.get('auth_token')?.value
    if (!token) {
      token = cookieStore.get('token')?.value
    }

    // 如果还是没有找到token，尝试从请求头中获取
    if (!token) {
      const authHeader = request.headers.get('Authorization')
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7)
      }
    }

    console.log('获取到的 token:', token ? token.substring(0, 20) + '...' : '不存在')

    if (!token) {
      console.log('未找到认证令牌')
      return NextResponse.json({
        code: 401,
        success: false,
        message: '未登录或会话已过期'
      }, { status: 401 })
    }

    // 验证令牌
    console.log('开始验证令牌...')
    let decoded;
    try {
      // 尝试直接解析token，以便调试
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
          console.log('令牌载荷解析结果:', payload);
        }
      } catch (e) {
        console.log('解析令牌载荷失败:', e);
      }

      decoded = await verifyToken(token);
      console.log('令牌验证成功:', decoded);
    } catch (error) {
      console.log('令牌验证失败:', error)
      return NextResponse.json({
        code: 401,
        success: false,
        message: '无效的认证令牌'
      }, { status: 401 })
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId as string }
    })

    if (!user) {
      console.log('用户不存在:', decoded.userId)
      return NextResponse.json({
        code: 401,
        success: false,
        message: '用户不存在'
      }, { status: 401 })
    }

    console.log('当前用户:', user.id, user.username)

    const body = await request.json()
    console.log('请求体:', body)

    // 验证请求参数
    const result = verifyEmailChangeSchema.safeParse(body)
    if (!result.success) {
      console.log('参数验证失败:', result.error.errors)
      return NextResponse.json({
        code: 400,
        success: false,
        message: result.error.errors[0].message
      }, { status: 400 })
    }

    const { email, code } = result.data
    console.log('参数验证通过:', { email, code })

    // 检查邮箱是否已被其他用户注册
    console.log('检查邮箱是否已被其他用户注册...')
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        id: { not: user.id }
      }
    })

    if (existingUser) {
      console.log('邮箱已被其他用户注册:', existingUser.id)
      return NextResponse.json({
        code: 400,
        success: false,
        message: '该邮箱已被其他用户注册'
      }, { status: 400 })
    }

    // 验证验证码
    console.log('验证验证码...')
    const isValid = await verifyVerificationCode(email, code, "email_change")
    if (!isValid) {
      console.log('验证码无效')
      return NextResponse.json({
        code: 400,
        success: false,
        message: '验证码无效或已过期'
      }, { status: 400 })
    }

    // 更新用户邮箱
    console.log('更新用户邮箱...')
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        email,
        emailVerified: new Date()
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        image: true,
        emailVerified: true,
        roleCode: true,
        role: {
          select: {
            name: true,
            code: true,
            type: true
          }
        }
      }
    })

    console.log('用户邮箱更新成功:', updatedUser)
    return NextResponse.json({
      code: 200,
      success: true,
      message: '邮箱修改成功',
      data: updatedUser
    })
  } catch (error) {
    console.error('验证邮箱修改失败:', error)
    return NextResponse.json({
      code: 500,
      success: false,
      message: '验证邮箱修改失败，请稍后重试'
    }, { status: 500 })
  }
}
