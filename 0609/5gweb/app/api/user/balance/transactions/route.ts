/**
 * 用户余额交易记录API
 * 处理用户查询自己的余额交易记录
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "../../../../../lib/unified-auth-service"

/**
 * 获取当前用户的余额交易记录
 *
 * @route GET /api/user/balance/transactions
 * @access 需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 获取当前用户
    const user = await UnifiedAuthService.getCurrentUser(request, "余额记录API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    console.log("[API] GET /api/user/balance/transactions - 当前用户:", user.id)

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') || undefined
    const startDate = searchParams.get('startDate') || undefined
    const endDate = searchParams.get('endDate') || undefined

    // 构建查询条件
    const where: any = {
      userId: user.id,
    }

    if (type && type !== 'all') {
      where.type = type
    }

    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    } else if (startDate) {
      where.createdAt = {
        gte: new Date(startDate),
      }
    } else if (endDate) {
      where.createdAt = {
        lte: new Date(endDate),
      }
    }

    console.log("[API] GET /api/user/balance/transactions - 查询条件:", where)

    // 查询总记录数
    const total = await prisma.balanceTransaction.count({
      where,
    })

    console.log("[API] GET /api/user/balance/transactions - 总记录数:", total)

    // 查询交易记录
    const transactions = await prisma.balanceTransaction.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        amount: true,
        balanceAfter: true,
        creditLimitChange: true,
        creditLimitAfter: true,
        type: true,
        paymentMethod: true,
        remarks: true,
        createdAt: true,
        admin: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    })

    console.log("[API] GET /api/user/balance/transactions - 查询到的记录数:", transactions.length)

    // 返回交易记录
    return NextResponse.json(
      {
        success: true,
        message: '获取余额交易记录成功',
        data: {
          transactions,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          },
        },
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error('[API] GET /api/user/balance/transactions - 错误:', error)

    // 返回更详细的错误信息
    const errorMessage = error instanceof Error ? error.message : '服务器错误，请稍后再试'
    const errorDetail = error instanceof Error && error.stack ? error.stack : 'No stack trace'

    console.error('[API] GET /api/user/balance/transactions - 错误详情:', errorDetail)

    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
        error: 'InternalServerError'
      },
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
