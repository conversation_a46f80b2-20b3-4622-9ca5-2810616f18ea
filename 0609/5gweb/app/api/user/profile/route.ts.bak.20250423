import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getToken } from "next-auth/jwt"
import * as jose from 'jose'

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

export async function GET(req: NextRequest) {
  try {
    // 1. 首先尝试从 NextAuth session 获取用户信息
    const session = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET
    })

    let userId = session?.sub

    // 2. 如果没有 session，尝试从 JWT token 获取
    if (!userId) {
      const token = req.cookies.get("token")?.value
      if (!token) {
        return NextResponse.json(
          { success: false, message: "未授权访问" },
          { status: 401 }
        )
      }

      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        userId = payload.sub as string
      } catch (error) {
        return NextResponse.json(
          { success: false, message: "无效的认证信息" },
          { status: 401 }
        )
      }
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 清除敏感信息
    const { password, ...safeUser } = user

    return NextResponse.json({
      success: true,
      data: safeUser
    })
  } catch (error) {
    console.error("获取用户资料失败:", error)
    return NextResponse.json(
      { success: false, message: "获取用户资料失败" },
      { status: 500 }
    )
  }
}

export async function PUT(req: NextRequest) {
  try {
    // 1. 获取用户 ID（与 GET 方法相同的认证逻辑）
    const session = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET
    })

    let userId = session?.sub

    if (!userId) {
      const token = req.cookies.get("token")?.value
      if (!token) {
        return NextResponse.json(
          { success: false, message: "未授权访问" },
          { status: 401 }
        )
      }

      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        userId = payload.sub as string
      } catch (error) {
        return NextResponse.json(
          { success: false, message: "无效的认证信息" },
          { status: 401 }
        )
      }
    }

    // 2. 获取请求数据
    const data = await req.json()

    // 3. 验证用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 4. 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: data.name,
        email: data.email,
        // 仅更新允许的字段
      },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    // 5. 清除敏感信息并返回
    const { password, ...safeUser } = updatedUser

    return NextResponse.json({
      success: true,
      data: safeUser
    })
  } catch (error) {
    console.error("更新用户资料失败:", error)
    return NextResponse.json(
      { success: false, message: "更新用户资料失败" },
      { status: 500 }
    )
  }
}