import logger from '@/lib/utils/logger';

/**
 * 用户仪表盘统计API
 * 提供用户任务、通话记录和费用的统计信息
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"

/**
 * 获取用户仪表盘统计数据
 *
 * @route GET /api/user/dashboard/stats
 * @access 需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 获取当前用户
    const user = await UnifiedAuthService.getCurrentUser(request, "仪表盘统计API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const period = searchParams.get('period') || 'week' // day, week, month, year

    // 计算时间范围
    const endDate = new Date()
    let startDate = new Date()

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1)
        break
      case 'week':
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
    }

    // 准备默认的空数据结构
    let taskStats = [];
    let callStats = [];
    let costStats = { _sum: { amount: 0 } };
    let taskTrend = [];
    let connectionRateTrend = [];
    let intentionDistribution = [];

    try {
      // 查询用户任务统计
      // 检查 prisma.task 是否存在
      const hasPrismaTask = typeof prisma === 'object' && prisma !== null && 'task' in prisma;

      if (hasPrismaTask) {
        taskStats = await prisma.task.groupBy({
          by: ['status'],
          where: {
            userId: user.id,
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          },
          _count: {
            id: true
          }
        });
      } else {
        logger.log('prisma.task 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取任务统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询通话记录统计
      // 检查 prisma.callRecord 是否存在
      const hasPrismaCallRecord = typeof prisma === 'object' && prisma !== null && 'callRecord' in prisma;

      if (hasPrismaCallRecord) {
        callStats = await prisma.callRecord.groupBy({
          by: ['connectionType'],
          where: {
            userId: user.id,
            startTime: {
              gte: startDate,
              lte: endDate
            }
          },
          _count: {
            id: true
          },
          _sum: {
            duration: true
          }
        });
      } else {
        logger.log('prisma.callRecord 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取通话记录统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询费用统计
      // 检查 prisma.balanceTransaction 是否存在
      const hasPrismaBalanceTransaction = typeof prisma === 'object' && prisma !== null && 'balanceTransaction' in prisma;

      if (hasPrismaBalanceTransaction) {
        costStats = await prisma.balanceTransaction.aggregate({
          where: {
            userId: user.id,
            type: 'deduct',
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          },
          _sum: {
            amount: true
          }
        });
      } else {
        logger.log('prisma.balanceTransaction 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取费用统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询任务趋势数据（按天统计）
      // 检查Task表是否存在
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'Task'
        );
      `;

      if (tableExists[0].exists) {
        taskTrend = await prisma.$queryRaw`
          SELECT
            DATE(createdAt) as date,
            COUNT(*) as count
          FROM Task
          WHERE userId = ${user.id}
            AND createdAt >= ${startDate}
            AND createdAt <= ${endDate}
          GROUP BY DATE(createdAt)
          ORDER BY date ASC
        `;
      }
    } catch (error) {
      logger.log('获取任务趋势数据失败，可能是表不存在:', error);
    }

    try {
      // 查询接通率趋势数据（按天统计）
      // 检查CallRecord表是否存在
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'CallRecord'
        );
      `;

      if (tableExists[0].exists) {
        connectionRateTrend = await prisma.$queryRaw`
          SELECT
            DATE(startTime) as date,
            COUNT(CASE WHEN isConnected = true THEN 1 END) as connectedCount,
            COUNT(*) as totalCount
          FROM CallRecord
          WHERE userId = ${user.id}
            AND startTime >= ${startDate}
            AND startTime <= ${endDate}
          GROUP BY DATE(startTime)
          ORDER BY date ASC
        `;
      }
    } catch (error) {
      logger.log('获取接通率趋势数据失败，可能是表不存在:', error);
    }

    try {
      // 查询意图分布
      // 检查 prisma.callRecord 是否存在
      const hasPrismaCallRecord = typeof prisma === 'object' && prisma !== null && 'callRecord' in prisma;

      if (hasPrismaCallRecord) {
        intentionDistribution = await prisma.callRecord.groupBy({
          by: ['intention'],
          where: {
            userId: user.id,
            startTime: {
              gte: startDate,
              lte: endDate
            },
            intention: {
              not: null
            }
          },
          _count: {
            id: true
          }
        });
      } else {
        logger.log('prisma.callRecord 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取意图分布失败，可能是模型不存在:', error);
    }

    // 计算总任务数
    const totalTasks = Array.isArray(taskStats) ? taskStats.reduce((sum, stat) => sum + (stat._count?.id || 0), 0) : 0

    // 计算总通话数
    const totalCalls = Array.isArray(callStats) ? callStats.reduce((sum, stat) => sum + (stat._count?.id || 0), 0) : 0

    // 计算总通话时长（秒）
    const totalDuration = Array.isArray(callStats) ? callStats.reduce((sum, stat) => sum + (stat._sum?.duration || 0), 0) : 0

    // 计算总费用
    const totalCost = Math.abs(costStats?._sum?.amount || 0)

    // 计算接通率
    const connectedCalls = Array.isArray(callStats) ? callStats.find(stat => stat.connectionType === 'VIDEO' || stat.connectionType === 'AUDIO') : null
    const connectionRate = totalCalls > 0 ? ((connectedCalls?._count?.id || 0) / totalCalls) : 0

    // 准备月度数据（如果没有数据，生成空数据）
    let monthlyData = [];
    if (!taskTrend || taskTrend.length === 0) {
      // 生成最近6个月的空数据
      for (let i = 5; i >= 0; i--) {
        const month = new Date();
        month.setMonth(month.getMonth() - i);
        const monthName = month.toLocaleString('zh-CN', { month: 'short' });
        monthlyData.push({
          name: monthName,
          total: 0,
          completed: 0,
          pending: 0
        });
      }
    } else {
      // 将任务趋势数据转换为月度数据
      const monthMap = new Map();
      taskTrend.forEach(item => {
        const date = new Date(item.date);
        const monthName = date.toLocaleString('zh-CN', { month: 'short' });

        if (!monthMap.has(monthName)) {
          monthMap.set(monthName, {
            name: monthName,
            total: 0,
            completed: 0,
            pending: 0
          });
        }

        const monthData = monthMap.get(monthName);
        monthData.total += parseInt(item.count);
      });

      monthlyData = Array.from(monthMap.values());
    }

    // 准备最近活动数据（如果没有数据，返回空数组）
    const recentActivities = [];

    // 获取用户余额和信用额度
    let totalBalance = user.balance || 0;
    let creditLimit = user.creditLimit || 0;
    logger.log(`用户 ${user.id} 的余额: ${totalBalance}, 授信额度: ${creditLimit}`);

    return NextResponse.json(
      {
        success: true,
        message: '获取仪表盘统计成功',
        data: {
          summary: {
            totalBalance,
            creditLimit,
            totalTasks,
            totalCalls,
            totalDuration,
            totalCost,
            connectionRate
          },
          monthlyData,
          recentActivities,
          taskStats,
          callStats,
          costStats: {
            totalCost
          },
          trends: {
            taskTrend,
            connectionRateTrend
          },
          distributions: {
            intentionDistribution
          }
        }
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("获取仪表盘统计错误:", error)
    return NextResponse.json(
      {
        success: false,
        message: '获取仪表盘统计失败: ' + (error instanceof Error ? error.message : '未知错误')
      },
      { status: 500 }
    )
  }
}
