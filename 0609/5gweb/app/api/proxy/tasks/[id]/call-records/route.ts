import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import AesUtils from "@/lib/api/crypto"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 代理获取任务的通话记录
 * 解决CORS问题
 */
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取通话记录API");
    if (response) {
      return response;
    }

    const taskId = params.id;
    
    const requestData = {
      orgCode: ORG_CODE,
      sign: AesUtils.generateSignature(ORG_CODE),
      loginName: LOGIN_NAME,
      mediaDeliverId: taskId,
      pageNum: 1,
      pageSize: 100, // 获取前100条记录
    };

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/queryCallRecords`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify(requestData),
    });

    const data = await apiResponse.json();

    if (data.errCode === 0) {
      // 处理通话记录数据
      const callRecords = data.result?.list || [];

      // 对于每个通话记录，获取详细信息
      const detailedRecords = await Promise.all(
        callRecords.map(async (record: any) => {
          try {
            // 如果有callId，获取通话详情
            if (record.callId) {
              const detailData = {
                orgCode: ORG_CODE,
                sign: AesUtils.generateSignature(ORG_CODE),
                loginName: LOGIN_NAME,
                callId: record.callId,
              };

              const detailResponse = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/queryCallDetail`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json; charset=utf-8",
                },
                body: JSON.stringify(detailData),
              });

              const detailResult = await detailResponse.json();

              if (detailResult.errCode === 0) {
                // 合并基本记录和详细信息
                return {
                  ...record,
                  ...detailResult.result,
                };
              }
            }
            return record;
          } catch (error) {
            return record;
          }
        })
      );

      return NextResponse.json({
        success: true,
        message: "获取通话记录成功",
        data: detailedRecords,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取通话记录失败: ${data.errInfo || "未知错误"}`,
        data: [],
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "获取通话记录失败，请稍后重试",
      data: [],
    }, { status: 500 });
  }
}
