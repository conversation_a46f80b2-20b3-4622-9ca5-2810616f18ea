import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import AesUtils from "@/lib/api/crypto"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 获取请求头部
 */
function getHeaders() {
  const signature = AesUtils.generateSignature(ORG_CODE)
  return {
    "Content-Type": "application/json; charset=utf-8",
    "access-token": signature,
  }
}

/**
 * 根据callType获取任务类型
 * @param callType API返回的callType
 * @returns 项目中使用的任务类型
 */
function getTaskTypeFromCallType(callType: number): string {
  switch (callType) {
    case 1:
      return "5G视频通知";
    case 2:
      return "5G视频互动";
    case 3:
      return "5G语音互动";
    default:
      return "5G视频通知";
  }
}

/**
 * 将API任务状态映射到项目状态
 * @param apiStatus API返回的任务状态
 * @returns 项目中使用的状态
 */
function mapTaskStatus(apiStatus: string): string {
  switch (apiStatus) {
    case "initialized":
      return "未开始";
    case "started":
      return "外呼中";
    case "paused":
      return "已暂停";
    case "finished":
      return "已完成";
    case "stopped":
      return "已停止";
    default:
      return "未知";
  }
}

/**
 * 代理获取任务详情
 * 解决CORS问题
 */
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取任务详情API");
    if (response) {
      return response;
    }

    const taskId = params.id;
    
    // 构建查询参数
    const queryParams = new URLSearchParams({
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
    }).toString();

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(`${API_BASE_URL}/openapi/callout/task/${taskId}?${queryParams}`, {
      method: "GET",
      headers: getHeaders(),
    });

    const data = await apiResponse.json();

    if (data.errCode === 0) {
      // 转换数据格式，只保留与当前项目页面匹配的字段
      const taskDetail = {
        id: data.result.taskId,
        name: data.result.name || taskId,
        type: getTaskTypeFromCallType(data.result.callType),
        content: data.result.content || data.result.name || "任务内容",
        status: mapTaskStatus(data.result.status),
        progress: data.result.status === "finished" ? 100 : 0,
        importTime: data.result.createdAt,
        startTime: data.result.settingStartTime || data.result.createdAt,
        completionTime: data.result.endedAt,
        creator: data.result.creator || "系统导入",
        userId: user.id,
        externalId: data.result.taskId,
        createdAt: data.result.createdAt,
        updatedAt: data.result.createdAt,
        // 额外的详情字段
        importNumber: data.result.importNumber,
        deliverNumber: data.result.deliverNumber,
        calledCount: data.result.calledCount,
        videoBotName: data.result.videoBotName,
        videoBotId: data.result.videoBotId,
        audioBotName: data.result.audioBotName,
        audioBotId: data.result.audioBotId,
        concurrency: data.result.concurrency,
      };

      return NextResponse.json({
        success: true,
        message: "获取任务详情成功",
        data: taskDetail,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取任务详情失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "获取任务详情失败，请稍后重试",
      data: null,
    }, { status: 500 });
  }
}
