import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import AesUtils from "@/lib/api/crypto"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 获取请求头部
 */
function getHeaders() {
  const signature = AesUtils.generateSignature(ORG_CODE)
  return {
    "Content-Type": "application/json; charset=utf-8",
    "access-token": signature,
  }
}

/**
 * 代理获取任务统计数据
 * 解决CORS问题
 */
export async function POST(request: Request) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取任务统计数据API");
    if (response) {
      return response;
    }

    // 解析请求体
    const body = await request.json();
    const { taskIds } = body;

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      return NextResponse.json({
        success: false,
        message: "请提供有效的任务ID列表",
        data: null,
      }, { status: 400 });
    }

    const requestData = {
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
      mediaDeliverIds: taskIds,
    };

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(`${API_BASE_URL}/openapi/task/panels`, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify(requestData),
    });

    const data = await apiResponse.json();

    if (data.errCode === 0) {
      // 获取通话记录
      try {
        // 调用通话记录API
        const callRecordsResponse = await fetch(`/api/proxy/tasks/${taskIds[0]}/call-records`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        const callRecordsData = await callRecordsResponse.json();

        if (callRecordsData.success) {
          // 将通话记录添加到统计数据中
          return NextResponse.json({
            success: true,
            message: "获取任务统计数据成功",
            data: {
              ...data.result,
              callRecords: callRecordsData.data
            },
          });
        }
      } catch (callRecordsError) {
        // 忽略获取通话记录的错误，继续返回统计数据
      }

      return NextResponse.json({
        success: true,
        message: "获取任务统计数据成功",
        data: data.result,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取任务统计数据失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "获取任务统计数据失败，请稍后重试",
      data: null,
    }, { status: 500 });
  }
}
