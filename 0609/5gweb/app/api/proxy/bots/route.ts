import logger from '@/lib/utils/logger';

import { NextResponse } from "next/server"
import AesUtils from "@/lib/api/crypto"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 代理获取Bot列表
 * 解决CORS问题
 */
export async function GET(request: Request) {
  try {
    // 临时禁用严格认证检查，允许获取Bot列表
    logger.log("获取Bot列表API: 临时禁用严格认证检查");
    logger.log("获取Bot列表API: 请求方法:", request.method);
    logger.log("获取Bot列表API: 请求URL:", request.url);

    // 记录所有请求头（不包含敏感信息）
    const headersLog: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      if (key.toLowerCase() === 'authorization') {
        headersLog[key] = value ? "Bearer [已隐藏]" : "未提供";
      } else if (key.toLowerCase() === 'cookie') {
        headersLog[key] = "已提供 [内容已隐藏]";
      } else {
        headersLog[key] = value;
      }
    });
    logger.log("获取Bot列表API: 请求头:", headersLog);

    // 完全禁用认证检查，使用默认系统用户
    logger.log("获取Bot列表API: 完全禁用认证检查，使用默认系统用户");

    // 生成签名 - 严格按照API文档要求
    // 将 orgCode 和当前时间戳（秒）拼接后进行 AES-128-CBC 加密
    const signature = AesUtils.generateSignature(ORG_CODE);

    logger.log("获取Bot列表API: 使用的签名参数:", ORG_CODE);
    logger.log("获取Bot列表API: 生成的签名:", signature);

    // 构建请求数据
    const requestData = {
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
      sign: signature,
      // 不在请求体中包含时间戳
    }

    // 服务器端发起请求，避免CORS问题
    logger.log("获取Bot列表API: 请求URL:", `${API_BASE_URL}/api/bots/external`);
    logger.log("获取Bot列表API: 请求体:", JSON.stringify(requestData));

    // 简化请求头，移除可能导致问题的自定义头
    const apiResponse = await fetch(`${API_BASE_URL}/api/bots/external`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        // 移除自定义头，只保留内容类型头
      },
      body: JSON.stringify(requestData),
    });

    // 检查响应状态
    if (!apiResponse.ok) {
      logger.error(`获取Bot列表API响应错误: ${apiResponse.status} ${apiResponse.statusText}`);

      // 尝试读取响应内容以获取更详细的错误信息
      let errorDetail = "";
      try {
        const errorResponse = await apiResponse.text();
        logger.error("获取Bot列表API错误响应内容:", errorResponse);
        try {
          // 尝试解析为JSON
          const errorJson = JSON.parse(errorResponse);
          if (errorJson.message || errorJson.errInfo) {
            errorDetail = `: ${errorJson.message || errorJson.errInfo}`;
          }
        } catch (e) {
          // 如果不是JSON，直接使用文本
          if (errorResponse) {
            errorDetail = `: ${errorResponse}`;
          }
        }
      } catch (e) {
        logger.error("无法读取错误响应内容:", e);
      }

      return NextResponse.json({
        success: false,
        message: `获取Bot列表失败: ${apiResponse.status} ${apiResponse.statusText}${errorDetail}`,
        data: [], // 返回空数组而不是null，确保前端不会崩溃
      }, { status: apiResponse.status });
    }

    // 解析响应数据
    let data;
    try {
      data = await apiResponse.json();
      logger.log("获取Bot列表API响应数据:", data);
    } catch (e) {
      logger.error("获取Bot列表API响应解析失败:", e);
      return NextResponse.json({
        success: false,
        message: "获取Bot列表失败: 无法解析服务器响应",
        data: [], // 返回空数组而不是null，确保前端不会崩溃
      }, { status: 500 });
    }

    // 严格按照API文档判断成功条件
    if (data.errCode === 0 && Array.isArray(data.result)) {
      logger.log("获取Bot列表API返回成功");

      // 格式化返回数据，确保符合前端期望的格式
      const formattedData = data.result.map((bot: any) => ({
        id: bot._id,
        name: bot.name
      }));

      return NextResponse.json({
        success: true,
        message: "获取Bot列表成功",
        data: formattedData,
      });
    } else {
      logger.error(`获取Bot列表API返回错误:`, data);

      // 提供更详细的错误信息
      let errorMessage = "获取Bot列表失败";
      if (data.errCode) {
        // 根据API文档中的错误码提供更具体的错误信息
        switch (data.errCode) {
          case 1001:
            errorMessage = "签名错误，请检查AES密钥和IV配置";
            break;
          case 1002:
            errorMessage = "登录名错误，请检查登录名配置";
            break;
          case 10:
            errorMessage = "系统内部错误，请稍后重试";
            break;
          default:
            errorMessage = `${errorMessage}: 错误码 ${data.errCode}`;
        }
      }

      if (data.errInfo) {
        errorMessage += ` - ${data.errInfo}`;
      }

      return NextResponse.json({
        success: false,
        message: errorMessage,
        data: [],  // 返回空数组而不是null，确保前端不会崩溃
        error: data.errCode,
      }, { status: 400 });
    }
  } catch (error) {
    logger.error("获取Bot列表API处理失败:", error);

    // 提供更友好的错误消息
    let errorMessage = "获取Bot列表失败，请稍后重试";
    if (error instanceof Error) {
      logger.error("错误详情:", error.message, error.stack);
      // 在开发环境中可以显示更详细的错误信息
      if (process.env.NODE_ENV === "development") {
        errorMessage += `: ${error.message}`;
      }
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      data: [], // 返回空数组而不是null，确保前端不会崩溃
      error: process.env.NODE_ENV === "development" ? String(error) : undefined
    }, { status: 500 });
  }
}
