import { NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"
import { nanoid } from "nanoid"
import sharp from "sharp"
import { revalidatePath } from "next/cache"
import { prisma } from "@/lib/prisma"

// 允许的图片类型
const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp"
]

// 最大文件大小 (2MB)
const MAX_FILE_SIZE = 2 * 1024 * 1024

// 图片尺寸限制
const MIN_DIMENSION = 100
const MAX_DIMENSION = 1000
const TARGET_SIZE = 100 // 固定目标尺寸，统一处理为100x100px

/**
 * 验证图片文件头
 * 简单检查文件头以确保是有效的图片文件
 */
function validateImageHeader(buffer: Buffer, type: string): boolean {
  // 检查文件头
  if (type === 'jpeg' || type === 'jpg') {
    return buffer[0] === 0xFF && buffer[1] === 0xD8
  } else if (type === 'png') {
    return buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47
  } else if (type === 'gif') {
    return buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46
  } else if (type === 'webp') {
    // WebP检查更复杂，这里简化处理
    return true
  }
  return false
}

/**
 * 压缩图片
 * 使用sharp库压缩图片并调整尺寸为固定的100x100px
 */
async function compressImage(buffer: Buffer, format: string): Promise<Buffer> {
  // 获取图片信息
  const metadata = await sharp(buffer).metadata()

  // 固定尺寸为100x100px
  const width = TARGET_SIZE
  const height = TARGET_SIZE

  // 根据格式选择压缩方法
  // 使用fit: 'cover'确保图片被裁剪为正方形，不变形
  let processedImage = sharp(buffer).resize(width, height, {
    fit: 'cover',
    position: 'centre'
  })

  if (format === 'jpg' || format === 'jpeg') {
    return await processedImage.jpeg({ quality: 80 }).toBuffer()
  } else if (format === 'png') {
    return await processedImage.png({ compressionLevel: 8 }).toBuffer()
  } else if (format === 'webp') {
    return await processedImage.webp({ quality: 80 }).toBuffer()
  } else if (format === 'gif') {
    // GIF不进行压缩，保持原样
    return buffer
  }

  // 默认使用JPEG格式
  return await processedImage.jpeg({ quality: 80 }).toBuffer()
}

/**
 * 处理Logo上传
 * POST /api/upload/logo
 */
export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get("logo") as File

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: "请选择要上传的文件"
        },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: "只能上传JPG、PNG、GIF或WebP格式的图片"
        },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        {
          success: false,
          message: "文件大小不能超过2MB"
        },
        { status: 400 }
      )
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 获取文件扩展名
    const ext = file.name.split(".").pop()?.toLowerCase() || 'jpg'

    // 验证文件头，确保是有效的图片文件
    if (!validateImageHeader(buffer, ext)) {
      return NextResponse.json(
        {
          success: false,
          message: "无效的图片文件"
        },
        { status: 400 }
      )
    }

    // 使用sharp获取图片信息
    try {
      const metadata = await sharp(buffer).metadata()
      const width = metadata.width || 0
      const height = metadata.height || 0

      // 验证图片尺寸，只要求最小尺寸，因为我们会自动处理成100x100px
      if (width < MIN_DIMENSION || height < MIN_DIMENSION) {
        return NextResponse.json(
          {
            success: false,
            message: `图片尺寸过小，最小尺寸为${MIN_DIMENSION}x${MIN_DIMENSION}像素`
          },
          { status: 400 }
        )
      }

      // 不限制最大尺寸，因为我们会自动处理成100x100px
    } catch (error) {
      console.error("图片验证失败:", error)
      return NextResponse.json(
        {
          success: false,
          message: "无法验证图片，请确保上传的是有效图片"
        },
        { status: 400 }
      )
    }

    // 压缩图片
    let processedBuffer: Buffer
    try {
      processedBuffer = await compressImage(buffer, ext)
    } catch (error) {
      console.error("图片压缩失败:", error)
      // 如果压缩失败，使用原始图片
      processedBuffer = buffer
    }

    // 生成安全的文件名
    const timestamp = Date.now()
    const randomId = nanoid(8)
    const fileName = `logo-${timestamp}-${randomId}.${ext}`

    // 确保上传目录存在
    const uploadDir = join(process.cwd(), "public", "uploads", "logos")
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件
    const filePath = join(uploadDir, fileName)
    await writeFile(filePath, processedBuffer)

    // 返回文件URL
    const fileUrl = `/uploads/logos/${fileName}`

    // 添加时间戳参数，确保不使用缓存
    const fileUrlWithTimestamp = `${fileUrl}?t=${timestamp}`

    // 直接更新数据库中的Logo设置
    try {
      await prisma.systemSettings.update({
        where: { id: 1 },
        data: { logo: fileUrlWithTimestamp }
      })

      // 重新验证相关路径的缓存
      revalidatePath('/', 'layout') // 重新验证根路径的布局
      revalidatePath('/login', 'page') // 重新验证登录页面
      revalidatePath('/dashboard', 'layout') // 重新验证仪表盘布局
      revalidatePath('/settings', 'page') // 重新验证设置页面
    } catch (dbError) {
      console.error("更新数据库中的Logo失败:", dbError)
      // 即使数据库更新失败，仍然返回成功响应，因为文件已经上传成功
    }

    return NextResponse.json({
      success: true,
      url: fileUrlWithTimestamp,
      message: "Logo上传成功，已自动处理为100x100px"
    })
  } catch (error) {
    console.error("Logo上传失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: "服务器错误，Logo上传失败"
      },
      { status: 500 }
    )
  }
}
