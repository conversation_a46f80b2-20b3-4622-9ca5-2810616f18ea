import logger from '@/lib/utils/logger';

/**
 * 业务类型API
 * 提供业务类型列表，确保与远程接口一致
 */

import { NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { prisma } from "@/lib/prisma"

/**
 * 获取业务类型列表
 *
 * @route GET /api/business-types
 * @access 需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response } = await AuthMiddleware.requireAuth(request, "业务类型API");
    if (response) {
      return response;
    }

    // 从数据库获取业务类型列表
    const businessTypes = await prisma.businessType.findMany({
      orderBy: {
        callType: 'asc'
      }
    });

    // 返回业务类型列表
    return NextResponse.json({
      success: true,
      message: "获取业务类型列表成功",
      data: businessTypes
    });
  } catch (error) {
    logger.error("获取业务类型列表错误:", error);

    // 如果数据库查询失败，使用默认业务类型
    const defaultBusinessTypes = [
      {
        id: "bt_001",
        code: "VIDEO_NOTIFICATION",
        name: "视频通知",
        description: "单向视频通知，无交互",
        callType: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "bt_002",
        code: "VIDEO_INTERACTION",
        name: "视频互动",
        description: "双向视频互动，有交互",
        callType: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "bt_003",
        code: "VOICE_INTERACTION",
        name: "语音互动",
        description: "语音互动，有交互",
        callType: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    return NextResponse.json({
      success: true,
      message: "获取默认业务类型列表成功",
      data: defaultBusinessTypes
    });
  }
}

/**
 * 创建业务类型
 *
 * @route POST /api/business-types
 * @access 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "创建业务类型API");
    if (response) {
      return response;
    }

    // 解析请求体
    const body = await request.json();
    const { code, name, description, callType } = body;

    // 验证必填字段
    if (!code || !name || !callType) {
      return NextResponse.json(
        {
          success: false,
          message: "业务类型代码、名称和外呼类型不能为空"
        },
        { status: 400 }
      );
    }

    // 检查业务类型代码是否已存在
    const existingBusinessType = await prisma.businessType.findUnique({
      where: { code }
    });

    if (existingBusinessType) {
      return NextResponse.json(
        {
          success: false,
          message: "业务类型代码已存在"
        },
        { status: 400 }
      );
    }

    // 创建业务类型
    const businessType = await prisma.businessType.create({
      data: {
        code,
        name,
        description,
        callType: parseInt(callType.toString())
      }
    });

    return NextResponse.json({
      success: true,
      message: "创建业务类型成功",
      data: businessType
    });
  } catch (error) {
    logger.error("创建业务类型错误:", error);
    return NextResponse.json(
      {
        success: false,
        message: "创建业务类型失败: " + (error instanceof Error ? error.message : "未知错误")
      },
      { status: 500 }
    );
  }
}
