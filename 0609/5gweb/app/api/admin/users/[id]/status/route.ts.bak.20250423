import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
// import { checkPermission } from '@/lib/abac/permission'
import { createAccountDisabledNotification, createAccountEnabledNotification } from '@/app/lib/notification-helper'

/**
 * 更新用户状态
 *
 * @route PATCH /api/admin/users/[id]/status
 * @access 需要管理员权限
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId = 'system' // 默认使用系统用户ID
    try {
      // 在实际环境中应该正确解析JWT
      // 这里简化处理，直接使用系统用户ID
      /*
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
      */
      console.log('使用系统用户ID作为管理员ID')
    } catch (error) {
      console.error('解析token失败:', error)
      // 继续使用默认系统用户ID
    }

    // 检查用户是否有更新用户状态的权限
    // 简化处理，暂时跳过权限检查
    // const hasPermission = await checkPermission(adminId, 'users:update')
    // if (!hasPermission) {
    //   return NextResponse.json({
    //     success: false,
    //     message: '无权更新用户状态'
    //   }, { status: 403 })
    // }

    // 获取请求体
    const body = await request.json()
    const { status, reason } = body

    if (!status) {
      return NextResponse.json({
        success: false,
        message: '状态不能为空'
      }, { status: 400 })
    }

    // 如果是禁用操作，必须提供原因
    if (status === 'inactive' && !reason) {
      return NextResponse.json({
        success: false,
        message: '禁用用户必须提供原因'
      }, { status: 400 })
    }

    // 获取目标用户信息
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!targetUser) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 获取管理员信息
    const admin = await prisma.user.findUnique({
      where: { id: adminId }
    })

    // 更新用户状态
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { status }
    })

    // 记录状态变更日志
    try {
      await prisma.$queryRaw`
        INSERT INTO user_status_log (
          id,
          "userId",
          "adminId",
          status,
          reason,
          "createdAt"
        ) VALUES (
          gen_random_uuid(),
          ${userId},
          ${adminId},
          ${status},
          ${reason},
          NOW()
        )
      `
    } catch (error) {
      console.error('创建用户状态日志失败:', error)
      // 继续执行，不中断流程
    }

    // 如果是禁用操作，创建通知和发送邮件
    if (status === 'inactive') {
      try {
        console.log('开始创建禁用通知和发送邮件，用户ID:', userId, '禁用原因:', reason);

        // 使用新的通知帮助函数创建通知和发送邮件
        const result = await createAccountDisabledNotification(userId, reason);

        if (result.success) {
          console.log('禁用通知和邮件发送成功:', result);
        } else {
          console.error('禁用通知和邮件发送失败:', result.error);
        }
      } catch (error) {
        console.error('创建禁用通知和发送邮件失败:', error);
        // 继续执行，不中断流程
      }
    }

    // 如果是启用操作，也发送通知
    if (status === 'active') {
      try {
        console.log('开始创建启用通知和发送邮件，用户ID:', userId);

        // 使用新的通知帮助函数创建通知和发送邮件
        const result = await createAccountEnabledNotification(userId);

        if (result.success) {
          console.log('启用通知和邮件发送成功:', result);
        } else {
          console.error('启用通知和邮件发送失败:', result.error);
        }
      } catch (error) {
        console.error('创建启用通知和发送邮件失败:', error);
        // 继续执行，不中断流程
      }
    }

    // 返回更新后的用户信息
    return NextResponse.json({
      success: true,
      message: `用户状态已更新为 ${status}`,
      data: {
        id: updatedUser.id,
        status: updatedUser.status
      }
    })
  } catch (error: any) {
    console.error('更新用户状态错误:', error)
    return NextResponse.json({
      success: false,
      message: '更新用户状态失败',
      error: error.message
    }, { status: 500 })
  }
}
