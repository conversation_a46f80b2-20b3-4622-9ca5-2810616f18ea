import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 初始化管理员角色的菜单权限
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[管理员API:${requestId}] 初始化管理员角色的菜单权限`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "初始化管理员菜单权限API");
    if (response) {
      console.log(`[管理员API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 403 })
    }

    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    if (!adminRole) {
      return NextResponse.json({
        success: false,
        message: "管理员角色不存在",
        requestId
      }, { status: 404 })
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany()

    // 关联所有菜单到管理员角色
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        menus: {
          connect: allMenus.map(menu => ({ id: menu.id }))
        }
      }
    })

    // 为每个菜单添加jCasbin权限
    for (const menu of allMenus) {
      await CasbinService.addPermissionForRole('ADMIN', `menu:${menu.code}`, 'view')
    }

    // 添加管理员的通配符权限
    await CasbinService.addPermissionForRole('ADMIN', '*', '*')

    return NextResponse.json({
      success: true,
      message: "管理员角色的菜单权限初始化成功",
      data: {
        roleId: adminRole.id,
        menuCount: allMenus.length
      },
      requestId
    })
  } catch (error) {
    console.error(`[管理员API:${requestId}] 初始化管理员角色的菜单权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "初始化管理员角色的菜单权限失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
