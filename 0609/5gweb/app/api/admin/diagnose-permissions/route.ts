import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { PermissionDiagnosticService } from "@/lib/services/permission-diagnostic-service"

/**
 * 诊断权限问题
 * 仅管理员可访问
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[权限诊断API:${requestId}] 开始诊断权限问题`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "权限诊断API");
    if (response) {
      console.log(`[权限诊断API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以诊断权限问题",
        requestId
      }, { status: 403 })
    }

    // 执行权限诊断
    const diagnosticReport = await PermissionDiagnosticService.diagnosePermissionIssues()

    return NextResponse.json({
      success: true,
      data: diagnosticReport,
      requestId
    })
  } catch (error) {
    console.error(`[权限诊断API:${requestId}] 诊断权限问题失败:`, error)
    return NextResponse.json({
      success: false,
      message: "诊断权限问题失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
