import logger from '@/lib/utils/logger';

/**
 * 权限导出API
 * 提供权限配置的导出功能
 */

import { NextRequest, NextResponse } from "next/server"
import { PermissionExportService } from "@/lib/services/permission-export-service"
import { PermissionAuditService } from "@/lib/services/permission-audit-service"
import { format as formatDate } from "date-fns"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 导出权限配置
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[权限导出API:${requestId}] 导出权限配置`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "导出权限配置API");
    if (response) {
      console.log(`[权限导出API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以导出权限配置",
        requestId
      }, { status: 403 })
    }

    // 获取查询参数
    const url = new URL(request.url)
    const roleCode = url.searchParams.get('roleCode') || undefined
    const format = url.searchParams.get('format') || 'json'
    const includeMenus = url.searchParams.get('includeMenus') !== 'false'
    const includeOperations = url.searchParams.get('includeOperations') !== 'false'
    const includeResources = url.searchParams.get('includeResources') !== 'false'

    // 导出权限配置
    let content = ''
    let contentType = ''
    let filename = ''
    const timestamp = formatDate(new Date(), 'yyyyMMdd_HHmmss')

    if (format === 'csv') {
      content = await PermissionExportService.exportPermissionsToCSV({
        roleCode,
        includeMenus,
        includeOperations,
        includeResources
      })
      contentType = 'text/csv;charset=utf-8'
      filename = `permissions_${timestamp}.csv`
    } else {
      content = await PermissionExportService.exportPermissionsToJSON({
        roleCode,
        includeMenus,
        includeOperations,
        includeResources
      })
      contentType = 'application/json;charset=utf-8'
      filename = `permissions_${timestamp}.json`
    }

    // 记录审计日志
    await PermissionAuditService.log({
      userId: admin.id,
      action: 'view',
      targetType: 'role',
      targetId: roleCode || 'all',
      targetName: roleCode || '所有角色',
      details: {
        format,
        includeMenus,
        includeOperations,
        includeResources,
        timestamp
      }
    })

    // 设置响应头
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Content-Disposition', `attachment; filename="${filename}"`)

    return new NextResponse(content, {
      status: 200,
      headers
    })
  } catch (error) {
    console.error(`[权限导出API:${requestId}] 导出权限配置失败:`, error)
    return NextResponse.json({
      success: false,
      message: "导出权限配置失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
