import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { PermissionDiagnosticService } from "@/lib/services/permission-diagnostic-service"

/**
 * 修复权限问题
 * 仅管理员可访问
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[权限修复API:${requestId}] 开始修复权限问题`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "权限修复API");
    if (response) {
      console.log(`[权限修复API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以修复权限问题",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const { fixType } = body

    if (!fixType) {
      return NextResponse.json({
        success: false,
        message: "缺少必要参数: fixType",
        requestId
      }, { status: 400 })
    }

    // 根据修复类型执行不同的修复操作
    switch (fixType) {
      case 'createUserRole':
        return await createUserRole(requestId)
      case 'addUserRolePermissions':
        return await addUserRolePermissions(requestId)
      case 'addUserRoleMenus':
        return await addUserRoleMenus(requestId)
      case 'assignRolesToUsers':
        return await assignRolesToUsers(requestId)
      case 'syncCasbinPolicies':
        return await syncCasbinPolicies(requestId)
      case 'fixAll':
        return await fixAll(requestId)
      default:
        return NextResponse.json({
          success: false,
          message: `未知的修复类型: ${fixType}`,
          requestId
        }, { status: 400 })
    }
  } catch (error) {
    console.error(`[权限修复API:${requestId}] 修复权限问题失败:`, error)
    return NextResponse.json({
      success: false,
      message: "修复权限问题失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 创建 USER 角色
 */
async function createUserRole(requestId: string) {
  console.log(`[权限修复API:${requestId}] 创建 USER 角色`)

  // 检查 USER 角色是否已存在
  const existingRole = await prisma.role.findUnique({
    where: { code: 'USER' }
  })

  if (existingRole) {
    return NextResponse.json({
      success: false,
      message: "USER 角色已存在",
      requestId
    }, { status: 400 })
  }

  // 创建 USER 角色
  const userRole = await prisma.role.create({
    data: {
      code: 'USER',
      name: '普通用户',
      type: 'customer',
      description: '系统默认用户角色',
      permissions: PermissionDiagnosticService.BASIC_USER_PERMISSIONS
    }
  })

  return NextResponse.json({
    success: true,
    message: "USER 角色创建成功",
    data: userRole,
    requestId
  })
}

/**
 * 为 USER 角色添加缺失的权限
 */
async function addUserRolePermissions(requestId: string) {
  console.log(`[权限修复API:${requestId}] 为 USER 角色添加缺失的权限`)

  // 获取 USER 角色
  const userRole = await prisma.role.findUnique({
    where: { code: 'USER' }
  })

  if (!userRole) {
    return NextResponse.json({
      success: false,
      message: "USER 角色不存在",
      requestId
    }, { status: 404 })
  }

  // 找出缺失的权限
  const missingPermissions = PermissionDiagnosticService.BASIC_USER_PERMISSIONS.filter(
    perm => !userRole.permissions.includes(perm)
  )

  if (missingPermissions.length === 0) {
    return NextResponse.json({
      success: true,
      message: "USER 角色已拥有所有基本权限",
      requestId
    })
  }

  // 更新角色权限
  const updatedRole = await prisma.role.update({
    where: { code: 'USER' },
    data: {
      permissions: [
        ...userRole.permissions,
        ...missingPermissions
      ]
    }
  })

  return NextResponse.json({
    success: true,
    message: `已为 USER 角色添加 ${missingPermissions.length} 个缺失的权限`,
    data: {
      addedPermissions: missingPermissions,
      role: updatedRole
    },
    requestId
  })
}

/**
 * 为 USER 角色添加缺失的菜单
 */
async function addUserRoleMenus(requestId: string) {
  console.log(`[权限修复API:${requestId}] 为 USER 角色添加缺失的菜单`)

  // 获取 USER 角色及其菜单
  const userRole = await prisma.role.findUnique({
    where: { code: 'USER' },
    include: { menus: true }
  })

  if (!userRole) {
    return NextResponse.json({
      success: false,
      message: "USER 角色不存在",
      requestId
    }, { status: 404 })
  }

  // 获取所有菜单
  const allMenus = await prisma.menu.findMany()

  // 找到用户应该看到的菜单
  const userMenus = allMenus.filter(menu => 
    PermissionDiagnosticService.BASIC_USER_MENUS.includes(menu.code) || 
    menu.path.includes('/user/') || 
    menu.path.includes('/tasks/') ||
    menu.path.includes('/notifications')
  )

  // 检查用户角色是否已经有这些菜单
  const existingMenuIds = userRole.menus.map(menu => menu.id)
  const menusToAdd = userMenus.filter(menu => !existingMenuIds.includes(menu.id))

  if (menusToAdd.length === 0) {
    return NextResponse.json({
      success: true,
      message: "USER 角色已拥有所有基本菜单",
      requestId
    })
  }

  // 为用户角色添加菜单
  await prisma.role.update({
    where: { code: 'USER' },
    data: {
      menus: {
        connect: menusToAdd.map(menu => ({ id: menu.id }))
      }
    }
  })

  return NextResponse.json({
    success: true,
    message: `已为 USER 角色添加 ${menusToAdd.length} 个缺失的菜单`,
    data: {
      addedMenus: menusToAdd.map(menu => menu.code)
    },
    requestId
  })
}

/**
 * 为没有角色的用户分配 USER 角色
 */
async function assignRolesToUsers(requestId: string) {
  console.log(`[权限修复API:${requestId}] 为没有角色的用户分配 USER 角色`)

  // 查找所有没有角色或角色不是 ADMIN 的用户
  const usersWithoutRole = await prisma.user.findMany({
    where: {
      OR: [
        { roleCode: null },
        { 
          roleCode: { 
            not: 'ADMIN',
            notIn: ['USER'] // 排除已经是 USER 的用户
          } 
        }
      ]
    },
    select: {
      id: true,
      username: true,
      email: true,
      roleCode: true
    }
  })

  if (usersWithoutRole.length === 0) {
    return NextResponse.json({
      success: true,
      message: "所有用户都已经有正确的角色",
      requestId
    })
  }

  // 更新用户角色
  const updatedUsers = []
  for (const user of usersWithoutRole) {
    // 如果用户没有角色，或者角色不是 ADMIN，则设置为 USER
    if (!user.roleCode || user.roleCode.toUpperCase() !== 'ADMIN') {
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { roleCode: 'USER' },
        select: {
          id: true,
          username: true,
          email: true,
          roleCode: true
        }
      })
      updatedUsers.push(updatedUser)
    }
  }

  return NextResponse.json({
    success: true,
    message: `已为 ${updatedUsers.length} 个用户分配 USER 角色`,
    data: {
      updatedUsers
    },
    requestId
  })
}

/**
 * 同步 jCasbin 策略与数据库权限
 */
async function syncCasbinPolicies(requestId: string) {
  console.log(`[权限修复API:${requestId}] 同步 jCasbin 策略与数据库权限`)

  // 获取 USER 角色及其菜单
  const userRole = await prisma.role.findUnique({
    where: { code: 'USER' },
    include: { menus: true }
  })

  if (!userRole) {
    return NextResponse.json({
      success: false,
      message: "USER 角色不存在",
      requestId
    }, { status: 404 })
  }

  // 获取 jCasbin 执行器
  const enforcer = await CasbinService.getEnforcer()

  // 清除 USER 角色的所有策略
  const policies = await enforcer.getFilteredPolicy(0, userRole.code)
  for (const policy of policies) {
    await enforcer.removePolicy(policy[0], policy[1], policy[2])
  }

  // 添加基本用户权限
  for (const permission of PermissionDiagnosticService.BASIC_USER_PERMISSIONS) {
    await enforcer.addPolicy(userRole.code, permission, '*')
  }

  // 添加菜单权限
  for (const menu of userRole.menus) {
    await enforcer.addPolicy(userRole.code, `menu:${menu.code}`, 'view')
  }

  // 保存策略
  await enforcer.savePolicy()

  // 清除缓存
  CasbinService.clearCache()

  return NextResponse.json({
    success: true,
    message: "jCasbin 策略已同步",
    requestId
  })
}

/**
 * 修复所有权限问题
 */
async function fixAll(requestId: string) {
  console.log(`[权限修复API:${requestId}] 修复所有权限问题`)

  // 1. 检查并创建 USER 角色
  let userRole = await prisma.role.findUnique({
    where: { code: 'USER' }
  })

  if (!userRole) {
    userRole = await prisma.role.create({
      data: {
        code: 'USER',
        name: '普通用户',
        type: 'customer',
        description: '系统默认用户角色',
        permissions: PermissionDiagnosticService.BASIC_USER_PERMISSIONS
      }
    })
    console.log(`[权限修复API:${requestId}] 创建了 USER 角色`)
  } else {
    // 2. 为 USER 角色添加缺失的权限
    const missingPermissions = PermissionDiagnosticService.BASIC_USER_PERMISSIONS.filter(
      perm => !userRole!.permissions.includes(perm)
    )

    if (missingPermissions.length > 0) {
      userRole = await prisma.role.update({
        where: { code: 'USER' },
        data: {
          permissions: [
            ...userRole.permissions,
            ...missingPermissions
          ]
        }
      })
      console.log(`[权限修复API:${requestId}] 为 USER 角色添加了 ${missingPermissions.length} 个缺失的权限`)
    }
  }

  // 3. 为 USER 角色添加缺失的菜单
  userRole = await prisma.role.findUnique({
    where: { code: 'USER' },
    include: { menus: true }
  }) as any

  const allMenus = await prisma.menu.findMany()
  const userMenus = allMenus.filter(menu => 
    PermissionDiagnosticService.BASIC_USER_MENUS.includes(menu.code) || 
    menu.path.includes('/user/') || 
    menu.path.includes('/tasks/') ||
    menu.path.includes('/notifications')
  )

  const existingMenuIds = userRole.menus.map((menu: any) => menu.id)
  const menusToAdd = userMenus.filter(menu => !existingMenuIds.includes(menu.id))

  if (menusToAdd.length > 0) {
    await prisma.role.update({
      where: { code: 'USER' },
      data: {
        menus: {
          connect: menusToAdd.map(menu => ({ id: menu.id }))
        }
      }
    })
    console.log(`[权限修复API:${requestId}] 为 USER 角色添加了 ${menusToAdd.length} 个缺失的菜单`)
  }

  // 4. 为没有角色的用户分配 USER 角色
  const usersWithoutRole = await prisma.user.findMany({
    where: {
      OR: [
        { roleCode: null },
        { 
          roleCode: { 
            not: 'ADMIN',
            notIn: ['USER'] 
          } 
        }
      ]
    }
  })

  if (usersWithoutRole.length > 0) {
    for (const user of usersWithoutRole) {
      if (!user.roleCode || user.roleCode.toUpperCase() !== 'ADMIN') {
        await prisma.user.update({
          where: { id: user.id },
          data: { roleCode: 'USER' }
        })
      }
    }
    console.log(`[权限修复API:${requestId}] 为 ${usersWithoutRole.length} 个用户分配了 USER 角色`)
  }

  // 5. 同步 jCasbin 策略与数据库权限
  const enforcer = await CasbinService.getEnforcer()
  
  // 清除 USER 角色的所有策略
  const policies = await enforcer.getFilteredPolicy(0, 'USER')
  for (const policy of policies) {
    await enforcer.removePolicy(policy[0], policy[1], policy[2])
  }

  // 添加基本用户权限
  for (const permission of PermissionDiagnosticService.BASIC_USER_PERMISSIONS) {
    await enforcer.addPolicy('USER', permission, '*')
  }

  // 添加菜单权限
  userRole = await prisma.role.findUnique({
    where: { code: 'USER' },
    include: { menus: true }
  }) as any

  for (const menu of userRole.menus) {
    await enforcer.addPolicy('USER', `menu:${menu.code}`, 'view')
  }

  // 保存策略
  await enforcer.savePolicy()

  // 清除缓存
  CasbinService.clearCache()
  console.log(`[权限修复API:${requestId}] 同步了 jCasbin 策略`)

  return NextResponse.json({
    success: true,
    message: "所有权限问题已修复",
    requestId
  })
}
