import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 获取用户认证资料
 * 
 * @route GET /api/admin/user/[id]/verification
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    
    // 尝试获取多种可能的token名称
    const tokenNames = ["token", "next-auth.session-token", "__Secure-next-auth.session-token"]
    let token = null
    
    for (const name of tokenNames) {
      const cookieValue = cookieStore.get(name)?.value
      if (cookieValue) {
        token = cookieValue
        console.log(`使用 ${name} 令牌进行认证`)
        break
      }
    }

    if (!token) {
      console.error('未找到有效的认证令牌')
      return NextResponse.json({
        success: false,
        message: '未授权访问，请确保您已登录并具有管理员权限'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      // 先尝试使用JWT验证
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        adminId = payload.sub || payload.userId
        console.log('使用JWT验证成功，用户ID:', adminId)
      } catch (jwtError) {
        console.log('JWT验证失败，尝试使用NextAuth会话')
        
        // 如果JWT验证失败，尝试使用NextAuth会话
        const { getServerSession } = await import("next-auth/next")
        const { authOptions } = await import("@/lib/auth")
        const session = await getServerSession(authOptions)
        
        if (!session?.user?.id) {
          throw new Error('无效的NextAuth会话')
        }
        
        adminId = session.user.id
        console.log('使用NextAuth会话验证成功，用户ID:', adminId)
      }
    } catch (error) {
      console.error('认证失败:', error)
      return NextResponse.json({
        success: false,
        message: '无效的认证信息，请重新登录'
      }, { status: 401 })
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: adminId as string },
      include: { role: true }
    })

    if (!admin) {
      console.error('未找到管理员用户:', adminId)
      return NextResponse.json({
        success: false,
        message: '用户信息不存在，请重新登录'
      }, { status: 401 })
    }
    
    if (admin.role?.code !== 'super' && admin.role?.code !== 'admin') {
      console.error('用户没有管理员权限:', admin.username, admin.role?.code)
      return NextResponse.json({
        success: false,
        message: '没有权限执行此操作，需要管理员权限'
      }, { status: 403 })
    }

    // 获取用户认证资料
    const verification = await prisma.userVerification.findUnique({
      where: { userId }
    })

    if (!verification) {
      console.log(`用户 ${userId} 没有提交认证资料`)
      return NextResponse.json({
        success: false,
        message: '未找到认证资料，用户可能尚未提交',
        data: null
      }, { status: 404 })
    }
    
    console.log(`成功获取用户 ${userId} 的认证资料，类型: ${verification.type}, 状态: ${verification.status}`)

    // 返回认证资料
    return NextResponse.json({
      success: true,
      message: '获取认证资料成功',
      data: {
        status: verification.status,
        type: verification.type,
        reviewedAt: verification.reviewedAt,
        remark: verification.remark,
        data: {
          // 个人认证信息
          realName: verification.realName,
          idCardNumber: verification.idCardNumber,
          idCardFront: verification.idCardFront,
          idCardBack: verification.idCardBack,
          idCardHolding: verification.idCardHolding,

          // 企业认证信息
          companyName: verification.companyName,
          legalPerson: verification.legalPerson,
          legalPersonIdCard: verification.legalPersonIdCard,
          socialCreditCode: verification.socialCreditCode,
          businessLicense: verification.businessLicense,
          otherDocuments: verification.otherDocuments,
        }
      }
    })
  } catch (error) {
    console.error("获取用户认证资料错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户认证资料失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
