import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth'
import { hasPermission } from '@/lib/permission'

/**
 * 管理员更改用户认证类型
 * @route POST /api/admin/user/verification
 */
export async function POST(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: decoded.userId as string },
      include: { role: true }
    })

    if (!admin) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查管理员权限
    const hasAccess = admin.role?.code === 'super' || admin.role?.code === 'admin'
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有权限执行此操作', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取请求数据
    const data = await req.json()
    const { userId, newType, reason } = data

    if (!userId || !newType || !reason) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数', error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        verification: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查新类型是否与当前类型相同
    if (user.verificationType === newType) {
      return NextResponse.json(
        { success: false, message: '新认证类型与当前类型相同', error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    // 计算截止日期（3天后）
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + 3)

    // 记录原始认证状态和类型
    const originalType = user.verificationType
    const originalStatus = user.verificationStatus

    // 更新用户认证类型
    await prisma.user.update({
      where: { id: userId },
      data: {
        verificationType: newType,
        verificationStatus: 'pending'
      }
    })

    // 创建或更新认证类型变更记录
    await prisma.verificationTypeChange.upsert({
      where: { userId },
      update: {
        originalType,
        originalStatus,
        newType,
        reason,
        adminId: admin.id,
        deadline,
        completed: false
      },
      create: {
        userId,
        originalType,
        originalStatus,
        newType,
        reason,
        adminId: admin.id,
        deadline,
        completed: false
      }
    })

    // 如果用户有现有的认证资料，则删除
    if (user.verification) {
      await prisma.userVerification.delete({
        where: { userId }
      })
    }

    // 创建系统通知
    try {
      await prisma.notification.create({
        data: {
          userId,
          title: '认证类型变更通知',
          content: `您的认证类型已被管理员变更为${newType === 'personal' ? '个人认证' : '企业认证'}，请在${deadline.toLocaleDateString()}前完成新的认证资料提交。`,
          type: 'verification',
          data: {
            action: 'type_change',
            originalType,
            newType,
            deadline: deadline.toISOString(),
            reason
          },
          isRead: false
        }
      });

      // 如果用户有邮箱，发送邮件通知
      if (user.email) {
        try {
          await prisma.emailQueue.create({
            data: {
              to: user.email,
              subject: '认证类型变更通知',
              text: `尊敬的${user.name || user.username}，您的认证类型已被管理员变更为${newType === 'personal' ? '个人认证' : '企业认证'}，请在${deadline.toLocaleDateString()}前完成新的认证资料提交。变更原因：${reason}`,
              html: `
                <div style="font-family: Arial, sans-serif; padding: 20px; color: #333;">
                  <h2 style="color: #4CAF50;">认证类型变更通知</h2>
                  <p>尊敬的 ${user.name || user.username}，</p>
                  <p>您的认证类型已被管理员变更为<strong>${newType === 'personal' ? '个人认证' : '企业认证'}</strong>。</p>
                  <p>请在 <strong>${deadline.toLocaleDateString()}</strong> 前完成新的认证资料提交。</p>
                  <p>变更原因：${reason}</p>
                  <p>如有任何疑问，请联系客服。</p>
                  <p style="margin-top: 30px; font-size: 12px; color: #999;">
                    此邮件由系统自动发送，请勿回复。
                  </p>
                </div>
              `,
              status: 'pending',
              priority: 'high'
            }
          });
        } catch (emailError) {
          logger.error('发送认证类型变更邮件失败:', emailError);
          // 邮件发送失败不影响主流程
        }
      }
    } catch (notificationError) {
      logger.error('创建认证类型变更通知失败:', notificationError);
      // 通知创建失败不影响主流程
    }

    return NextResponse.json(
      {
        success: true,
        message: '认证类型变更成功',
        data: {
          userId,
          newType,
          deadline
        }
      },
      { status: 200, headers }
    )
  } catch (error: any) {
    console.error('管理员更改用户认证类型错误:', error)
    return NextResponse.json(
      { success: false, message: '服务器错误', error: error.message },
      { status: 500, headers }
    )
  }
}

/**
 * 获取认证类型变更记录
 * @route GET /api/admin/user/verification?userId=xxx
 */
export async function GET(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: decoded.userId as string },
      include: { role: true }
    })

    if (!admin) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查管理员权限
    const hasAccess = admin.role?.code === 'super' || admin.role?.code === 'admin'
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有权限执行此操作', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取查询参数
    const url = new URL(req.url)
    const userId = url.searchParams.get('userId')

    // 如果提供了userId，则获取特定用户的记录
    if (userId) {
      const change = await prisma.verificationTypeChange.findUnique({
        where: { userId },
        include: {
          admin: {
            select: {
              id: true,
              username: true,
              name: true
            }
          },
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              verificationType: true,
              verificationStatus: true
            }
          }
        }
      })

      if (!change) {
        return NextResponse.json(
          { success: false, message: '未找到认证类型变更记录', error: 'NotFound' },
          { status: 404, headers }
        )
      }

      return NextResponse.json(
        { success: true, data: change },
        { status: 200, headers }
      )
    }

    // 否则获取所有记录
    const changes = await prisma.verificationTypeChange.findMany({
      include: {
        admin: {
          select: {
            id: true,
            username: true,
            name: true
          }
        },
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            verificationType: true,
            verificationStatus: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(
      { success: true, data: changes },
      { status: 200, headers }
    )
  } catch (error: any) {
    console.error('获取认证类型变更记录错误:', error)
    return NextResponse.json(
      { success: false, message: '服务器错误', error: error.message },
      { status: 500, headers }
    )
  }
}
