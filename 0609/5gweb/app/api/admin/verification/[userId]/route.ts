import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取用户认证资料
 *
 * @route GET /api/admin/verification/[userId]
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId
    logger.error(`[认证资料查看API] 开始处理请求，用户ID: ${userId}`)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "认证资料查看API")
    if (response) {
      return response
    }

    // 获取用户认证资料
    console.log(`[认证资料查看API] 开始查询用户认证资料: ${userId}`)
    let verification;
    try {
      verification = await prisma.userVerification.findUnique({
        where: { userId }
      })

      console.log(`[认证资料查看API] 查询结果:`, verification ? `成功, 类型: ${verification.type}, 状态: ${verification.status}` : '未找到认证资料')

      if (!verification) {
        console.log(`用户 ${userId} 没有提交认证资料`)
        return NextResponse.json({
          success: false,
          message: '未找到认证资料，用户可能尚未提交',
          data: null
        }, { status: 404 })
      }
    } catch (dbError) {
      console.error(`[认证资料查看API] 查询认证资料错误:`, dbError)
      return NextResponse.json({
        success: false,
        message: '查询认证资料时发生错误: ' + (dbError instanceof Error ? dbError.message : '未知错误')
      }, { status: 500 })
    }

    console.log(`[认证资料查看API] 成功获取用户 ${userId} 的认证资料，类型: ${verification.type}, 状态: ${verification.status}`)

    // 准备响应数据
    const responseData = {
      success: true,
      message: '获取认证资料成功',
      data: {
        status: verification.status,
        type: verification.type,
        reviewedAt: verification.reviewedAt,
        remark: verification.remark,
        data: {
          // 个人认证信息
          realName: verification.realName,
          idCardNumber: verification.idCardNumber,
          idCardFront: verification.idCardFront,
          idCardBack: verification.idCardBack,
          idCardHolding: verification.idCardHolding,

          // 企业认证信息
          companyName: verification.companyName,
          legalPerson: verification.legalPerson,
          legalPersonIdCard: verification.legalPersonIdCard,
          socialCreditCode: verification.socialCreditCode,
          businessLicense: verification.businessLicense,
          otherDocuments: verification.otherDocuments,
        }
      }
    };

    console.log(`[认证资料查看API] 返回数据:`, {
      success: responseData.success,
      message: responseData.message,
      dataType: responseData.data.type,
      dataStatus: responseData.data.status
    });
    // 返回认证资料
    return NextResponse.json(responseData)
  } catch (error) {
    console.error("获取用户认证资料错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户认证资料失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
