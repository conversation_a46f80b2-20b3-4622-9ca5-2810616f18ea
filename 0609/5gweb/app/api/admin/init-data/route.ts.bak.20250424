/**
 * 初始化系统数据 API 路由
 * 用于初始化菜单和操作数据
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

/**
 * 初始化系统数据
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    // 检查是否有管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
      select: { roleCode: true }
    })

    if (!user || user.roleCode !== "ADMIN") {
      return NextResponse.json({ error: "无权限" }, { status: 403 })
    }

    // 初始化菜单数据
    const menuCount = await prisma.menu.count()
    let createdMenus = []

    if (menuCount === 0) {
      // 定义基础菜单
      const menus = [
        // 仪表盘
        {
          code: "dashboard",
          name: "仪表盘",
          path: "/dashboard",
          icon: "LayoutDashboard",
          order: 10,
          visible: true
        },

        // 客户管理
        {
          code: "accounts",
          name: "客户管理",
          path: "/accounts",
          icon: "Users",
          order: 20,
          visible: true
        },
        {
          code: "customer",
          name: "客户列表",
          path: "/accounts/customer",
          icon: "User",
          parentId: null, // 创建后会更新为 accounts 的ID
          order: 21,
          visible: true
        },

        // 任务管理
        {
          code: "tasks",
          name: "任务管理",
          path: "/tasks",
          icon: "FileText",
          order: 30,
          visible: true
        },
        {
          code: "tasks_upload",
          name: "任务上传",
          path: "/tasks/upload",
          icon: "Upload",
          parentId: null, // 创建后会更新为 tasks 的ID
          order: 31,
          visible: true
        },
        {
          code: "tasks_details",
          name: "任务详情",
          path: "/tasks/details",
          icon: "FileSearch",
          parentId: null, // 创建后会更新为 tasks 的ID
          order: 32,
          visible: true
        },

        // 视频任务管理
        {
          code: "video_tasks",
          name: "视频任务管理",
          path: "/video-tasks",
          icon: "Video",
          order: 40,
          visible: true
        },

        // 费率管理
        {
          code: "rates",
          name: "费率管理",
          path: "/rates",
          icon: "Percent",
          order: 50,
          visible: true
        },

        // 用户管理
        {
          code: "users",
          name: "用户管理",
          path: "/users",
          icon: "Users",
          order: 60,
          visible: true
        },

        // 系统设置
        {
          code: "settings",
          name: "系统设置",
          path: "/settings",
          icon: "Settings",
          order: 70,
          visible: true
        },
        {
          code: "roles",
          name: "角色管理",
          path: "/settings/roles",
          icon: "Shield",
          parentId: null, // 创建后会更新为 settings 的ID
          order: 71,
          visible: true
        },
        {
          code: "menus",
          name: "菜单管理",
          path: "/settings/menus",
          icon: "Menu",
          parentId: null, // 创建后会更新为 settings 的ID
          order: 72,
          visible: true
        },
        {
          code: "logs",
          name: "系统日志",
          path: "/settings/logs",
          icon: "FileText",
          parentId: null, // 创建后会更新为 settings 的ID
          order: 73,
          visible: true
        },
        {
          code: "system_settings",
          name: "系统参数",
          path: "/settings/system",
          icon: "Sliders",
          parentId: null, // 创建后会更新为 settings 的ID
          order: 74,
          visible: true
        }
      ]

      // 先创建父菜单
      const parentMenus = menus.filter(menu => !menu.parentId);
      const createdParentMenus = await Promise.all(
        parentMenus.map(menu =>
          prisma.menu.create({
            data: menu
          })
        )
      );

      // 创建父菜单映射关系
      const menuCodeToIdMap = createdParentMenus.reduce((map, menu) => {
        map[menu.code] = menu.id;
        return map;
      }, {} as Record<string, string>);

      // 创建子菜单，并设置正确的父菜单ID
      const childMenus = menus.filter(menu => menu.parentId === null &&
        ['customer', 'tasks_upload', 'tasks_details', 'roles', 'menus', 'logs', 'system_settings'].includes(menu.code));

      // 设置正确的父菜单ID
      const childMenusWithParent = childMenus.map(menu => {
        const parentCode = {
          'customer': 'accounts',
          'tasks_upload': 'tasks',
          'tasks_details': 'tasks',
          'roles': 'settings',
          'menus': 'settings',
          'logs': 'settings',
          'system_settings': 'settings'
        }[menu.code];

        return {
          ...menu,
          parentId: menuCodeToIdMap[parentCode]
        };
      });

      // 创建子菜单
      const createdChildMenus = await Promise.all(
        childMenusWithParent.map(menu =>
          prisma.menu.create({
            data: menu
          })
        )
      );

      // 合并所有创建的菜单
      createdMenus = [...createdParentMenus, ...createdChildMenus]
    }

    // 初始化操作数据
    const operationCount = await prisma.operation.count()
    let createdOperations = []

    if (operationCount === 0) {
      // 获取已创建的菜单
      const existingMenus = await prisma.menu.findMany()
      const menuMap = existingMenus.reduce((acc, menu) => {
        acc[menu.code] = menu.id
        return acc
      }, {} as Record<string, string>)

      // 定义基础操作
      const operations = [
        // 仪表盘操作
        {
          code: "dashboard:view",
          name: "查看仪表盘",
          type: "page",
          description: "查看系统仪表盘",
          menuId: menuMap["dashboard"],
          enabled: true
        },

        // 客户管理操作
        {
          code: "accounts:view",
          name: "查看客户管理",
          type: "page",
          description: "查看客户管理页面",
          menuId: menuMap["accounts"],
          enabled: true
        },
        {
          code: "customer:view",
          name: "查看客户列表",
          type: "page",
          description: "查看客户列表和详情",
          menuId: menuMap["customer"],
          enabled: true
        },
        {
          code: "customer:create",
          name: "创建客户",
          type: "action",
          description: "创建新客户",
          menuId: menuMap["customer"],
          enabled: true
        },
        {
          code: "customer:update",
          name: "更新客户",
          type: "action",
          description: "更新客户信息",
          menuId: menuMap["customer"],
          enabled: true
        },
        {
          code: "customer:delete",
          name: "删除客户",
          type: "action",
          description: "删除客户",
          menuId: menuMap["customer"],
          enabled: true
        },
        {
          code: "customer:export",
          name: "导出客户",
          type: "action",
          description: "导出客户数据",
          menuId: menuMap["customer"],
          enabled: true
        },

        // 任务管理操作
        {
          code: "tasks:view",
          name: "查看任务管理",
          type: "page",
          description: "查看任务管理页面",
          menuId: menuMap["tasks"],
          enabled: true
        },
        {
          code: "tasks_upload:view",
          name: "查看任务上传",
          type: "page",
          description: "查看任务上传页面",
          menuId: menuMap["tasks_upload"],
          enabled: true
        },
        {
          code: "tasks_upload:upload",
          name: "上传任务",
          type: "action",
          description: "上传新任务",
          menuId: menuMap["tasks_upload"],
          enabled: true
        },
        {
          code: "tasks_details:view",
          name: "查看任务详情",
          type: "page",
          description: "查看任务详细信息",
          menuId: menuMap["tasks_details"],
          enabled: true
        },
        {
          code: "tasks_details:update",
          name: "更新任务",
          type: "action",
          description: "更新任务信息",
          menuId: menuMap["tasks_details"],
          enabled: true
        },
        {
          code: "tasks_details:delete",
          name: "删除任务",
          type: "action",
          description: "删除任务",
          menuId: menuMap["tasks_details"],
          enabled: true
        },
        {
          code: "tasks_details:export",
          name: "导出任务",
          type: "action",
          description: "导出任务数据",
          menuId: menuMap["tasks_details"],
          enabled: true
        },

        // 视频任务管理操作
        {
          code: "video_tasks:view",
          name: "查看视频任务",
          type: "page",
          description: "查看视频任务列表和详情",
          menuId: menuMap["video_tasks"],
          enabled: true
        },
        {
          code: "video_tasks:create",
          name: "创建视频任务",
          type: "action",
          description: "创建新视频任务",
          menuId: menuMap["video_tasks"],
          enabled: true
        },
        {
          code: "video_tasks:update",
          name: "更新视频任务",
          type: "action",
          description: "更新视频任务信息",
          menuId: menuMap["video_tasks"],
          enabled: true
        },
        {
          code: "video_tasks:delete",
          name: "删除视频任务",
          type: "action",
          description: "删除视频任务",
          menuId: menuMap["video_tasks"],
          enabled: true
        },
        {
          code: "video_tasks:export",
          name: "导出视频任务",
          type: "action",
          description: "导出视频任务数据",
          menuId: menuMap["video_tasks"],
          enabled: true
        },

        // 费率管理操作
        {
          code: "rates:view",
          name: "查看费率",
          type: "page",
          description: "查看费率列表和详情",
          menuId: menuMap["rates"],
          enabled: true
        },
        {
          code: "rates:create",
          name: "创建费率",
          type: "action",
          description: "创建新费率",
          menuId: menuMap["rates"],
          enabled: true
        },
        {
          code: "rates:update",
          name: "更新费率",
          type: "action",
          description: "更新费率信息",
          menuId: menuMap["rates"],
          enabled: true
        },
        {
          code: "rates:delete",
          name: "删除费率",
          type: "action",
          description: "删除费率",
          menuId: menuMap["rates"],
          enabled: true
        },

        // 用户管理操作
        {
          code: "user:view",
          name: "查看用户",
          type: "page",
          description: "查看用户列表和详情",
          menuId: menuMap["users"],
          enabled: true
        },
        {
          code: "user:create",
          name: "创建用户",
          type: "action",
          description: "创建新用户",
          menuId: menuMap["users"],
          enabled: true
        },
        {
          code: "user:update",
          name: "更新用户",
          type: "action",
          description: "更新用户信息",
          menuId: menuMap["users"],
          enabled: true
        },
        {
          code: "user:delete",
          name: "删除用户",
          type: "action",
          description: "删除用户",
          menuId: menuMap["users"],
          enabled: true
        },
        {
          code: "user:export",
          name: "导出用户",
          type: "action",
          description: "导出用户数据",
          menuId: menuMap["users"],
          enabled: true
        },

        // 系统设置操作
        {
          code: "settings:view",
          name: "查看系统设置",
          type: "page",
          description: "查看系统设置页面",
          menuId: menuMap["settings"],
          enabled: true
        },

        // 角色管理操作
        {
          code: "role:view",
          name: "查看角色",
          type: "page",
          description: "查看角色列表和详情",
          menuId: menuMap["roles"],
          enabled: true
        },
        {
          code: "role:create",
          name: "创建角色",
          type: "action",
          description: "创建新角色",
          menuId: menuMap["roles"],
          enabled: true
        },
        {
          code: "role:update",
          name: "更新角色",
          type: "action",
          description: "更新角色信息",
          menuId: menuMap["roles"],
          enabled: true
        },
        {
          code: "role:delete",
          name: "删除角色",
          type: "action",
          description: "删除角色",
          menuId: menuMap["roles"],
          enabled: true
        },

        // 菜单管理操作
        {
          code: "menu:view",
          name: "查看菜单",
          type: "page",
          description: "查看菜单列表和详情",
          menuId: menuMap["menus"],
          enabled: true
        },
        {
          code: "menu:create",
          name: "创建菜单",
          type: "action",
          description: "创建新菜单",
          menuId: menuMap["menus"],
          enabled: true
        },
        {
          code: "menu:update",
          name: "更新菜单",
          type: "action",
          description: "更新菜单信息",
          menuId: menuMap["menus"],
          enabled: true
        },
        {
          code: "menu:delete",
          name: "删除菜单",
          type: "action",
          description: "删除菜单",
          menuId: menuMap["menus"],
          enabled: true
        },

        // 系统日志操作
        {
          code: "logs:view",
          name: "查看日志",
          type: "page",
          description: "查看系统日志",
          menuId: menuMap["logs"],
          enabled: true
        },
        {
          code: "logs:export",
          name: "导出日志",
          type: "action",
          description: "导出系统日志",
          menuId: menuMap["logs"],
          enabled: true
        },

        // 系统参数操作
        {
          code: "system_settings:view",
          name: "查看系统参数",
          type: "page",
          description: "查看系统参数设置",
          menuId: menuMap["system_settings"],
          enabled: true
        },
        {
          code: "system_settings:update",
          name: "更新系统参数",
          type: "action",
          description: "更新系统参数设置",
          menuId: menuMap["system_settings"],
          enabled: true
        }
      ]

      // 创建操作
      createdOperations = await Promise.all(
        operations.map(operation =>
          prisma.operation.create({
            data: operation
          })
        )
      )
    }

    return NextResponse.json({
      success: true,
      message: "初始化数据成功",
      data: {
        menus: {
          created: createdMenus.length,
          total: await prisma.menu.count()
        },
        operations: {
          created: createdOperations.length,
          total: await prisma.operation.count()
        }
      }
    })
  } catch (error) {
    console.error("初始化数据错误:", error)
    return NextResponse.json(
      { success: false, message: "初始化数据失败", error: String(error) },
      { status: 500 }
    )
  }
}
