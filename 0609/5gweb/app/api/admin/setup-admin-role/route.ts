/**
 * 设置管理员角色为超级管理员
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user } = await AuthMiddleware.requireAdmin(request, "管理员角色设置API");
    if (response) {
      return response;
    }

    // 1. 获取管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: "ADMIN" }
    })

    if (!adminRole) {
      return NextResponse.json(
        { success: false, error: "管理员角色不存在" },
        { status: 404 }
      )
    }

    // 2. 更新管理员角色为超级管理员
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        permissions: ["*"], // 通配符表示所有权限
        type: "system",     // 系统角色
        description: "系统超级管理员，拥有所有权限"
      }
    })

    // 3. 获取所有菜单
    const allMenus = await prisma.menu.findMany({
      where: { visible: true }
    })

    // 4. 获取所有操作
    const allOperations = await prisma.operation.findMany({
      where: { enabled: true }
    })

    // 5. 清除现有的角色-菜单关联
    await prisma.$executeRaw`
      DELETE FROM "_RoleMenus"
      WHERE "B" = ${adminRole.id}
    `

    // 6. 创建新的角色-菜单关联
    for (const menu of allMenus) {
      await prisma.$executeRaw`
        INSERT INTO "_RoleMenus" ("A", "B")
        VALUES (${menu.id}, ${adminRole.id})
        ON CONFLICT ("A", "B") DO NOTHING
      `
    }

    // 7. 清除现有的角色-操作关联
    await prisma.$executeRaw`
      DELETE FROM "_OperationToRole"
      WHERE "B" = ${adminRole.id}
    `

    // 8. 创建新的角色-操作关联
    for (const operation of allOperations) {
      await prisma.$executeRaw`
        INSERT INTO "_OperationToRole" ("A", "B")
        VALUES (${operation.id}, ${adminRole.id})
        ON CONFLICT ("A", "B") DO NOTHING
      `
    }

    // 9. 设置角色不可删除标志
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        type: "system", // 系统角色不可删除
        notificationEnabled: true
      }
    })

    return NextResponse.json({
      success: true,
      message: "管理员角色已设置为超级管理员",
      data: {
        role: adminRole.id,
        menusCount: allMenus.length,
        operationsCount: allOperations.length
      }
    })
  } catch (error) {
    console.error("设置管理员角色为超级管理员失败:", error)
    return NextResponse.json(
      { success: false, error: "设置管理员角色为超级管理员失败" },
      { status: 500 }
    )
  }
}
