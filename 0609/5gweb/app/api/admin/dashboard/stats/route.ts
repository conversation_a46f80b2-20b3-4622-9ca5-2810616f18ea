import logger from '@/lib/utils/logger';

/**
 * 管理员仪表盘统计API
 * 提供所有用户任务、通话记录和费用的统计信息
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取管理员仪表盘统计数据
 *
 * @route GET /api/admin/dashboard/stats
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 使用权限检查中间件检查管理员权限
    const { response, user } = await AuthMiddleware.requireAdmin(request, "管理员仪表盘统计API");
    if (response) {
      return response;
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const period = searchParams.get('period') || 'week' // day, week, month, year

    // 计算时间范围
    const endDate = new Date()
    let startDate = new Date()

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1)
        break
      case 'week':
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
    }

    // 准备默认的空数据结构
    let taskStats = [];
    let callStats = [];
    let costStats = { _sum: { amount: 0 } };
    let taskTrend = [];
    let connectionRateTrend = [];
    let intentionDistribution = [];
    let userStats = [];

    try {
      // 查询所有任务统计
      // 检查 prisma.task 是否存在
      const hasPrismaTask = typeof prisma === 'object' && prisma !== null && 'task' in prisma;

      if (hasPrismaTask) {
        taskStats = await prisma.task.groupBy({
          by: ['status'],
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          },
          _count: {
            id: true
          }
        });
      } else {
        logger.log('prisma.task 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取任务统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询所有通话记录统计
      // 检查 prisma.callRecord 是否存在
      const hasPrismaCallRecord = typeof prisma === 'object' && prisma !== null && 'callRecord' in prisma;

      if (hasPrismaCallRecord) {
        callStats = await prisma.callRecord.groupBy({
          by: ['connectionType'],
          where: {
            startTime: {
              gte: startDate,
              lte: endDate
            }
          },
          _count: {
            id: true
          },
          _sum: {
            duration: true
          }
        });
      } else {
        logger.log('prisma.callRecord 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取通话记录统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询所有费用统计
      // 检查 prisma.balanceTransaction 是否存在
      const hasPrismaBalanceTransaction = typeof prisma === 'object' && prisma !== null && 'balanceTransaction' in prisma;

      if (hasPrismaBalanceTransaction) {
        costStats = await prisma.balanceTransaction.aggregate({
          where: {
            type: 'deduct',
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          },
          _sum: {
            amount: true
          }
        });
      } else {
        logger.log('prisma.balanceTransaction 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取费用统计失败，可能是模型不存在:', error);
    }

    try {
      // 查询任务趋势数据（按天统计）
      // 检查Task表是否存在
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'Task'
        );
      `;

      if (tableExists[0].exists) {
        taskTrend = await prisma.$queryRaw`
          SELECT
            DATE(createdAt) as date,
            COUNT(*) as count
          FROM Task
          WHERE createdAt >= ${startDate}
            AND createdAt <= ${endDate}
          GROUP BY DATE(createdAt)
          ORDER BY date ASC
        `;
      }
    } catch (error) {
      logger.log('获取任务趋势数据失败，可能是表不存在:', error);
    }

    try {
      // 查询接通率趋势数据（按天统计）
      // 检查CallRecord表是否存在
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'CallRecord'
        );
      `;

      if (tableExists[0].exists) {
        connectionRateTrend = await prisma.$queryRaw`
          SELECT
            DATE(startTime) as date,
            COUNT(CASE WHEN isConnected = true THEN 1 END) as connectedCount,
            COUNT(*) as totalCount
          FROM CallRecord
          WHERE startTime >= ${startDate}
            AND startTime <= ${endDate}
          GROUP BY DATE(startTime)
          ORDER BY date ASC
        `;
      }
    } catch (error) {
      logger.log('获取接通率趋势数据失败，可能是表不存在:', error);
    }

    try {
      // 查询意图分布
      // 检查 prisma.callRecord 是否存在
      const hasPrismaCallRecord = typeof prisma === 'object' && prisma !== null && 'callRecord' in prisma;

      if (hasPrismaCallRecord) {
        intentionDistribution = await prisma.callRecord.groupBy({
          by: ['intention'],
          where: {
            startTime: {
              gte: startDate,
              lte: endDate
            },
            intention: {
              not: null
            }
          },
          _count: {
            id: true
          }
        });
      } else {
        logger.log('prisma.callRecord 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取意图分布失败，可能是模型不存在:', error);
    }

    try {
      // 查询用户统计 - 优化查询性能
      // 检查 prisma.user 是否存在
      const hasPrismaUser = typeof prisma === 'object' && prisma !== null && 'user' in prisma;

      if (hasPrismaUser) {
        // 获取所有非管理员用户
        const users = await prisma.user.findMany({
          where: {
            roleCode: {
              not: 'ADMIN'
            }
          },
          select: {
            id: true,
            username: true,
            name: true,
            balance: true,
            creditLimit: true
          }
        });

        // 获取所有用户ID
        const userIds = users.map(user => user.id);

        // 批量查询所有用户的任务数
        const taskCounts = await prisma.task.groupBy({
          by: ['userId'],
          where: {
            userId: {
              in: userIds
            }
          },
          _count: {
            id: true
          }
        });

        // 批量查询所有用户的通话记录数
        const callCounts = await prisma.callRecord.groupBy({
          by: ['userId', 'isConnected'],
          where: {
            userId: {
              in: userIds
            }
          },
          _count: {
            id: true
          }
        });

        // 批量查询所有用户的通话时长
        const callDurations = await prisma.callRecord.groupBy({
          by: ['userId'],
          where: {
            userId: {
              in: userIds
            }
          },
          _sum: {
            duration: true
          }
        });

        // 批量查询所有用户的费用总额
        const costAmounts = await prisma.balanceTransaction.groupBy({
          by: ['userId'],
          where: {
            userId: {
              in: userIds
            },
            type: 'deduct'
          },
          _sum: {
            amount: true
          }
        });

        // 处理查询结果，构建用户统计数据
        for (const user of users) {
          // 查找用户的任务数
          const taskData = taskCounts.find(tc => tc.userId === user.id);
          const taskCount = taskData ? taskData._count.id : 0;

          // 查找用户的通话记录数
          const allCallData = callCounts.filter(cc => cc.userId === user.id);
          const callCount = allCallData.reduce((sum, cc) => sum + cc._count.id, 0);

          // 查找用户的接通通话记录数
          const connectedCallData = allCallData.find(cc => cc.userId === user.id && cc.isConnected === true);
          const connectedCallCount = connectedCallData ? connectedCallData._count.id : 0;

          // 查找用户的通话时长
          const durationData = callDurations.find(cd => cd.userId === user.id);
          const callDuration = durationData && durationData._sum.duration ? durationData._sum.duration : 0;

          // 查找用户的费用总额
          const costData = costAmounts.find(ca => ca.userId === user.id);
          const costAmount = costData && costData._sum.amount ? Math.abs(costData._sum.amount) : 0;

          // 添加到用户统计
          userStats.push({
            id: user.id,
            username: user.username,
            name: user.name || user.username,
            balance: user.balance || 0,
            creditLimit: user.creditLimit || 0,
            taskCount,
            callCount,
            connectedCallCount,
            connectionRate: callCount > 0 ? (connectedCallCount / callCount) : 0,
            callDuration,
            costAmount
          });
        }
      } else {
        logger.log('prisma.user 不存在，使用空数据');
      }
    } catch (error) {
      logger.log('获取用户统计失败，可能是模型不存在:', error);
    }

    // 计算总任务数
    const totalTasks = Array.isArray(taskStats) ? taskStats.reduce((sum, stat) => sum + (stat._count?.id || 0), 0) : 0

    // 计算总通话数
    const totalCalls = Array.isArray(callStats) ? callStats.reduce((sum, stat) => sum + (stat._count?.id || 0), 0) : 0

    // 计算总通话时长（秒）
    const totalDuration = Array.isArray(callStats) ? callStats.reduce((sum, stat) => sum + (stat._sum?.duration || 0), 0) : 0

    // 计算总费用
    const totalCost = Math.abs(costStats?._sum?.amount || 0)

    // 计算接通率
    const connectedCalls = Array.isArray(callStats) ? callStats.find(stat => stat.connectionType === 'VIDEO' || stat.connectionType === 'AUDIO') : null
    const connectionRate = totalCalls > 0 ? ((connectedCalls?._count?.id || 0) / totalCalls) : 0

    // 准备月度数据（如果没有数据，生成空数据）
    let monthlyData = [];
    if (!taskTrend || taskTrend.length === 0) {
      // 生成最近6个月的空数据
      for (let i = 5; i >= 0; i--) {
        const month = new Date();
        month.setMonth(month.getMonth() - i);
        const monthName = month.toLocaleString('zh-CN', { month: 'short' });
        monthlyData.push({
          name: monthName,
          total: 0,
          completed: 0,
          pending: 0
        });
      }
    } else {
      // 将任务趋势数据转换为月度数据
      const monthMap = new Map();
      taskTrend.forEach(item => {
        const date = new Date(item.date);
        const monthName = date.toLocaleString('zh-CN', { month: 'short' });

        if (!monthMap.has(monthName)) {
          monthMap.set(monthName, {
            name: monthName,
            total: 0,
            completed: 0,
            pending: 0
          });
        }

        const monthData = monthMap.get(monthName);
        monthData.total += parseInt(item.count);
      });

      monthlyData = Array.from(monthMap.values());
    }

    // 准备最近活动数据
    let recentActivities = [];
    try {
      // 查询最近的任务
      const recentTasks = await prisma.task.findMany({
        take: 10,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true
            }
          }
        }
      });

      // 转换为活动数据
      recentActivities = recentTasks.map(task => ({
        id: task.id,
        type: 'task',
        title: task.name,
        status: task.status,
        user: {
          id: task.user?.id || '',
          name: task.user?.name || task.user?.username || '未知用户'
        },
        timestamp: task.createdAt
      }));
    } catch (error) {
      logger.log('获取最近活动失败:', error);
    }

    // 获取所有用户余额总和
    let totalBalance = 0;
    let totalCreditLimit = 0;
    try {
      // 查询所有用户余额总和
      const balanceSum = await prisma.user.aggregate({
        _sum: {
          balance: true,
          creditLimit: true
        }
      });

      totalBalance = balanceSum._sum?.balance || 0;
      totalCreditLimit = balanceSum._sum?.creditLimit || 0;
    } catch (error) {
      logger.log('获取用户余额总和失败:', error);
    }

    return NextResponse.json(
      {
        success: true,
        message: '获取管理员仪表盘统计成功',
        data: {
          summary: {
            totalBalance,
            totalCreditLimit,
            totalTasks,
            totalCalls,
            totalDuration,
            totalCost,
            connectionRate,
            userCount: userStats.length
          },
          monthlyData,
          recentActivities,
          taskStats,
          callStats,
          costStats: {
            totalCost
          },
          trends: {
            taskTrend,
            connectionRateTrend
          },
          distributions: {
            intentionDistribution
          },
          userStats
        }
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("获取管理员仪表盘统计错误:", error)
    return NextResponse.json(
      {
        success: false,
        message: '获取管理员仪表盘统计失败: ' + (error instanceof Error ? error.message : '未知错误')
      },
      { status: 500 }
    )
  }
}
