import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 修复菜单路径并重新同步权限
 */
export async function GET(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[修复菜单API:${requestId}] 开始修复菜单路径`)

  try {
    // 更新菜单路径
    const menuUpdates = [
      { code: 'roles', path: '/settings/roles' },
      { code: 'users', path: '/system/admin-users' },
      { code: 'dashboard', path: '/dashboard' },
      { code: 'tasks', path: '/tasks' },
      { code: 'tasks_upload', path: '/tasks/upload' },
      { code: 'tasks_details', path: '/tasks/details' },
      { code: 'settings', path: '/settings' },
      { code: 'system', path: '/system' }
    ]

    const updateResults = []
    for (const update of menuUpdates) {
      const result = await prisma.menu.updateMany({
        where: { code: update.code },
        data: { path: update.path }
      })
      updateResults.push({ code: update.code, path: update.path, updated: result.count })
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany()

    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    if (adminRole) {
      // 关联所有菜单到管理员角色
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          menus: {
            connect: allMenus.map(menu => ({ id: menu.id }))
          }
        }
      })

      // 为每个菜单添加jCasbin权限
      for (const menu of allMenus) {
        await CasbinService.addPermissionForRole('ADMIN', `menu:${menu.code}`, 'view')
      }

      // 添加管理员的通配符权限
      await CasbinService.addPermissionForRole('ADMIN', '*', '*')
    }

    // 同步所有角色的权限
    const roles = await prisma.role.findMany({
      include: {
        menus: true,
        operations: true
      }
    })

    for (const role of roles) {
      // 获取角色当前的所有权限
      const currentPermissions = await CasbinService.getRolePermissions(role.code)
      
      // 删除所有菜单和操作权限
      for (const permission of currentPermissions) {
        if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
          await CasbinService.removePermissionForRole(role.code, permission[1], permission[2])
        }
      }

      // 添加菜单权限
      for (const menu of role.menus) {
        await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
      }

      // 添加操作权限
      for (const operation of role.operations) {
        await CasbinService.addPermissionForRole(role.code, `operation:${operation.code}`, 'execute')
      }
    }

    // 创建默认菜单（如果不存在）
    const defaultMenus = [
      {
        name: "仪表盘",
        path: "/dashboard",
        icon: "LayoutDashboard",
        code: "dashboard",
        visible: true,
        order: 1
      },
      {
        name: "任务管理",
        path: "/tasks",
        icon: "FileText",
        code: "tasks",
        visible: true,
        order: 2
      },
      {
        name: "任务上传",
        path: "/tasks/upload",
        icon: "Upload",
        code: "tasks_upload",
        visible: true,
        order: 3,
        parentCode: "tasks"
      },
      {
        name: "任务详情",
        path: "/tasks/details",
        icon: "FileSearch",
        code: "tasks_details",
        visible: true,
        order: 4,
        parentCode: "tasks"
      },
      {
        name: "系统管理",
        path: "/system",
        icon: "Settings",
        code: "system",
        visible: true,
        order: 5
      },
      {
        name: "用户管理",
        path: "/system/admin-users",
        icon: "Users",
        code: "users",
        visible: true,
        order: 6,
        parentCode: "system"
      },
      {
        name: "系统设置",
        path: "/settings",
        icon: "Settings",
        code: "settings",
        visible: true,
        order: 7
      },
      {
        name: "角色管理",
        path: "/settings/roles",
        icon: "Shield",
        code: "roles",
        visible: true,
        order: 8,
        parentCode: "settings"
      }
    ]

    // 创建或更新默认菜单
    const menuResults = []
    for (const menuData of defaultMenus) {
      const { parentCode, ...menuInfo } = menuData
      
      // 查找父菜单
      let parentId = null
      if (parentCode) {
        const parentMenu = await prisma.menu.findFirst({
          where: { code: parentCode }
        })
        if (parentMenu) {
          parentId = parentMenu.id
        }
      }
      
      // 检查菜单是否已存在
      const existingMenu = await prisma.menu.findFirst({
        where: { code: menuInfo.code }
      })
      
      if (existingMenu) {
        // 更新现有菜单
        const updatedMenu = await prisma.menu.update({
          where: { id: existingMenu.id },
          data: {
            ...menuInfo,
            parentId
          }
        })
        menuResults.push({ action: 'updated', menu: updatedMenu })
      } else {
        // 创建新菜单
        const newMenu = await prisma.menu.create({
          data: {
            ...menuInfo,
            parentId
          }
        })
        menuResults.push({ action: 'created', menu: newMenu })
        
        // 关联到管理员角色
        if (adminRole) {
          await prisma.role.update({
            where: { id: adminRole.id },
            data: {
              menus: {
                connect: { id: newMenu.id }
              }
            }
          })
          
          // 添加jCasbin权限
          await CasbinService.addPermissionForRole('ADMIN', `menu:${newMenu.code}`, 'view')
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: "菜单路径修复成功",
      data: {
        menuUpdates: updateResults,
        menuResults,
        roleCount: roles.length
      },
      requestId
    })
  } catch (error) {
    console.error(`[修复菜单API:${requestId}] 修复菜单路径失败:`, error)
    return NextResponse.json({
      success: false,
      message: "修复菜单路径失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
