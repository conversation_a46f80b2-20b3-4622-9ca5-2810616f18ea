/**
 * 设置管理员角色为超级管理员
 * 这个API端点会将ADMIN角色设置为系统唯一的超级管理员，拥有所有权限
 */

import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST() {
  try {
    console.log("开始设置超级管理员...")
    
    // 1. 获取管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: "ADMIN" }
    })

    if (!adminRole) {
      console.error("管理员角色不存在")
      return NextResponse.json(
        { success: false, error: "管理员角色不存在" },
        { status: 404 }
      )
    }

    console.log("找到管理员角色:", adminRole.id)

    // 2. 更新管理员角色为超级管理员
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        permissions: ["*"], // 通配符表示所有权限
        type: "system",     // 系统角色
        description: "系统超级管理员，拥有所有权限"
      }
    })

    console.log("已更新管理员角色权限")

    // 3. 获取所有菜单
    const allMenus = await prisma.menu.findMany({
      where: { visible: true }
    })

    console.log(`找到 ${allMenus.length} 个菜单`)

    // 4. 获取所有操作
    const allOperations = await prisma.operation.findMany({
      where: { enabled: true }
    })

    console.log(`找到 ${allOperations.length} 个操作`)

    // 5. 清除现有的角色-菜单关联
    await prisma.$executeRaw`
      DELETE FROM "_RoleMenus" 
      WHERE "B" = ${adminRole.id}
    `

    console.log("已清除现有的角色-菜单关联")

    // 6. 创建新的角色-菜单关联
    for (const menu of allMenus) {
      await prisma.$executeRaw`
        INSERT INTO "_RoleMenus" ("A", "B")
        VALUES (${menu.id}, ${adminRole.id})
        ON CONFLICT ("A", "B") DO NOTHING
      `
    }

    console.log("已创建新的角色-菜单关联")

    // 7. 清除现有的角色-操作关联
    try {
      await prisma.$executeRaw`
        DELETE FROM "_OperationToRole" 
        WHERE "B" = ${adminRole.id}
      `
      console.log("已清除现有的角色-操作关联")
    } catch (error) {
      console.log("清除角色-操作关联失败，可能表不存在:", error)
    }

    // 8. 创建新的角色-操作关联
    try {
      for (const operation of allOperations) {
        await prisma.$executeRaw`
          INSERT INTO "_OperationToRole" ("A", "B")
          VALUES (${operation.id}, ${adminRole.id})
          ON CONFLICT ("A", "B") DO NOTHING
        `
      }
      console.log("已创建新的角色-操作关联")
    } catch (error) {
      console.log("创建角色-操作关联失败，可能表不存在:", error)
    }

    // 9. 设置角色不可删除标志
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        type: "system", // 系统角色不可删除
        notificationEnabled: true
      }
    })

    console.log("已设置角色不可删除标志")

    // 10. 更新所有使用ADMIN角色的用户权限
    await prisma.user.updateMany({
      where: { roleCode: "ADMIN" },
      data: {
        permissions: ["*"] // 通配符表示所有权限
      }
    })

    console.log("已更新所有管理员用户权限")

    return NextResponse.json({
      success: true,
      message: "管理员角色已设置为超级管理员",
      data: {
        role: adminRole.id,
        menusCount: allMenus.length,
        operationsCount: allOperations.length
      }
    })
  } catch (error) {
    console.error("设置管理员角色为超级管理员失败:", error)
    return NextResponse.json(
      { success: false, error: "设置管理员角色为超级管理员失败" },
      { status: 500 }
    )
  }
}
