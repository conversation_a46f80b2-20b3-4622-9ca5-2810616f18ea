import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import logger from "@/lib/utils/logger"
import { getTaskGroups } from "@/lib/api/video-call-service"
import { db } from "@/lib/db"

/**
 * 获取远程任务数据
 * 从远程API获取任务数据
 */
export async function GET(request: Request) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取远程任务API");
    if (response) {
      logger.error("用户未登录或会话已过期");
      return response;
    }

    logger.log("开始获取远程任务数据...");
    logger.log("当前用户:", user.id, user.username, user.roleCode);

    try {
      // 获取远程任务数据
      logger.log("调用 getTaskGroups 函数获取远程任务数据...");
      const taskGroups = await getTaskGroups();

      logger.log("getTaskGroups 返回结果:", JSON.stringify(taskGroups));

      if (taskGroups.success && Array.isArray(taskGroups.data)) {
        logger.log(`成功获取 ${taskGroups.data.length} 条远程任务数据`);

        // 转换任务数据格式，使其与本地任务数据格式一致
        const tasks = taskGroups.data.map(task => ({
          id: task.id,
          name: task.name,
          type: "5G视频通知", // 默认类型
          content: task.name,
          status: mapTaskStatus(task.status),
          progress: task.status === "finished" ? 100 : 0,
          importTime: task.createdAt,
          startTime: task.createdAt,
          completionTime: task.status === "finished" ? task.createdAt : null,
          creator: task.creator || "系统导入",
          userId: user.id,
          externalId: task.id,
          createdAt: task.createdAt,
          updatedAt: task.createdAt,
        }));

        // 将远程任务数据保存到数据库中
        try {
          logger.log("尝试将远程任务数据保存到数据库...");

          // 对于每个任务，检查是否已存在，如果不存在则创建
          for (const task of tasks) {
            const existingTask = await db.task.findFirst({
              where: {
                externalId: task.externalId
              }
            });

            if (!existingTask) {
              await db.task.create({
                data: {
                  name: task.name,
                  type: task.type,
                  content: task.content,
                  status: task.status,
                  progress: task.progress,
                  startTime: new Date(task.startTime),
                  completionTime: task.completionTime ? new Date(task.completionTime) : null,
                  creator: task.creator,
                  userId: task.userId,
                  externalId: task.externalId,
                  importTime: new Date(task.importTime),
                }
              });
            }
          }

          logger.log("远程任务数据已保存到数据库");
        } catch (dbError) {
          logger.error("保存远程任务数据到数据库失败:", dbError);
        }

        return NextResponse.json({
          success: true,
          message: "获取远程任务数据成功",
          data: {
            list: tasks,
            total: tasks.length,
            page: 1,
            pageSize: tasks.length,
            totalPages: 1
          }
        });
      } else {
        logger.error("获取远程任务数据失败:", taskGroups.message);

        // 如果远程API调用失败，尝试从数据库获取任务数据
        const dbTasks = await db.task.findMany({
          where: {
            userId: user.id
          },
          orderBy: {
            createdAt: "desc"
          }
        });

        if (dbTasks.length > 0) {
          logger.log(`从数据库获取到 ${dbTasks.length} 条任务数据`);

          return NextResponse.json({
            success: true,
            message: "从数据库获取任务数据成功",
            data: {
              list: dbTasks,
              total: dbTasks.length,
              page: 1,
              pageSize: dbTasks.length,
              totalPages: 1
            }
          });
        }

        return NextResponse.json({
          success: false,
          message: taskGroups.message || "获取远程任务数据失败",
          data: {
            list: [],
            total: 0,
            page: 1,
            pageSize: 10,
            totalPages: 0
          }
        });
      }
    } catch (apiError) {
      logger.error("调用远程API错误:", apiError);

      // 如果远程API调用失败，尝试从数据库获取任务数据
      const dbTasks = await db.task.findMany({
        where: {
          userId: user.id
        },
        orderBy: {
          createdAt: "desc"
        }
      });

      if (dbTasks.length > 0) {
        logger.log(`从数据库获取到 ${dbTasks.length} 条任务数据`);

        return NextResponse.json({
          success: true,
          message: "从数据库获取任务数据成功",
          data: {
            list: dbTasks,
            total: dbTasks.length,
            page: 1,
            pageSize: dbTasks.length,
            totalPages: 1
          }
        });
      }

      return NextResponse.json({
        success: false,
        message: "获取远程任务数据失败，请稍后重试",
        data: {
          list: [],
          total: 0,
          page: 1,
          pageSize: 10,
          totalPages: 0
        }
      });
    }
  } catch (error) {
    logger.error("获取远程任务数据错误:", error);
    return NextResponse.json({
      success: false,
      message: "获取远程任务数据失败，请稍后重试",
      data: {
        list: [],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0
      }
    }, { status: 500 });
  }
}

/**
 * 将API任务状态映射到项目状态
 * @param apiStatus API返回的任务状态
 * @returns 项目中使用的状态
 */
function mapTaskStatus(apiStatus: string): string {
  switch (apiStatus) {
    case "initialized":
      return "未开始";
    case "started":
      return "外呼中";
    case "paused":
      return "已暂停";
    case "finished":
      return "已完成";
    case "stopped":
      return "已停止";
    default:
      return "未知";
  }
}
