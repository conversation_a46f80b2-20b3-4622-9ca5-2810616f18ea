import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { emailService } from "@/lib/services/email"
import { generateVerificationCode, storeVerificationCode } from "@/lib/services/verification"

const CODE_EXPIRY = 5 * 60 // 5分钟

/**
 * 发送验证码响应接口
 */
interface SendVerificationResponse {
  code: number
  success: boolean
  message: string
}

/**
 * 验证码请求验证 schema
 */
const sendVerificationSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
})

/**
 * 处理发送验证码请求
 * POST /api/auth/send-verification-code
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 发送验证码响应
 *
 * 处理流程：
 * 1. 验证请求参数
 * 2. 检查邮箱是否已被注册
 * 3. 生成验证码
 * 4. 存储验证码（Redis，有效期5分钟）
 * 5. 发送验证码邮件
 *
 * 错误处理：
 * - 400: 参数验证失败、邮箱已被注册
 * - 500: 服务器内部错误
 *
 * @example
 * ```typescript
 * // 请求体示例
 * {
 *   "email": "<EMAIL>"
 * }
 *
 * // 成功响应示例
 * {
 *   "code": 200,
 *   "success": true,
 *   "message": "验证码已发送，请查收邮件"
 * }
 * ```
 */
export async function POST(request: NextRequest): Promise<NextResponse<SendVerificationResponse>> {
  try {
    console.log('收到发送验证码请求')
    const body = await request.json()
    console.log('请求体:', body)

    // 验证请求参数
    const result = sendVerificationSchema.safeParse(body)
    if (!result.success) {
      console.log('参数验证失败:', result.error.errors)
      return NextResponse.json({
        code: 400,
        success: false,
        message: result.error.errors[0].message
      }, { status: 400 })
    }

    const { email } = result.data
    console.log('参数验证通过:', { email })

    // 检查邮箱是否已被注册
    console.log('检查邮箱是否已被注册...')
    try {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      })

      if (existingUser) {
        console.log('邮箱已被注册:', existingUser)
        return NextResponse.json({
          code: 400,
          success: false,
          message: '邮箱已被注册'
        }, { status: 400 })
      }
    } catch (dbError) {
      console.error('检查邮箱失败:', dbError)
      // 在开发环境中，如果数据库查询失败，允许继续
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json({
          code: 500,
          success: false,
          message: '服务器错误，请稍后重试'
        }, { status: 500 })
      }
      console.log('开发环境: 忽略数据库错误，继续发送验证码')
    }

    // 生成验证码
    console.log('生成验证码...')
    let code = generateVerificationCode()
    console.log('验证码生成成功:', code)

    // 保存验证码到 Redis
    console.log('保存验证码到 Redis...')
    try {
      await storeVerificationCode(email, code)
      console.log('验证码保存成功')
    } catch (error) {
      console.error('验证码保存失败:', error)
      // 在开发环境中，如果Redis存储失败，允许继续
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json({
          code: 500,
          success: false,
          message: '验证码保存失败，请稍后重试'
        }, { status: 500 })
      }
      console.log('开发环境: 忽略Redis存储错误，继续发送验证码')
      // 在开发环境中，如果Redis存储失败，我们仍然发送验证码邮件
      // 并使用测试验证码123456
      code = '123456';
    }

    // 发送验证码邮件
    console.log('发送验证码邮件...')
    try {
      await emailService.sendVerificationEmail(email, code, 'register')
      console.log('验证码邮件发送成功')
    } catch (error) {
      console.error('验证码邮件发送失败:', error)
      // 在开发环境中，如果邮件发送失败，允许继续
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json({
          code: 500,
          success: false,
          message: '验证码邮件发送失败，请稍后重试'
        }, { status: 500 })
      }
      console.log('开发环境: 忽略邮件发送错误，继续处理')
      // 在开发环境中，即使邮件发送失败，也返回成功
      // 并在控制台显示测试验证码
      console.log('开发环境测试验证码:', code)
    }

    return NextResponse.json({
      code: 200,
      success: true,
      message: '验证码已发送'
    })
  } catch (error) {
    console.error('发送验证码失败:', error)
    return NextResponse.json({
      code: 500,
      success: false,
      message: '发送验证码失败，请稍后重试'
    }, { status: 500 })
  }
}