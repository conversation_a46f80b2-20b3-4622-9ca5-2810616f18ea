import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { v4 as uuidv4 } from 'uuid'

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 获取当前用户信息
 *
 * @route GET /api/auth/me
 * @access 所有已登录用户可访问
 */
export async function GET(req: NextRequest) {
  const requestId = uuidv4().substring(0, 8)
  console.log(`[获取用户API:${requestId}] 获取当前用户信息API被调用`)

  try {
    // 获取cookie中的token
    const cookieStore = cookies()
    // 尝试从不同的cookie名称中获取token
    const tokenCookie = cookieStore.get("token")
    const authTokenCookie = cookieStore.get("auth_token")
    const authBackupCookie = cookieStore.get("auth_backup")

    // 记录可用的cookie
    console.log(`[获取用户API:${requestId}] 可用的cookies:`, cookieStore.getAll().map(c => c.name))

    let token = tokenCookie?.value || authTokenCookie?.value
    let userId = authBackupCookie?.value

    if (!token && !userId) {
      console.log(`[获取用户API:${requestId}] 未找到任何认证cookie`)
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 })
    }

    // 如果有token，尝试验证
    let extractedUserId = null
    if (token) {
      console.log(`[获取用户API:${requestId}] 找到token，长度:`, token.length)
      try {
        console.log(`[获取用户API:${requestId}] 验证token...`)
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        extractedUserId = payload.userId as string || payload.sub as string
        console.log(`[获取用户API:${requestId}] 从令牌中提取的用户ID:`, extractedUserId)
      } catch (tokenError) {
        console.error(`[获取用户API:${requestId}] 令牌验证失败:`, tokenError)
        // 如果没有备用ID，则返回错误
        if (!userId) {
          return NextResponse.json({
            success: false,
            message: '令牌无效，请重新登录',
          }, { status: 401 })
        }
      }
    }

    // 使用提取的用户ID或备用cookie中的用户ID
    const finalUserId = extractedUserId || userId
    console.log(`[获取用户API:${requestId}] 最终使用的用户ID:`, finalUserId)

    if (!finalUserId) {
      throw new Error('无效的用户ID')
    }

    // 从数据库查询用户信息
    const user = await prisma.user.findUnique({
      where: { id: finalUserId },
      include: {
        role: true
      }
    })

    if (!user) {
      console.log(`[获取用户API:${requestId}] 用户不存在: ${finalUserId}`)
      return NextResponse.json({
        success: false,
        message: '用户不存在，请重新登录',
      }, { status: 404 })
    }

    console.log(`[获取用户API:${requestId}] 找到用户: ID=${user.id}, 用户名=${user.username}, 角色=${user.role?.type || 'user'}`)

    // 确保角色信息存在
    if (!user.role) {
      console.log(`[获取用户API:${requestId}] 警告: 用户角色信息不存在，尝试修复`)
      try {
        const role = await prisma.role.findUnique({
          where: { code: user.roleCode },
        })
        if (role) {
          user.role = role
          console.log(`[获取用户API:${requestId}] 成功修复用户角色信息`)
        }
      } catch (roleError) {
        console.error(`[获取用户API:${requestId}] 获取角色信息失败:`, roleError)
      }
    }

    // 返回用户信息，确保使用数据库中的角色信息
    const userData = {
      id: user.id,
      username: user.username,
      role: user.role?.type || 'user',
      roleCode: user.roleCode,
      email: user.email,
      name: user.name
    }

    return NextResponse.json({
      success: true,
      message: '获取用户信息成功',
      data: userData
    })

  } catch (error) {
    console.error(`[获取用户API:${requestId}] 获取用户信息错误:`, error)
    return NextResponse.json({
      success: false,
      message: '获取用户信息失败: ' + (error instanceof Error ? error.message : '未知错误'),
      requestId
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}