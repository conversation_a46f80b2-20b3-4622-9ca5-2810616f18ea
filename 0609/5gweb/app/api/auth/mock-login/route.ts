/**
 * 模拟登录 API 路由
 * 从localStorage中读取用户数据进行验证
 * 
 * 请求格式：
 * ```json
 * {
 *   "identifier": "用户名或邮箱",
 *   "password": "密码"
 * }
 * ```
 * 
 * 响应格式：
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "token": "JWT token",
 *     "user": {
 *       "id": "用户ID",
 *       "email": "邮箱",
 *       "username": "用户名",
 *       "role": "角色"
 *     }
 *   }
 * }
 * ```
 */

import { NextRequest } from "next/server"
import { verifyPassword } from "@/lib/password-utils"
import * as jose from 'jose'
import { cookies } from "next/headers"

// 使用与中间件相同的密钥
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { identifier, password, remember } = body

    console.log("模拟登录请求:", { identifier })

    // 从localStorage中获取用户列表
    // 注意：这里我们不能直接访问localStorage，因为这是服务端代码
    // 客户端需要将用户列表作为请求的一部分发送过来
    // 但为了简化，我们假设用户已经添加到了系统中，并在客户端代码中处理

    // 返回成功响应，让客户端处理实际的验证逻辑
    return new Response(
      JSON.stringify({
        success: true,
        message: "使用模拟登录API，请在客户端验证用户凭据"
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json"
        }
      }
    )
  } catch (error) {
    console.error("模拟登录错误:", error)
    return new Response(
      JSON.stringify({ success: false, error: "登录过程中发生错误" }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json"
        }
      }
    )
  }
}
