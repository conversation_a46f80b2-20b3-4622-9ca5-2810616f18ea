/**
 * 登录 API 路由
 * 处理用户登录请求，验证凭据并创建会话
 *
 * 请求格式：
 * ```json
 * {
 *   "identifier": "用户名或邮箱",
 *   "password": "密码"
 * }
 * ```
 *
 * 响应格式：
 * ```json
 * {
 *   "success": true,
 *   "token": "JWT token",
 *   "user": {
 *     "id": "用户ID",
 *     "email": "邮箱",
 *     "username": "用户名",
 *     "roleCode": "角色代码",
 *     "role": {
 *       "name": "角色名称",
 *       "permissions": ["权限1", "权限2"]
 *     }
 *   }
 * }
 * ```
 *
 * 错误响应：
 * ```json
 * {
 *   "success": false,
 *   "error": "错误信息"
 * }
 * ```
 *
 * @module LoginAPI
 */

import { NextRequest } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { compare } from "bcryptjs"
import { UAParser } from 'ua-parser-js'
import { createAccountLockedNotification } from "@/lib/notification-helper"
import { csrfProtection } from "@/app/api/csrf-middleware"
import { AuthLogService } from "@/app/lib/auth-log-service"

// JWT密钥配置
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

// 登录失败锁定设置
const MAX_LOGIN_ATTEMPTS = 5
const LOCK_DURATION = 30 * 60 * 1000

async function handleLogin(request: NextRequest) {
  const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2, 5)

  try {
    const body = await request.json()
    const { identifier, password, remember } = body
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1'
    const userAgent = request.headers.get('user-agent') || ''

    // 查询用户和必要的关联数据
    let user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: identifier },
          { email: identifier }
        ]
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      await AuthLogService.logLoginAttempt({
        username: identifier,
        success: false,
        reason: '用户不存在',
        ipAddress,
        userAgent,
        requestId
      })
      return Response.json({ success: false, error: "用户不存在" }, { status: 401 })
    }

    // 验证密码
    const isValid = await compare(password, user.password)
    if (!isValid) {
      await AuthLogService.logLoginAttempt({
        userId: user.id,
        username: user.username,
        success: false,
        reason: '密码错误',
        ipAddress,
        userAgent,
        requestId
      })
      return Response.json({ success: false, error: "密码错误" }, { status: 401 })
    }

    // 提取权限列表
    const permissions = user.role?.rolePermissions.map(rp => rp.permission.code) || []

    // 创建最小化的 token payload
    const tokenPayload = {
      sub: user.id,
      username: user.username,
      role: user.roleCode,
      permissions,
      type: 'access_token'
    }

    // 生成 JWT token
    const accessToken = await new jose.SignJWT(tokenPayload)
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setIssuedAt()
      .setExpirationTime(remember ? '30d' : '24h')
      .sign(JWT_SECRET)

    // 设置 cookie
    const cookieStore = cookies()
    const expiresIn = remember ? 30 * 24 * 60 * 60 : 24 * 60 * 60
    const expiresAt = new Date(Date.now() + expiresIn * 1000)

    // 清除旧的 cookies
    cookieStore.delete('token')
    cookieStore.delete('logged_in')
    
    // 设置新的 cookies
    cookieStore.set('token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      expires: expiresAt
    })

    cookieStore.set('logged_in', 'true', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      expires: expiresAt
    })

    // 更新用户最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    })

    // 返回最小化的用户数据
    const { password: _, ...safeUser } = user
    return Response.json({
      success: true,
      data: {
        user: {
          id: safeUser.id,
          username: safeUser.username,
          email: safeUser.email,
          roleCode: safeUser.roleCode,
          role: {
            code: safeUser.role?.code,
            name: safeUser.role?.name,
            permissions
          }
        }
      }
    })
  } catch (error) {
    console.error("登录错误:", error)
    return Response.json(
      { success: false, error: "登录过程中发生错误" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  return csrfProtection(request, handleLogin)
}