import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { options } from "../../[...nextauth]/options"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 更新用户会话信息
 * 此API用于在用户信息变更后更新NextAuth会话
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "会话更新API");
    if (response) {
      return response;
    }

    // 获取当前会话
    const session = await getServerSession(options)
    if (!session) {
      return NextResponse.json(
        { success: false, message: "未找到有效会话" },
        { status: 401 }
      )
    }

    // 获取请求数据
    const data = await req.json()
    const { field, value } = data

    if (!field || value === undefined) {
      return NextResponse.json(
        { success: false, message: "缺少必要参数" },
        { status: 400 }
      )
    }

    // 验证字段是否允许更新
    const allowedFields = ["name", "email", "image"]
    if (!allowedFields.includes(field)) {
      return NextResponse.json(
        { success: false, message: "不允许更新该字段" },
        { status: 400 }
      )
    }

    // 从数据库获取最新的用户信息
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        username: true,
        name: true,
        email: true,
        image: true,
        roleCode: true,
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true
          }
        }
      }
    })

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 更新会话
    // 注意：NextAuth 会话更新是通过 cookie 完成的
    // 这里我们返回最新的用户信息，前端会使用这些信息更新本地状态
    // 当用户刷新页面时，NextAuth 会从数据库获取最新的用户信息

    // 添加时间戳到图片URL，避免缓存问题
    if (field === 'image' && updatedUser.image) {
      updatedUser.image = updatedUser.image.includes('?t=')
        ? updatedUser.image
        : `${updatedUser.image}?t=${Date.now()}`;
    }

    return NextResponse.json({
      success: true,
      data: {
        user: updatedUser
      },
      message: "会话信息已更新"
    })
  } catch (error) {
    console.error("更新会话失败:", error)
    return NextResponse.json(
      { success: false, message: "更新会话失败" },
      { status: 500 }
    )
  }
}
