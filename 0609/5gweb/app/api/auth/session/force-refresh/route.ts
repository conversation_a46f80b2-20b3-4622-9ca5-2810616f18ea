import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { options } from "../../[...nextauth]/options"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { cookies } from "next/headers"
import { sign } from "jsonwebtoken"

/**
 * 强制刷新用户会话
 * 此API用于在用户信息变更后强制刷新NextAuth会话
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "会话强制刷新API");
    if (response) {
      return response;
    }

    // 获取当前会话
    const session = await getServerSession(options)
    if (!session) {
      return NextResponse.json(
        { success: false, message: "未找到有效会话" },
        { status: 401 }
      )
    }

    // 从数据库获取最新的用户信息
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 添加时间戳到图片URL，避免缓存问题
    if (updatedUser.image) {
      updatedUser.image = updatedUser.image.includes('?t=')
        ? updatedUser.image
        : `${updatedUser.image}?t=${Date.now()}`;
    }

    // 清除敏感信息
    const { password, ...userWithoutPassword } = updatedUser;

    // 设置一个特殊的cookie，指示客户端需要刷新页面
    // 这是因为NextAuth的会话只能在服务器端或通过完整页面刷新来更新
    cookies().set('force_refresh', 'true', {
      path: '/',
      maxAge: 10, // 只保留10秒
      httpOnly: false, // 允许JavaScript访问
      sameSite: 'strict'
    });

    // 设置一个特殊的cookie，用于更新头像
    cookies().set('avatar_updated', updatedUser.image || '', {
      path: '/',
      maxAge: 60 * 60, // 保留1小时
      httpOnly: false, // 允许JavaScript访问
      sameSite: 'strict'
    });

    // 返回更新后的用户信息
    return NextResponse.json({
      success: true,
      data: {
        user: userWithoutPassword,
        needRefresh: true
      },
      message: "会话已标记为需要刷新"
    })
  } catch (error) {
    console.error("刷新会话失败:", error)
    return NextResponse.json(
      { success: false, message: "刷新会话失败" },
      { status: 500 }
    )
  }
}
