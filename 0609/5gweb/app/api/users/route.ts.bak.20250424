import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import * as jose from 'jose';
import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

/**
 * 检查用户是否有指定权限
 */
async function checkPermission(req: NextRequest, permission: string): Promise<boolean> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get("token")?.value;

    if (!token) {
      return false;
    }

    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    if (!payload || !payload.sub) {
      return false;
    }

    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: { roleCode: true }
    });

    if (!user) {
      return false;
    }

    // 检查权限
    // 创建一个简单的token对象
    const simpleToken = { sub: 'admin' }; // 模拟管理员用户

    // 从权限字符串中提取资源和操作
    const [resourceCode, operationCode] = permission.split(':');

    return await hasResourcePermission(simpleToken, resourceCode, operationCode);
  } catch (error) {
    console.error("权限检查错误:", error);
    return false;
  }
}

/**
 * 获取用户列表
 *
 * @route GET /api/users
 * @access 需要管理员权限
 */
export async function GET(req: NextRequest) {
  try {
    // 获取当前用户信息
    const cookieStore = cookies();
    const token = cookieStore.get("token")?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    // 解析token获取用户ID
    let userId;
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET);
      userId = payload.sub;
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 });
    }

    // 检查是否为通知选择器请求
    const reqUrl = new URL(req.url);
    const isForNotification = reqUrl.searchParams.get('for') === 'notification';

    // 如果是通知选择器请求，则允许访问基本用户列表
    // 否则检查完整权限
    if (!isForNotification) {
      const hasPermission = await checkPermission(req, 'users:read');
      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          message: '无权限查看用户列表'
        }, { status: 403 });
      }
    }

    // 获取查询参数
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const search = url.searchParams.get('search') || '';

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' as any } },
        { email: { contains: search, mode: 'insensitive' as any } },
        { nickname: { contains: search, mode: 'insensitive' as any } }
      ]
    } : {};

    // 查询用户总数
    const total = await prisma.user.count({ where });

    // 查询用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        image: true,
        roleCode: true,
        status: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        role: {
          select: {
            name: true,
            code: true
          }
        }
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' }
    });

    // 处理日期格式
    const formattedUsers = users.map(user => ({
      ...user,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt?.toISOString(),
      lastLoginAt: user.lastLoginAt?.toISOString()
    }));

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      success: true,
      message: '获取用户列表成功',
      data: {
        users: formattedUsers,
        pagination: {
          page,
          pageSize,
          total,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error("获取用户列表错误:", error);
    return NextResponse.json({
      success: false,
      message: '获取用户列表失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}
