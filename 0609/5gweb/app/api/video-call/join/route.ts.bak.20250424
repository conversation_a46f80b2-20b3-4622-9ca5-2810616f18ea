import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

interface VideoCallParticipant {
  user: {
    id: string
    username: string
    image: string | null
  }
}

interface VideoCall {
  id: string
  participants: VideoCallParticipant[]
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未登录",
          data: null,
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { callId } = body

    // 验证必要参数
    if (!callId) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      )
    }

    // 模拟检查视频通话
    // 注意：由于没有找到videoCall模型，我们这里模拟返回结果
    const videoCall = {
      id: callId,
      status: 'active',
      creatorId: 'user1',
      participants: [
        {
          id: '1',
          userId: 'user1',
          user: {
            id: 'user1',
            username: 'user1',
            image: '/avatar1.png'
          }
        }
      ]
    } as any

    if (!videoCall) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "视频通话不存在",
          data: null,
        },
        { status: 404 }
      )
    }

    // 模拟检查用户是否已经是参与者
    const isParticipant = videoCall.participants.some(
      (p: any) => p.user.id === session.user.id
    ) || false

    if (isParticipant) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "已经是参与者",
          data: null,
        },
        { status: 400 }
      )
    }

    // 模拟加入视频通话
    // 注意：由于没有找到videoCall模型，我们这里模拟更新操作
    console.log('模拟用户加入视频通话:', session.user.id, '到通话:', callId)

    // 模拟更新后的视频通话数据
    const updatedVideoCall = {
      id: callId,
      status: 'active',
      creatorId: 'user1',
      participants: [
        {
          id: '1',
          userId: 'user1',
          user: {
            id: 'user1',
            username: 'user1',
            image: '/avatar1.png'
          }
        },
        {
          id: '2',
          userId: session.user.id,
          user: {
            id: session.user.id,
            username: session.user.name || 'guest',
            image: session.user.image || '/default-avatar.png'
          }
        }
      ]
    } as any

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "加入视频通话成功",
        data: updatedVideoCall,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("加入视频通话错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}