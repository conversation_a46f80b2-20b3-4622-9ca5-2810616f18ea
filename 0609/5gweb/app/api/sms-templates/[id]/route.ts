import logger from '@/lib/utils/logger';

/**
 * 单个短信模板API
 * 用于获取、更新和删除单个短信模板
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { z } from "zod"

// 短信模板更新验证模式
const smsTemplateUpdateSchema = z.object({
  name: z.string().min(1, "模板名称不能为空").optional(),
  content: z.string().min(1, "模板内容不能为空").optional(),
  type: z.enum(["text", "video", "flash"], {
    errorMap: () => ({ message: "模板类型必须是 text、video 或 flash" }),
  }).optional(),
  isActive: z.boolean().optional(),
})

/**
 * 获取单个短信模板
 * GET /api/sms-templates/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAuth(request, "获取短信模板详情");
    if (authResult.response) {
      return authResult.response;
    }

    // 获取短信模板
    const template = await prisma.smsTemplate.findUnique({
      where: { id: params.id },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        {
          success: false,
          message: "短信模板不存在",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    logger.error("获取短信模板详情失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取短信模板详情失败",
      },
      { status: 500 }
    );
  }
}

/**
 * 更新短信模板
 * PUT /api/sms-templates/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "更新短信模板");
    if (authResult.response) {
      return authResult.response;
    }

    // 获取请求体
    const data = await request.json();

    // 验证请求数据
    const validationResult = smsTemplateUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: "数据验证失败",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    // 检查模板是否存在
    const existingTemplate = await prisma.smsTemplate.findUnique({
      where: { id: params.id },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          message: "短信模板不存在",
        },
        { status: 404 }
      );
    }

    // 更新短信模板
    const template = await prisma.smsTemplate.update({
      where: { id: params.id },
      data,
    });

    return NextResponse.json({
      success: true,
      message: "短信模板更新成功",
      data: template,
    });
  } catch (error) {
    logger.error("更新短信模板失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新短信模板失败",
      },
      { status: 500 }
    );
  }
}

/**
 * 删除短信模板
 * DELETE /api/sms-templates/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "删除短信模板");
    if (authResult.response) {
      return authResult.response;
    }

    // 检查模板是否存在
    const existingTemplate = await prisma.smsTemplate.findUnique({
      where: { id: params.id },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          message: "短信模板不存在",
        },
        { status: 404 }
      );
    }

    // 删除短信模板
    await prisma.smsTemplate.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "短信模板删除成功",
    });
  } catch (error) {
    logger.error("删除短信模板失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "删除短信模板失败",
      },
      { status: 500 }
    );
  }
}
