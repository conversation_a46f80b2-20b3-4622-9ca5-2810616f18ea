import logger from '@/lib/utils/logger';

/**
 * 短信模板API
 * 用于管理短信模板
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { z } from "zod"

// 短信模板验证模式
const smsTemplateSchema = z.object({
  code: z.string().min(1, "模板代码不能为空"),
  name: z.string().min(1, "模板名称不能为空"),
  content: z.string().min(1, "模板内容不能为空"),
  type: z.enum(["text", "video", "flash"], {
    errorMap: () => ({ message: "模板类型必须是 text、video 或 flash" }),
  }),
  isActive: z.boolean().optional(),
})

/**
 * 获取短信模板列表
 * GET /api/sms-templates
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAuth(request, "获取短信模板列表");
    if (authResult.response) {
      return authResult.response;
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get("type");
    const isActive = searchParams.get("isActive") === "true";
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search") || "";

    // 构建查询条件
    const where: any = {};
    if (type) {
      where.type = type;
    }
    if (searchParams.has("isActive")) {
      where.isActive = isActive;
    }
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    // 查询总数
    const total = await prisma.smsTemplate.count({ where });

    // 查询短信模板
    const templates = await prisma.smsTemplate.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        items: templates,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      },
    });
  } catch (error) {
    logger.error("获取短信模板列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取短信模板列表失败",
      },
      { status: 500 }
    );
  }
}

/**
 * 创建短信模板
 * POST /api/sms-templates
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "创建短信模板");
    if (authResult.response) {
      return authResult.response;
    }

    // 获取请求体
    const data = await request.json();

    // 验证请求数据
    const validationResult = smsTemplateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: "数据验证失败",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    // 检查模板代码是否已存在
    const existingTemplate = await prisma.smsTemplate.findUnique({
      where: { code: data.code },
    });

    if (existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          message: "模板代码已存在",
        },
        { status: 400 }
      );
    }

    // 创建短信模板
    const template = await prisma.smsTemplate.create({
      data: {
        ...data,
        createdById: authResult.user!.id,
      },
    });

    return NextResponse.json({
      success: true,
      message: "短信模板创建成功",
      data: template,
    });
  } catch (error) {
    logger.error("创建短信模板失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "创建短信模板失败",
      },
      { status: 500 }
    );
  }
}
