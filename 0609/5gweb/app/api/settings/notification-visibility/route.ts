import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { checkPermission } from "@/lib/casbin/enforcer";
import logger from '@/lib/utils/logger';

/**
 * 获取通知组件可见性设置
 *
 * @route GET /api/settings/notification-visibility
 * @access 需要 settings:view 权限
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "获取通知组件可见性设置API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    // 检查用户是否有权限查看设置
    const hasPermission = await checkPermission(request, 'settings:view');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权查看设置'
      }, { status: 403 });
    }

    // 获取所有角色
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        code: true,
        name: true,
        type: true,
        notificationEnabled: true
      },
      orderBy: {
        id: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      data: roles
    });
  } catch (error) {
    logger.error('获取通知组件可见性设置失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取通知组件可见性设置失败'
    }, { status: 500 });
  }
}

/**
 * 更新通知组件可见性设置
 *
 * @route PUT /api/settings/notification-visibility
 * @access 需要 settings:edit 权限
 */
export async function PUT(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "更新通知组件可见性设置API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    // 检查用户是否有权限编辑设置
    const hasPermission = await checkPermission(request, 'settings:edit');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权编辑设置'
      }, { status: 403 });
    }

    // 解析请求体
    const body = await request.json();
    const { roleCode, enabled } = body;

    if (!roleCode) {
      return NextResponse.json({
        success: false,
        message: '角色代码不能为空'
      }, { status: 400 });
    }

    // 更新角色的通知可见性设置
    const updatedRole = await prisma.role.update({
      where: { code: roleCode },
      data: { notificationEnabled: enabled },
      select: {
        id: true,
        code: true,
        name: true,
        type: true,
        notificationEnabled: true
      }
    });

    return NextResponse.json({
      success: true,
      message: '通知组件可见性设置已更新',
      data: updatedRole
    });
  } catch (error) {
    logger.error('更新通知组件可见性设置失败:', error);
    return NextResponse.json({
      success: false,
      message: '更新通知组件可见性设置失败'
    }, { status: 500 });
  }
}
