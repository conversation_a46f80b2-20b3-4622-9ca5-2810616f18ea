import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { withAuth } from "@/lib/middleware/with-auth"
import logger from "@/lib/utils/logger"
import crypto from "crypto"

/**
 * 生成字符串的哈希值
 * @param data 要哈希的数据
 * @returns 哈希值（十六进制字符串）
 */
async function generateHash(data: string): Promise<string> {
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * 获取系统设置
 *
 * 使用tryAuth方法，允许未登录用户访问，但会根据用户角色返回不同的设置信息：
 * - 管理员用户：返回完整的系统设置
 * - 普通用户：返回过滤后的系统设置
 * - 未登录用户：返回最小化的公开系统设置
 *
 * 安全考虑：
 * - 未登录用户只能看到公开的设置信息
 * - 敏感设置信息（如安全配置）只对管理员可见
 * - 使用ETag和缓存控制优化性能
 * - 速率限制防止API滥用
 */
export const GET = withAuth(
  async (request: NextRequest, { user, requestId }) => {
    try {
      // 获取查询参数
      const url = new URL(request.url);
      const timestamp = url.searchParams.get('t') || Date.now().toString();

      // 记录API请求
      logger.log(`[系统设置API:${requestId}] 获取系统设置`);

      // 检查用户是否已登录
      const isAuthenticated = !!user;
      const isAdmin = isAuthenticated && user.roleCode === 'ADMIN';

      if (isAuthenticated) {
        logger.log(`[系统设置API:${requestId}] 已登录用户，ID: ${user.id}, 角色: ${user.roleCode}`);
      } else {
        logger.log(`[系统设置API:${requestId}] 未登录用户`);
      }

      // 从数据库获取系统设置
      const settings = await prisma.systemSettings.findFirst()

    // 如果没有设置记录，创建默认设置
    if (!settings) {
      const defaultSettings = {
        siteName: "外呼管理系统",
        logo: "/logo.png",
        footerText: "",
        description: "高效的外呼任务管理平台",
        keywords: "外呼,管理系统,任务管理",
        applicationName: "外呼管理系统",
        appleMobileWebAppTitle: "外呼系统",
        theme: {
          primaryColor: "#0284c7",
          mode: "system"
        },
        features: {
          enableRegistration: true,
          enablePasswordReset: true,
          enableNotifications: true
        },
        security: {
          passwordMinLength: 8,
          requireSpecialChar: true,
          requireNumber: true,
          requireUppercase: true,
          loginAttempts: 5,
          sessionTimeout: 30
        },
        loginPage: {
          backgroundEffect: "all",
          title: "外呼管理系统",
          subtitle: "提升工作效率的得力助手",
          features: [
            { icon: "user", text: "专业的客户管理" },
            { icon: "lock", text: "安全的数据保护" },
            { icon: "mail", text: "高效的沟通工具" }
          ]
        }
      }

      // 创建默认设置记录
      const newSettings = await prisma.systemSettings.create({
        data: defaultSettings
      })

      return NextResponse.json({
        success: true,
        data: newSettings
      })
    }

    // 处理 logo URL，添加时间戳防止缓存
    let processedSettings = { ...settings };
    if (processedSettings.logo && !processedSettings.logo.includes('?')) {
      processedSettings.logo = `${processedSettings.logo}?t=${timestamp}`;
    }

    // 根据用户角色过滤设置数据
    let filteredSettings;

    if (isAdmin) {
      // 管理员可以看到所有设置
      logger.log(`[系统设置API:${requestId}] 管理员用户，返回完整设置`);
      filteredSettings = processedSettings;
    } else if (isAuthenticated) {
      // 已登录普通用户可以看到部分设置，但不包括安全配置
      logger.log(`[系统设置API:${requestId}] 普通用户，返回过滤后的设置`);
      const { security, ...userSettings } = processedSettings;
      filteredSettings = userSettings;
    } else {
      // 未登录用户只能看到最小化的公开设置
      logger.log(`[系统设置API:${requestId}] 未登录用户，返回公开设置`);
      filteredSettings = {
        siteName: processedSettings.siteName,
        logo: processedSettings.logo,
        footerText: processedSettings.footerText,
        theme: {
          mode: processedSettings.theme?.mode || 'system'
        },
        loginPage: processedSettings.loginPage
      };
    }

    // 设置缓存控制，根据用户类型设置不同的缓存策略
    let cacheControl = '';

    if (isAdmin) {
      // 管理员：使用私有缓存，短时间有效，需要验证
      cacheControl = 'private, max-age=30, must-revalidate';
    } else if (isAuthenticated) {
      // 已登录用户：使用私有缓存，较长时间有效，需要验证
      cacheControl = 'private, max-age=60, must-revalidate';
    } else {
      // 未登录用户：可以使用共享缓存，较长时间有效，但需要验证
      cacheControl = 'public, max-age=300, must-revalidate';
    }

    // 生成ETag（基于用户ID和设置数据的哈希）
    const settingsData = JSON.stringify(filteredSettings);
    const dataHash = await generateHash(isAuthenticated ? `${user.id}:${settingsData}` : `guest:${settingsData}`);
    const etag = `"${dataHash}"`;

    // 检查客户端的If-None-Match头
    const ifNoneMatch = request.headers.get('if-none-match');
    if (ifNoneMatch === etag) {
      // 内容未变更，返回304 Not Modified
      return new NextResponse(null, {
        status: 304,
        headers: {
          'Cache-Control': cacheControl,
          'ETag': etag
        }
      });
    }

    // 返回响应
    return NextResponse.json(
      {
        success: true,
        data: filteredSettings,
        timestamp: timestamp // 使用请求中的时间戳或当前时间
      },
      {
        headers: {
          'Cache-Control': cacheControl,
          'ETag': etag,
          'Expires': '0',
        }
      }
    )
  } catch (error) {
    logger.error(`[系统设置API:${requestId}] 获取系统设置失败:`, error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取系统设置失败",
        requestId
      },
      { status: 500 }
    )
  }
  },
  { tryAuth: true, apiName: "获取系统设置API" }
)

/**
 * 更新系统设置
 *
 * 需要管理员权限
 */
export const POST = withAuth(
  async (request: NextRequest, { user, requestId }) => {
    try {
      // 记录API请求
      logger.log(`[系统设置API:${requestId}] 更新系统设置`);

      // 用户必须是管理员
      if (user.roleCode !== 'ADMIN') {
        logger.warn(`[系统设置API:${requestId}] 非管理员用户尝试更新系统设置，用户ID: ${user.id}, 角色: ${user.roleCode}`);
        return NextResponse.json(
          {
            success: false,
            message: "未授权访问或没有管理员权限",
            requestId
          },
          { status: 403 }
        );
      }

    const data = await request.json()

    // 验证必要字段
    if (!data.siteName) {
      return NextResponse.json(
        {
          success: false,
          message: "网站名称不能为空"
        },
        { status: 400 }
      )
    }

    // 确保 theme 和 features 是对象
    const theme = typeof data.theme === 'object' ? data.theme : {}
    const features = typeof data.features === 'object' ? data.features : {}

    // 确保 security 是对象
    const security = typeof data.security === 'object' ? data.security : {}

    // 确保 loginPage 是对象
    const loginPage = typeof data.loginPage === 'object' ? data.loginPage : {}

    // 记录旧的Logo和网站名称，用于检测是否需要触发特殊更新
    const oldSettings = await prisma.systemSettings.findFirst({
      where: { id: 1 }
    })

    const logoChanged = oldSettings && oldSettings.logo !== data.logo && data.logo
    const nameChanged = oldSettings && oldSettings.siteName !== data.siteName && data.siteName

    // 不再添加时间戳，避免无限循环请求
    let logoUrl = data.logo || "/logo.png"

    // 更新系统设置
    const settings = await prisma.systemSettings.upsert({
      where: {
        id: 1
      },
      update: {
        siteName: data.siteName,
        logo: logoUrl,
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      },
      create: {
        siteName: data.siteName,
        logo: logoUrl,
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      }
    })

    // 不再使用 revalidatePath，避免静态生成存储错误

    // 如果Logo或网站名称发生变化，触发特殊事件
    if (logoChanged || nameChanged) {
      // 在响应中添加标记，表示需要刷新
      const responseData = {
        success: true,
        data: settings,
        timestamp: Date.now(),
        needsRefresh: true,
        changes: {
          logo: logoChanged,
          name: nameChanged
        }
      }

      return NextResponse.json(responseData)
    } else {
      // 不再添加时间戳，避免无限循环请求
      const responseData = {
        success: true,
        data: settings
      }

      return NextResponse.json(responseData)
    }
  } catch (error) {
    logger.error(`[系统设置API:${requestId}] 更新系统设置失败:`, error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新系统设置失败",
        requestId
      },
      { status: 500 }
    )
  }
  },
  { requireAdmin: true, apiName: "更新系统设置API" }
)