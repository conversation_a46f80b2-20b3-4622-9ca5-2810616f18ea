import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { prisma } from "@/lib/prisma";
import { sendAuthCodeVerificationEmail } from "@/lib/services/email-auth-code";
import { generateVerificationCode, storeVerificationCode } from "@/lib/services/verification";
import nodemailer from "nodemailer";
import logger from '@/lib/utils/logger';

/**
 * 发送邮箱验证码
 * POST /api/settings/email/send-verification
 * 
 * 用于在修改邮箱授权码前发送验证码到管理员邮箱
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "发送邮箱验证码");
    if (authResult.response) {
      return authResult.response;
    }
    
    const user = authResult.user;
    if (!user?.email) {
      return NextResponse.json({
        success: false,
        message: "无法获取管理员邮箱地址"
      }, { status: 400 });
    }

    // 生成验证码
    const code = generateVerificationCode();
    
    // 存储验证码
    try {
      await storeVerificationCode(user.email, code, "email_auth_code", 300); // 5分钟有效期
    } catch (error) {
      logger.error("存储验证码失败:", error);
      return NextResponse.json({
        success: false,
        message: "验证码存储失败，请稍后重试"
      }, { status: 500 });
    }
    
    // 发送验证码邮件
    try {
      // 获取当前邮件设置
      const settings = await prisma.systemSettings.findFirst();
      if (!settings || !settings.emailSettings) {
        return NextResponse.json({
          success: false,
          message: "邮件设置不存在，请先配置邮件设置"
        }, { status: 404 });
      }
      
      // 创建邮件传输器
      const emailSettings = settings.emailSettings as any;
      const transporter = nodemailer.createTransport({
        host: emailSettings.host,
        port: parseInt(emailSettings.port),
        secure: emailSettings.secure,
        auth: {
          user: emailSettings.auth.user,
          pass: emailSettings.auth.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      });
      
      // 发送验证码邮件
      await sendAuthCodeVerificationEmail(user.email, code, transporter);
      
      return NextResponse.json({
        success: true,
        message: "验证码已发送到您的邮箱"
      });
    } catch (error) {
      logger.error("发送验证码邮件失败:", error);
      return NextResponse.json({
        success: false,
        message: "发送验证码邮件失败，请稍后重试"
      }, { status: 500 });
    }
  } catch (error) {
    logger.error("发送邮箱验证码失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "发送邮箱验证码失败"
    }, { status: 500 });
  }
}
