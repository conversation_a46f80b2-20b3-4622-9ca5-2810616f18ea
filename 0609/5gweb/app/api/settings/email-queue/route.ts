import logger from '@/lib/utils/logger';

/**
 * 邮件队列管理API
 * 用于查询和管理邮件队列
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import CronService from "@/lib/services/cron-service"

/**
 * 获取邮件队列列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.checkPermission(request, 'settings:read');
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: authResult.message || '无权访问'
      }, { status: 401 });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const status = url.searchParams.get('status') || undefined;
    const search = url.searchParams.get('search') || undefined;

    // 构建查询条件
    const where: any = {};
    if (status) {
      where.status = status;
    }
    if (search) {
      where.OR = [
        { to: { contains: search, mode: 'insensitive' } },
        { subject: { contains: search, mode: 'insensitive' } }
      ];
    }

    // 查询总数
    const total = await prisma.emailQueue.count({ where });

    // 查询邮件队列
    const emails = await prisma.emailQueue.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            username: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        items: emails,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    });
  } catch (error) {
    logger.error("获取邮件队列失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "获取邮件队列失败"
    }, { status: 500 });
  }
}

/**
 * 手动处理邮件队列
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.checkPermission(request, 'settings:write');
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: authResult.message || '无权访问'
      }, { status: 401 });
    }

    // 处理邮件队列
    const result = await CronService.processEmails();

    return NextResponse.json(result);
  } catch (error) {
    logger.error("处理邮件队列失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "处理邮件队列失败"
    }, { status: 500 });
  }
}

/**
 * 清理邮件队列
 */
export async function DELETE(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.checkPermission(request, 'settings:write');
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: authResult.message || '无权访问'
      }, { status: 401 });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const days = parseInt(url.searchParams.get('days') || '7');
    const status = url.searchParams.get('status');

    // 构建查询条件
    const where: any = {
      createdAt: {
        lt: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      }
    };

    if (status) {
      where.status = status;
    } else {
      where.status = {
        in: ['sent', 'failed']
      };
    }

    // 删除邮件队列
    const result = await prisma.emailQueue.deleteMany({ where });

    return NextResponse.json({
      success: true,
      message: `成功清理 ${result.count} 条邮件记录`,
      count: result.count
    });
  } catch (error) {
    logger.error("清理邮件队列失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "清理邮件队列失败"
    }, { status: 500 });
  }
}
