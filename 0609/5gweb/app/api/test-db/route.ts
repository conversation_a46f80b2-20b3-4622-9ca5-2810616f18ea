import { NextResponse } from "next/server"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"

export async function GET() {
  try {
    // 查询任务表中的数据
    const tasks = await db.task.findMany({
      take: 10,
      orderBy: {
        createdAt: "desc"
      }
    });

    // 返回查询结果
    return NextResponse.json({
      success: true,
      message: "数据库连接测试成功",
      count: tasks.length,
      data: tasks
    });
  } catch (error) {
    logger.error("数据库连接测试失败:", error);
    
    return NextResponse.json({
      success: false,
      message: "数据库连接测试失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
