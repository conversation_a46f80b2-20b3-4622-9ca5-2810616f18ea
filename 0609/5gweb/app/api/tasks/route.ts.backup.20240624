import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { AuthMiddleware } from "@/lib/auth-middleware";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取任务列表API");
    if (response) {
      return response;
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const startDate = searchParams.get("startDate") || "";
    const endDate = searchParams.get("endDate") || "";
    const upload = searchParams.get("upload") === "true";

    // 构建查询条件
    let where: any = {};

    // 如果不是管理员或超级管理员，只能查看自己的任务
    if (user.role !== "super" && user.role !== "admin") {
      where.userId = user.id;
    }

    // 搜索条件
    if (search) {
      where.name = {
        contains: search,
      };
    }

    // 状态过滤
    if (status) {
      where.status = status;
    }

    // 日期范围过滤
    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      where.createdAt = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      where.createdAt = {
        lte: new Date(endDate),
      };
    }

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 查询总数
    const total = await db.task.count({ where });

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    // 查询任务列表
    let tasks;
    if (upload) {
      // 如果是上传页面，只返回最近的10条任务
      tasks = await db.task.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        take: 10,
      });
    } else {
      // 否则返回分页数据
      tasks = await db.task.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: pageSize,
      });
    }

    // 格式化任务数据
    const paginatedTasks = tasks.map((task) => {
      return {
        id: task.id,
        name: task.name,
        type: task.type,
        content: task.content,
        startTime: task.startTime,
        progress: task.progress,
        creator: task.creator,
        status: task.status,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        // 其他需要返回的字段
      };
    });

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取任务列表成功",
        data: {
          list: paginatedTasks,
          total,
          page,
          pageSize,
          totalPages,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("获取任务列表错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "创建任务API");
    if (response) {
      return response;
    }
    // 解析请求体
    const body = await request.json()
    const { name, content, callType, startTime, resource, phoneNumber, smsType, smsTemplate } = body

    // 验证请求参数
    if (!name || !content || !callType || !startTime || !resource) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 },
      )
    }

    let newTask;
    // 创建新任务（数据库）
    logger.log("使用数据库创建任务")
    newTask = await db.task.create({
      data: {
        name,
        type: callType,
        content,
        startTime: new Date(startTime),
        progress: 0,
        creator: (user.role === "super" || user.role === "admin") ? "管理员" : (user.name || user.username),
        status: "未开始",
        userId: user.id,
        // 其他字段根据实际数据库模型添加
      }
    });

    // 返回成功响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "创建任务成功",
        data: newTask,
      },
      { status: 200 },
    )
  } catch (error) {
    logger.error("创建任务错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

