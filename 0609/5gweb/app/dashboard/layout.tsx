"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/hooks/use-auth"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar"
import {
  DashboardIcon,
  ProjectIcon,
  BellIcon,
  UserIcon,
  TeamIcon,
  SettingsIcon
} from "@/components/icons"
import { cn } from "@/lib/utils"
import { useMediaQuery } from "@/hooks/use-media-query"

interface MenuItem {
  title: string
  href: string
  icon?: React.ReactNode
  children?: Omit<MenuItem, 'children'>[]
}

interface MenuData {
  id: string
  name: string
  path: string
  icon?: string
  parentId?: string
  visible: boolean
  order?: number
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [isCollapsed, setIsCollapsed] = useState(false)
  const isMobile = useMediaQuery("(max-width: 768px)")

  // 初始化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebar-collapsed')
      if (savedState === 'true') {
        setIsCollapsed(true)
      }
    }
  }, [])

  // 处理折叠状态变化
  const handleToggleSidebar = () => {
    const newState = !isCollapsed
    setIsCollapsed(newState)
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', String(newState))
    }
  }

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push("/login")
        return
      }

      if (user?.data?.role?.menus && Array.isArray(user.data.role.menus)) {
        const menus = user.data.role.menus as MenuData[]
        
        // 获取图标组件
        const getIcon = (iconName?: string | null) => {
          if (!iconName) return undefined
          
          // 图标名称映射表
          const iconMap: Record<string, React.ReactNode> = {
            "仪表盘": <DashboardIcon className="h-4 w-4" />,
            "任务管理": <ProjectIcon className="h-4 w-4" />,
            "通知中心": <BellIcon className="h-4 w-4" />,
            "用户中心": <UserIcon className="h-4 w-4" />,
            "团队管理": <TeamIcon className="h-4 w-4" />,
            "系统设置": <SettingsIcon className="h-4 w-4" />,
            // 英文映射
            "dashboard": <DashboardIcon className="h-4 w-4" />,
            "project": <ProjectIcon className="h-4 w-4" />,
            "notification": <BellIcon className="h-4 w-4" />,
            "user": <UserIcon className="h-4 w-4" />,
            "team": <TeamIcon className="h-4 w-4" />,
            "settings": <SettingsIcon className="h-4 w-4" />,
            // Ant Design 图标名称
            "DashboardOutlined": <DashboardIcon className="h-4 w-4" />,
            "ProjectOutlined": <ProjectIcon className="h-4 w-4" />,
            "BellOutlined": <BellIcon className="h-4 w-4" />,
            "UserOutlined": <UserIcon className="h-4 w-4" />,
            "TeamOutlined": <TeamIcon className="h-4 w-4" />,
            "SettingOutlined": <SettingsIcon className="h-4 w-4" />
          }

          // 尝试直接匹配，然后尝试小写匹配
          const icon = iconMap[iconName] || iconMap[iconName.toLowerCase()]
          return icon || undefined
        }

        // 处理顶级菜单
        const formattedMenus = menus
          .filter(menu => menu.visible && !menu.parentId)
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map(menu => {
            // 查找并处理子菜单
            const children = menus
              .filter(item => item.visible && item.parentId === menu.id)
              .sort((a, b) => (a.order || 0) - (b.order || 0))
              .map(child => ({
                title: child.name,
                href: child.path || "/",
                icon: getIcon(child.name) || getIcon(child.icon)
              }))

            return {
              title: menu.name,
              href: menu.path || "/",
              icon: getIcon(menu.name) || getIcon(menu.icon),
              ...(children.length > 0 ? { children } : undefined)
            }
          })

        setMenuItems(formattedMenus)
      } else {
        setMenuItems([])
      }
    }
  }, [user, loading, router])

  if (loading) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>
  }

  return (
    <div className="flex h-screen bg-background">
      <Sidebar
        customMenuItems={menuItems}
        isCollapsed={isCollapsed}
        isMobile={isMobile}
        onToggleSidebar={handleToggleSidebar}
      />
      <div 
        className={cn(
          "flex flex-1 flex-col transition-all duration-300 ease-in-out",
          isCollapsed ? "ml-16" : "ml-64",
          isMobile && "ml-0"
        )}
      >
        <main className="flex-1 overflow-y-auto p-8">
          {children}
        </main>
      </div>
    </div>
  )
}