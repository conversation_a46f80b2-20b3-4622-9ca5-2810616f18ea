"use client"
import { useTheme } from "next-themes"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ThemeConfig } from "@/components/theme-config"

export default function ThemePage() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">主题设置</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>主题设置</CardTitle>
          <CardDescription>选择预设主题或自定义颜色方案</CardDescription>
        </CardHeader>
        <CardContent>
          <ThemeConfig />
        </CardContent>
      </Card>
    </div>
  )
}

