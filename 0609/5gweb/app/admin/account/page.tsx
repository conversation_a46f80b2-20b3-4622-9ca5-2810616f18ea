"use client"

import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"

export default function AdminAccountPage() {
  const router = useRouter()

  return (
    <div className="w-full">
      <div className="mb-4">
        <h1 className="text-2xl font-bold">基本信息</h1>
        <p className="text-gray-500 text-sm">查看和管理账户的基本信息</p>
      </div>

      <div className="grid grid-cols-2 gap-y-6">
        <div>
          <div className="text-gray-500 mb-1">账户名称</div>
          <div>张三企业</div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">登录名称</div>
          <div>zhangsan</div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">初始密码</div>
          <div>******</div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">邮箱</div>
          <div><EMAIL></div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">账户类型</div>
          <div>一级客户</div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">手机号</div>
          <div>13800138000</div>
        </div>
        {/* 移除了余额和授信额度 */}
        {/* 移除了费率和计费单位 */}
        <div>
          <div className="text-gray-500 mb-1">状态</div>
          <div>启用</div>
        </div>
        <div>
          <div className="text-gray-500 mb-1">开户时间</div>
          <div>2023-01-01 09:00:00</div>
        </div>
      </div>

      <div className="flex mt-8 space-x-4">
        <Button variant="outline" onClick={() => router.back()} className="flex items-center">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <Button className="bg-blue-500 hover:bg-blue-600 text-white">编辑账号</Button>
        {/* 移除了充值按钮和充值记录按钮 */}
      </div>
    </div>
  )
}

