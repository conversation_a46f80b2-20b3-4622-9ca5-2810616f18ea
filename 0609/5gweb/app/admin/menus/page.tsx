"use client"

/**
 * 菜单管理页面
 * 提供菜单的列表展示、创建、编辑和删除功能
 */

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { DataTable } from "@/components/ui/data-table"
import { FormDialog } from "@/components/ui/form-dialog"
import { toast } from "sonner"
import { Plus } from "lucide-react"
import { FormField } from "@/types/form"
import { SortableTable } from "@/components/ui/sortable-table"
import { SyncPermissionsButton } from "@/components/sync-permissions-button"

interface Menu {
  id: string
  code: string
  name: string
  path: string
  icon: string | null
  parentId: string | null
  order: number
  visible: boolean
  parent: Menu | null
  children: Menu[]
  roles: {
    id: string
    name: string
  }[]
}

interface Role {
  id: string
  name: string
}

export default function MenusPage() {
  const [menus, setMenus] = useState<Menu[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null)
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    path: "",
    icon: "",
    parentId: "",
    order: 0,
    visible: true,
    roleIds: [] as string[],
  })

  // 获取菜单列表
  const fetchMenus = async () => {
    try {
      const response = await fetch("/api/menus")
      const data = await response.json()
      if (data.success) {
        setMenus(data.menus)
      } else {
        toast.error(data.error || "获取菜单列表失败")
      }
    } catch (error) {
      toast.error("获取菜单列表失败")
    }
  }

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await fetch("/api/roles")
      const data = await response.json()
      if (data.success) {
        setRoles(data.roles)
      }
    } catch (error) {
      toast.error("获取角色列表失败")
    }
  }

  useEffect(() => {
    fetchMenus()
    fetchRoles()
  }, [])

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const url = "/api/menus"
      const method = editingMenu ? "PUT" : "POST"
      const body = editingMenu
        ? { ...formData, id: editingMenu.id }
        : formData

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      })

      const data = await response.json()
      if (data.success) {
        toast.success(
          editingMenu
            ? "更新菜单成功"
            : "创建菜单成功"
        )
        setIsDialogOpen(false)
        setEditingMenu(null)
        setFormData({
          code: "",
          name: "",
          path: "",
          icon: "",
          parentId: "",
          order: 0,
          visible: true,
          roleIds: [],
        })
        fetchMenus()
      } else {
        toast.error(data.error || "操作失败")
      }
    } catch (error) {
      toast.error("操作失败")
    }
  }

  // 处理删除操作
  const handleDelete = async (menu: Menu) => {
    if (!confirm("确定要删除这个菜单吗？")) return

    try {
      const response = await fetch(`/api/menus?id=${menu.id}`, {
        method: "DELETE",
      })

      const data = await response.json()
      if (data.success) {
        toast.success("删除菜单成功")
        fetchMenus()
      } else {
        toast.error(data.error || "删除菜单失败")
      }
    } catch (error) {
      toast.error("删除菜单失败")
    }
  }

  // 打开编辑对话框
  const handleEdit = (menu: Menu) => {
    setEditingMenu(menu)
    setFormData({
      code: menu.code,
      name: menu.name,
      path: menu.path,
      icon: menu.icon || "",
      parentId: menu.parentId || "",
      order: menu.order,
      visible: menu.visible,
      roleIds: menu.roles.map((role) => role.id),
    })
    setIsDialogOpen(true)
  }

  // 处理菜单排序
  const handleReorder = async (newMenus: Menu[]) => {
    try {
      const updates = newMenus.map((menu, index) => ({
        id: menu.id,
        order: index,
      }))

      const response = await fetch("/api/menus/reorder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      })

      const data = await response.json()
      if (data.success) {
        toast.success("菜单排序更新成功")
        fetchMenus()
      } else {
        toast.error(data.error || "更新菜单排序失败")
      }
    } catch (error) {
      toast.error("更新菜单排序失败")
    }
  }

  const columns = [
    {
      key: "code",
      title: "菜单代码",
    },
    {
      key: "name",
      title: "菜单名称",
    },
    {
      key: "path",
      title: "路由路径",
    },
    {
      key: "icon",
      title: "图标",
    },
    {
      key: "parent",
      title: "父菜单",
      render: (menu: Menu) => menu.parent?.name || "-",
    },
    {
      key: "order",
      title: "排序",
    },
    {
      key: "visible",
      title: "是否可见",
      render: (menu: Menu) => (menu.visible ? "是" : "否"),
    },
    {
      key: "roles",
      title: "关联角色",
      render: (menu: Menu) =>
        menu.roles.map((role) => role.name).join(", "),
    },
  ]

  const formFields: FormField[] = [
    {
      name: "code",
      label: "菜单代码",
      type: "text",
      required: true,
      value: formData.code,
      onChange: (value: string) =>
        setFormData({ ...formData, code: value }),
    },
    {
      name: "name",
      label: "菜单名称",
      type: "text",
      required: true,
      value: formData.name,
      onChange: (value: string) =>
        setFormData({ ...formData, name: value }),
    },
    {
      name: "path",
      label: "路由路径",
      type: "text",
      required: true,
      value: formData.path,
      onChange: (value: string) =>
        setFormData({ ...formData, path: value }),
    },
    {
      name: "icon",
      label: "图标",
      type: "icon",
      value: formData.icon,
      onChange: (value: string) =>
        setFormData({ ...formData, icon: value }),
    },
    {
      name: "parentId",
      label: "父菜单",
      type: "select",
      options: [
        { value: "", label: "无" },
        ...menus.map((menu) => ({
          value: menu.id,
          label: menu.name,
        })),
      ],
      value: formData.parentId,
      onChange: (value: string) =>
        setFormData({ ...formData, parentId: value }),
    },
    {
      name: "order",
      label: "排序",
      type: "text",
      required: true,
      value: formData.order.toString(),
      onChange: (value: string) =>
        setFormData({ ...formData, order: parseInt(value) || 0 }),
    },
    {
      name: "visible",
      label: "是否可见",
      type: "select",
      required: true,
      options: [
        { value: "true", label: "是" },
        { value: "false", label: "否" },
      ],
      value: formData.visible.toString(),
      onChange: (value: string) =>
        setFormData({ ...formData, visible: value === "true" }),
    },
    {
      name: "roleIds",
      label: "关联角色",
      type: "select",
      required: true,
      multiple: true,
      options: roles.map((role) => ({
        value: role.id,
        label: role.name,
      })),
      value: formData.roleIds,
      onChange: (value: string[]) =>
        setFormData({ ...formData, roleIds: value }),
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">菜单管理</h1>
        <div className="flex space-x-2">
          <SyncPermissionsButton
            variant="outline"
            onSuccess={() => toast.success("权限同步成功")}
          />
          <FormDialog
            title={editingMenu ? "编辑菜单" : "新建菜单"}
            trigger={
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建菜单
              </Button>
            }
            fields={formFields}
            onSubmit={handleSubmit}
            onCancel={() => {
              setIsDialogOpen(false)
              setEditingMenu(null)
              setFormData({
                code: "",
                name: "",
                path: "",
                icon: "",
                parentId: "",
                order: 0,
                visible: true,
                roleIds: [],
              })
            }}
            open={isDialogOpen}
            onOpenChange={setIsDialogOpen}
          />
        </div>
      </div>

      <SortableTable
        columns={columns}
        data={menus}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onReorder={handleReorder}
        getItemId={(menu) => menu.id}
      />
    </div>
  )
}