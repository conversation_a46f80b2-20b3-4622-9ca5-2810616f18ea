"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { apiLogger, cryptoLogger, taskLogger, callLogger, monitorLogger } from "@/utils/debug-logger"
import { aesEncrypt, aesDecrypt } from "@/utils/crypto-utils"

export default function DebugPage() {
  const [debugValue, setDebugValue] = useState("")
  const [logOutput, setLogOutput] = useState("")
  const [encryptInput, setEncryptInput] = useState("")
  const [encryptOutput, setEncryptOutput] = useState("")
  const [decryptInput, setDecryptInput] = useState("")
  const [decryptOutput, setDecryptOutput] = useState("")

  // 调试类别
  const categories = [
    { id: "api", label: "API" },
    { id: "crypto", label: "加密" },
    { id: "task", label: "任务" },
    { id: "call", label: "通话" },
    { id: "monitor", label: "监控" },
    { id: "ui", label: "UI" },
    { id: "all", label: "全部" },
  ]

  // 初始化调试设置
  useEffect(() => {
    if (typeof window !== "undefined") {
      // 从localStorage获取调试设置
      const savedDebug = localStorage.getItem("debug-settings")
      if (savedDebug) {
        setDebugValue(savedDebug)
      }

      // 从URL参数获取调试设置
      const urlParams = new URLSearchParams(window.location.search)
      const debugParam = urlParams.get("debug")
      if (debugParam) {
        setDebugValue(debugParam)
      }
    }
  }, [])

  // 更新调试设置
  const updateDebugSettings = (value: string) => {
    setDebugValue(value)

    if (typeof window !== "undefined") {
      // 保存到localStorage
      localStorage.setItem("debug-settings", value)

      // 更新URL参数
      const url = new URL(window.location.href)
      url.searchParams.set("debug", value)
      window.history.replaceState({}, "", url.toString())
    }

    toast({
      title: "调试设置已更新",
      description: `当前调试类别: ${value || "无"}`,
    })
  }

  // 切换调试类别
  const toggleCategory = (category: string, enabled: boolean) => {
    const categories = debugValue
      .split(",")
      .map((c) => c.trim())
      .filter((c) => c)

    if (category === "all") {
      updateDebugSettings(enabled ? "*" : "")
      return
    }

    if (enabled) {
      // 添加类别
      if (!categories.includes(category)) {
        categories.push(category)
      }
    } else {
      // 移除类别
      const index = categories.indexOf(category)
      if (index !== -1) {
        categories.splice(index, 1)
      }
    }

    updateDebugSettings(categories.join(","))
  }

  // 检查类别是否启用
  const isCategoryEnabled = (category: string) => {
    if (category === "all") {
      return debugValue === "*"
    }

    if (debugValue === "*") {
      return true
    }

    const categories = debugValue.split(",").map((c) => c.trim())
    return categories.includes(category)
  }

  // 测试日志输出
  const testLogging = () => {
    setLogOutput("")

    // 重定向console输出到文本区域
    const originalConsole = { ...console }
    const logMessages: string[] = []

    console.log = (...args) => {
      originalConsole.log(...args)
      logMessages.push(
        args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg))).join(" "),
      )
      setLogOutput(logMessages.join("\n"))
    }

    console.debug = (...args) => {
      originalConsole.debug(...args)
      logMessages.push(
        `[DEBUG] ${args
          .map((arg) => (typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)))
          .join(" ")}`,
      )
      setLogOutput(logMessages.join("\n"))
    }

    console.info = (...args) => {
      originalConsole.info(...args)
      logMessages.push(
        `[INFO] ${args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg))).join(" ")}`,
      )
      setLogOutput(logMessages.join("\n"))
    }

    console.warn = (...args) => {
      originalConsole.warn(...args)
      logMessages.push(
        `[WARN] ${args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg))).join(" ")}`,
      )
      setLogOutput(logMessages.join("\n"))
    }

    console.error = (...args) => {
      originalConsole.error(...args)
      logMessages.push(
        `[ERROR] ${args
          .map((arg) => (typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)))
          .join(" ")}`,
      )
      setLogOutput(logMessages.join("\n"))
    }

    // 测试各种日志
    apiLogger.debug("API调试日志测试", { endpoint: "/api/test", method: "GET" })
    apiLogger.info("API信息日志测试", { status: 200, responseTime: "120ms" })
    apiLogger.warn("API警告日志测试", { retries: 3, warning: "响应时间过长" })
    apiLogger.error("API错误日志测试", { status: 500, error: "服务器内部错误" })

    cryptoLogger.debug("加密调试日志测试", { algorithm: "AES-CBC", keyLength: 32 })
    cryptoLogger.info("加密信息日志测试", { operation: "加密完成" })

    taskLogger.debug("任务调试日志测试", { taskId: "task-123", status: "processing" })
    callLogger.info("通话信息日志测试", { callId: "call-456", duration: 120 })
    monitorLogger.warn("监控警告日志测试", { metric: "CPU使用率", value: "85%", threshold: "80%" })

    // 恢复原始console
    setTimeout(() => {
      console.log = originalConsole.log
      console.debug = originalConsole.debug
      console.info = originalConsole.info
      console.warn = originalConsole.warn
      console.error = originalConsole.error
    }, 1000)
  }

  // 测试加密
  const testEncrypt = () => {
    try {
      if (!encryptInput) {
        toast({
          title: "请输入要加密的内容",
          variant: "destructive",
        })
        return
      }

      const encrypted = aesEncrypt(encryptInput)
      setEncryptOutput(encrypted)

      toast({
        title: "加密成功",
        description: "已将加密结果显示在输出框中",
      })
    } catch (error) {
      console.error("加密失败:", error)
      toast({
        title: "加密失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    }
  }

  // 测试解密
  const testDecrypt = () => {
    try {
      if (!decryptInput) {
        toast({
          title: "请输入要解密的内容",
          variant: "destructive",
        })
        return
      }

      const decrypted = aesDecrypt(decryptInput)
      setDecryptOutput(decrypted)

      toast({
        title: "解密成功",
        description: "已将解密结果显示在输出框中",
      })
    } catch (error) {
      console.error("解密失败:", error)
      toast({
        title: "解密失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">调试控制面板</h1>

      <Tabs defaultValue="settings">
        <TabsList>
          <TabsTrigger value="settings">调试设置</TabsTrigger>
          <TabsTrigger value="logging">日志测试</TabsTrigger>
          <TabsTrigger value="crypto">加密工具</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>调试类别设置</CardTitle>
              <CardDescription>选择要启用的调试类别，设置将保存在浏览器中</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Switch
                      id={`category-${category.id}`}
                      checked={isCategoryEnabled(category.id)}
                      onCheckedChange={(checked) => toggleCategory(category.id, checked)}
                    />
                    <Label htmlFor={`category-${category.id}`}>{category.label}</Label>
                  </div>
                ))}
              </div>

              <div className="space-y-2">
                <Label htmlFor="debug-value">当前DEBUG值</Label>
                <div className="flex space-x-2">
                  <Textarea
                    id="debug-value"
                    value={debugValue}
                    onChange={(e) => setDebugValue(e.target.value)}
                    placeholder="输入DEBUG值，多个类别用逗号分隔"
                    className="flex-1"
                  />
                  <Button onClick={() => updateDebugSettings(debugValue)}>应用</Button>
                </div>
                <p className="text-sm text-muted-foreground">提示: 使用&quot;*&quot;启用所有调试输出，使用逗号分隔多个类别</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" onClick={() => updateDebugSettings("")}>
                清除所有调试设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>环境变量状态</CardTitle>
              <CardDescription>当前环境变量配置状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">DEBUG</span>
                  <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">已配置</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  DEBUG环境变量用于控制服务器端的调试输出。当前值将在服务器端生效。
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>日志测试</CardTitle>
              <CardDescription>测试各种日志类别和级别的输出</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={testLogging}>生成测试日志</Button>

              <div className="space-y-2">
                <Label htmlFor="log-output">日志输出</Label>
                <Textarea id="log-output" value={logOutput} readOnly className="h-[300px] font-mono text-sm" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="crypto" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>加密测试</CardTitle>
              <CardDescription>测试AES加密功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="encrypt-input">输入明文</Label>
                <Textarea
                  id="encrypt-input"
                  value={encryptInput}
                  onChange={(e) => setEncryptInput(e.target.value)}
                  placeholder="输入要加密的文本"
                  className="h-[100px]"
                />
              </div>

              <Button onClick={testEncrypt}>加密</Button>

              <div className="space-y-2">
                <Label htmlFor="encrypt-output">加密结果</Label>
                <Textarea id="encrypt-output" value={encryptOutput} readOnly className="h-[100px] font-mono text-sm" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>解密测试</CardTitle>
              <CardDescription>测试AES解密功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="decrypt-input">输入密文</Label>
                <Textarea
                  id="decrypt-input"
                  value={decryptInput}
                  onChange={(e) => setDecryptInput(e.target.value)}
                  placeholder="输入要解密的Base64文本"
                  className="h-[100px]"
                />
              </div>

              <Button onClick={testDecrypt}>解密</Button>

              <div className="space-y-2">
                <Label htmlFor="decrypt-output">解密结果</Label>
                <Textarea id="decrypt-output" value={decryptOutput} readOnly className="h-[100px] font-mono text-sm" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

