import logger from '@/lib/utils/logger';

// 测试 area-data 库的数据结构
const { pca, pcaa } = require('area-data');

logger.log('pca 数据结构:', Object.keys(pca).length);
logger.log('pcaa 数据结构:', Object.keys(pcaa).length);

// 打印所有省份
logger.log('所有省份:', pca['86']);

// 打印北京市的区县
logger.log('北京市的区县:', pcaa['110000']);

// 打印北京市辖区的区县
logger.log('北京市辖区的区县:', pcaa['110100']);

// 打印所有数据结构
logger.log('pca 结构:', JSON.stringify(pca).substring(0, 200) + '...');
logger.log('pcaa 结构:', JSON.stringify(pcaa).substring(0, 200) + '...');
