/**
 * 中国省市区数据
 * 简化版，包含主要省份和城市
 */

export interface Region {
  value: string;
  label: string;
  children?: Region[];
}

export const provinces: Region[] = [
  {
    value: "北京市",
    label: "北京市",
    children: [
      {
        value: "北京市",
        label: "北京市",
        children: [
          { value: "东城区", label: "东城区" },
          { value: "西城区", label: "西城区" },
          { value: "朝阳区", label: "朝阳区" },
          { value: "海淀区", label: "海淀区" },
          { value: "丰台区", label: "丰台区" },
          { value: "石景山区", label: "石景山区" },
          { value: "门头沟区", label: "门头沟区" },
          { value: "房山区", label: "房山区" },
          { value: "通州区", label: "通州区" },
          { value: "顺义区", label: "顺义区" },
          { value: "昌平区", label: "昌平区" },
          { value: "大兴区", label: "大兴区" },
          { value: "怀柔区", label: "怀柔区" },
          { value: "平谷区", label: "平谷区" },
          { value: "密云区", label: "密云区" },
          { value: "延庆区", label: "延庆区" }
        ]
      }
    ]
  },
  {
    value: "上海市",
    label: "上海市",
    children: [
      {
        value: "上海市",
        label: "上海市",
        children: [
          { value: "黄浦区", label: "黄浦区" },
          { value: "徐汇区", label: "徐汇区" },
          { value: "长宁区", label: "长宁区" },
          { value: "静安区", label: "静安区" },
          { value: "普陀区", label: "普陀区" },
          { value: "虹口区", label: "虹口区" },
          { value: "杨浦区", label: "杨浦区" },
          { value: "闵行区", label: "闵行区" },
          { value: "宝山区", label: "宝山区" },
          { value: "嘉定区", label: "嘉定区" },
          { value: "浦东新区", label: "浦东新区" },
          { value: "金山区", label: "金山区" },
          { value: "松江区", label: "松江区" },
          { value: "青浦区", label: "青浦区" },
          { value: "奉贤区", label: "奉贤区" },
          { value: "崇明区", label: "崇明区" }
        ]
      }
    ]
  },
  {
    value: "广东省",
    label: "广东省",
    children: [
      {
        value: "广州市",
        label: "广州市",
        children: [
          { value: "越秀区", label: "越秀区" },
          { value: "荔湾区", label: "荔湾区" },
          { value: "海珠区", label: "海珠区" },
          { value: "天河区", label: "天河区" },
          { value: "白云区", label: "白云区" },
          { value: "黄埔区", label: "黄埔区" },
          { value: "番禺区", label: "番禺区" },
          { value: "花都区", label: "花都区" },
          { value: "南沙区", label: "南沙区" },
          { value: "从化区", label: "从化区" },
          { value: "增城区", label: "增城区" }
        ]
      },
      {
        value: "深圳市",
        label: "深圳市",
        children: [
          { value: "福田区", label: "福田区" },
          { value: "罗湖区", label: "罗湖区" },
          { value: "南山区", label: "南山区" },
          { value: "宝安区", label: "宝安区" },
          { value: "龙岗区", label: "龙岗区" },
          { value: "盐田区", label: "盐田区" },
          { value: "龙华区", label: "龙华区" },
          { value: "坪山区", label: "坪山区" },
          { value: "光明区", label: "光明区" }
        ]
      },
      {
        value: "珠海市",
        label: "珠海市",
        children: [
          { value: "香洲区", label: "香洲区" },
          { value: "斗门区", label: "斗门区" },
          { value: "金湾区", label: "金湾区" }
        ]
      },
      {
        value: "汕头市",
        label: "汕头市",
        children: [
          { value: "龙湖区", label: "龙湖区" },
          { value: "金平区", label: "金平区" },
          { value: "濠江区", label: "濠江区" },
          { value: "潮阳区", label: "潮阳区" },
          { value: "潮南区", label: "潮南区" },
          { value: "澄海区", label: "澄海区" },
          { value: "南澳县", label: "南澳县" }
        ]
      }
    ]
  },
  {
    value: "江苏省",
    label: "江苏省",
    children: [
      {
        value: "南京市",
        label: "南京市",
        children: [
          { value: "玄武区", label: "玄武区" },
          { value: "秦淮区", label: "秦淮区" },
          { value: "建邺区", label: "建邺区" },
          { value: "鼓楼区", label: "鼓楼区" },
          { value: "浦口区", label: "浦口区" },
          { value: "栖霞区", label: "栖霞区" },
          { value: "雨花台区", label: "雨花台区" },
          { value: "江宁区", label: "江宁区" },
          { value: "六合区", label: "六合区" },
          { value: "溧水区", label: "溧水区" },
          { value: "高淳区", label: "高淳区" }
        ]
      },
      {
        value: "苏州市",
        label: "苏州市",
        children: [
          { value: "姑苏区", label: "姑苏区" },
          { value: "虎丘区", label: "虎丘区" },
          { value: "吴中区", label: "吴中区" },
          { value: "相城区", label: "相城区" },
          { value: "吴江区", label: "吴江区" },
          { value: "常熟市", label: "常熟市" },
          { value: "张家港市", label: "张家港市" },
          { value: "昆山市", label: "昆山市" },
          { value: "太仓市", label: "太仓市" }
        ]
      }
    ]
  },
  {
    value: "浙江省",
    label: "浙江省",
    children: [
      {
        value: "杭州市",
        label: "杭州市",
        children: [
          { value: "上城区", label: "上城区" },
          { value: "下城区", label: "下城区" },
          { value: "江干区", label: "江干区" },
          { value: "拱墅区", label: "拱墅区" },
          { value: "西湖区", label: "西湖区" },
          { value: "滨江区", label: "滨江区" },
          { value: "萧山区", label: "萧山区" },
          { value: "余杭区", label: "余杭区" },
          { value: "富阳区", label: "富阳区" },
          { value: "临安区", label: "临安区" },
          { value: "桐庐县", label: "桐庐县" },
          { value: "淳安县", label: "淳安县" },
          { value: "建德市", label: "建德市" }
        ]
      },
      {
        value: "宁波市",
        label: "宁波市",
        children: [
          { value: "海曙区", label: "海曙区" },
          { value: "江北区", label: "江北区" },
          { value: "北仑区", label: "北仑区" },
          { value: "镇海区", label: "镇海区" },
          { value: "鄞州区", label: "鄞州区" },
          { value: "奉化区", label: "奉化区" },
          { value: "象山县", label: "象山县" },
          { value: "宁海县", label: "宁海县" },
          { value: "余姚市", label: "余姚市" },
          { value: "慈溪市", label: "慈溪市" }
        ]
      }
    ]
  },
  {
    value: "四川省",
    label: "四川省",
    children: [
      {
        value: "成都市",
        label: "成都市",
        children: [
          { value: "锦江区", label: "锦江区" },
          { value: "青羊区", label: "青羊区" },
          { value: "金牛区", label: "金牛区" },
          { value: "武侯区", label: "武侯区" },
          { value: "成华区", label: "成华区" },
          { value: "龙泉驿区", label: "龙泉驿区" },
          { value: "青白江区", label: "青白江区" },
          { value: "新都区", label: "新都区" },
          { value: "温江区", label: "温江区" },
          { value: "双流区", label: "双流区" },
          { value: "郫都区", label: "郫都区" }
        ]
      }
    ]
  },
  {
    value: "河南省",
    label: "河南省",
    children: [
      {
        value: "郑州市",
        label: "郑州市",
        children: [
          { value: "中原区", label: "中原区" },
          { value: "二七区", label: "二七区" },
          { value: "管城回族区", label: "管城回族区" },
          { value: "金水区", label: "金水区" },
          { value: "上街区", label: "上街区" },
          { value: "惠济区", label: "惠济区" },
          { value: "中牟县", label: "中牟县" },
          { value: "巩义市", label: "巩义市" },
          { value: "荥阳市", label: "荥阳市" },
          { value: "新密市", label: "新密市" },
          { value: "新郑市", label: "新郑市" },
          { value: "登封市", label: "登封市" }
        ]
      }
    ]
  },
  {
    value: "湖北省",
    label: "湖北省",
    children: [
      {
        value: "武汉市",
        label: "武汉市",
        children: [
          { value: "江岸区", label: "江岸区" },
          { value: "江汉区", label: "江汉区" },
          { value: "硚口区", label: "硚口区" },
          { value: "汉阳区", label: "汉阳区" },
          { value: "武昌区", label: "武昌区" },
          { value: "青山区", label: "青山区" },
          { value: "洪山区", label: "洪山区" },
          { value: "东西湖区", label: "东西湖区" },
          { value: "汉南区", label: "汉南区" },
          { value: "蔡甸区", label: "蔡甸区" },
          { value: "江夏区", label: "江夏区" },
          { value: "黄陂区", label: "黄陂区" },
          { value: "新洲区", label: "新洲区" }
        ]
      }
    ]
  },
  {
    value: "湖南省",
    label: "湖南省",
    children: [
      {
        value: "长沙市",
        label: "长沙市",
        children: [
          { value: "芙蓉区", label: "芙蓉区" },
          { value: "天心区", label: "天心区" },
          { value: "岳麓区", label: "岳麓区" },
          { value: "开福区", label: "开福区" },
          { value: "雨花区", label: "雨花区" },
          { value: "望城区", label: "望城区" },
          { value: "长沙县", label: "长沙县" },
          { value: "浏阳市", label: "浏阳市" },
          { value: "宁乡市", label: "宁乡市" }
        ]
      }
    ]
  },
  {
    value: "山东省",
    label: "山东省",
    children: [
      {
        value: "济南市",
        label: "济南市",
        children: [
          { value: "历下区", label: "历下区" },
          { value: "市中区", label: "市中区" },
          { value: "槐荫区", label: "槐荫区" },
          { value: "天桥区", label: "天桥区" },
          { value: "历城区", label: "历城区" },
          { value: "长清区", label: "长清区" },
          { value: "章丘区", label: "章丘区" },
          { value: "济阳区", label: "济阳区" },
          { value: "莱芜区", label: "莱芜区" },
          { value: "钢城区", label: "钢城区" },
          { value: "平阴县", label: "平阴县" },
          { value: "商河县", label: "商河县" }
        ]
      },
      {
        value: "青岛市",
        label: "青岛市",
        children: [
          { value: "市南区", label: "市南区" },
          { value: "市北区", label: "市北区" },
          { value: "黄岛区", label: "黄岛区" },
          { value: "崂山区", label: "崂山区" },
          { value: "李沧区", label: "李沧区" },
          { value: "城阳区", label: "城阳区" },
          { value: "即墨区", label: "即墨区" },
          { value: "胶州市", label: "胶州市" },
          { value: "平度市", label: "平度市" },
          { value: "莱西市", label: "莱西市" }
        ]
      }
    ]
  }
];

/**
 * 根据省份获取城市列表
 * @param province 省份
 * @returns 城市列表
 */
export function getCitiesByProvince(province: string): Region[] {
  const provinceData = provinces.find(p => p.value === province);
  return provinceData?.children || [];
}

/**
 * 根据省份和城市获取区县列表
 * @param province 省份
 * @param city 城市
 * @returns 区县列表
 */
export function getDistrictsByCity(province: string, city: string): Region[] {
  const provinceData = provinces.find(p => p.value === province);
  const cityData = provinceData?.children?.find(c => c.value === city);
  return cityData?.children || [];
}
