/**
 * 用户信息安全处理工具
 * 用于处理用户信息，确保敏感信息不会泄露
 */

import logger from './logger';

/**
 * 敏感字段列表
 */
const SENSITIVE_FIELDS = [
  'email',
  'password',
  'token',
  'secret',
  'key',
  'auth',
  'credentials',
];

/**
 * 清理用户信息，移除敏感字段
 * @param user 用户信息对象
 * @returns 清理后的用户信息对象
 */
export function sanitizeUserInfo(user: any): any {
  if (!user) {
    return null;
  }

  // 创建一个新对象，避免修改原对象
  const sanitizedUser: any = { ...user };

  // 移除敏感字段
  SENSITIVE_FIELDS.forEach(field => {
    if (field in sanitizedUser) {
      delete sanitizedUser[field];
    }
  });

  // 特殊处理邮箱地址，只保留部分信息
  if (sanitizedUser.email) {
    sanitizedUser.email = maskEmail(sanitizedUser.email);
  }

  return sanitizedUser;
}

/**
 * 掩码邮箱地址
 * 例如：<EMAIL> -> u***@e***.com
 * @param email 邮箱地址
 * @returns 掩码后的邮箱地址
 */
export function maskEmail(email: string): string {
  if (!email || !email.includes('@')) {
    return email;
  }

  const [username, domain] = email.split('@');
  const domainParts = domain.split('.');
  const tld = domainParts.pop(); // 顶级域名
  const domainName = domainParts.join('.');

  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 1);
  const maskedDomain = domainName.charAt(0) + '*'.repeat(domainName.length - 1);

  return `${maskedUsername}@${maskedDomain}.${tld}`;
}

/**
 * 安全地记录用户操作日志
 * @param action 操作名称
 * @param user 用户信息
 * @param details 操作详情
 */
export function logUserAction(action: string, user: any, details?: any): void {
  // 清理用户信息
  const sanitizedUser = sanitizeUserInfo(user);
  
  // 清理操作详情
  const sanitizedDetails = details ? { ...details } : {};
  
  // 移除详情中的敏感字段
  SENSITIVE_FIELDS.forEach(field => {
    if (field in sanitizedDetails) {
      delete sanitizedDetails[field];
    }
  });
  
  // 记录操作日志
  logger.log(`用户操作: ${action}`, {
    user: sanitizedUser,
    details: sanitizedDetails,
    timestamp: new Date().toISOString(),
  });
}

/**
 * 用户信息安全处理工具对象
 */
const userInfoSanitizer = {
  sanitizeUserInfo,
  maskEmail,
  logUserAction,
};

export default userInfoSanitizer;
