/**
 * 对话框工具函数
 * 替代原生的 alert/confirm/prompt 函数
 */
import { toast } from "react-hot-toast";

/**
 * 显示提示信息
 * 替代原生的 alert 函数
 * @param message 提示信息
 */
export function showMessage(message: string): void {
  if (typeof window !== 'undefined') {
    toast(message, {
      duration: 3000,
    });
  }
}

/**
 * 显示成功信息
 * @param message 成功信息
 */
export function showSuccess(message: string): void {
  if (typeof window !== 'undefined') {
    toast.success(message, {
      duration: 3000,
    });
  }
}

/**
 * 显示错误信息
 * 替代原生的 alert 函数显示错误
 * @param message 错误信息
 */
export function showError(message: string): void {
  if (typeof window !== 'undefined') {
    toast.error(message, {
      duration: 5000,
    });
  }
}

/**
 * 对话框工具对象
 */
const dialog = {
  showMessage,
  showSuccess,
  showError,
};

export default dialog;
