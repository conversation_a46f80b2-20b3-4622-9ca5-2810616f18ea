import { z } from 'zod'

// 用户相关验证
export const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
  remember: z.boolean().optional(),
})

export const registerSchema = z.object({
  username: z
    .string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z.string().email('邮箱格式不正确'),
  password: z
    .string()
    .min(8, '密码至少8个字符')
    .regex(/[A-Z]/, '密码必须包含大写字母')
    .regex(/[a-z]/, '密码必须包含小写字母')
    .regex(/[0-9]/, '密码必须包含数字'),
  verificationCode: z
    .string()
    .length(6, '验证码必须是6位数字')
    .regex(/^\d+$/, '验证码必须是数字'),
})

// 任务相关验证
export const createTaskSchema = z.object({
  name: z.string().min(1, '任务名称不能为空'),
  content: z.string().min(1, '任务内容不能为空'),
  callType: z.string().min(1, '通话类型不能为空'),
  startTime: z.date().min(new Date(), '开始时间不能为空'),
  resource: z.string().min(1, '资源不能为空'),
  priority: z.number().min(1).max(5).default(3),
  tags: z.array(z.string()).optional(),
  assignees: z.array(z.string()).optional(),
})

export const updateTaskSchema = z.object({
  status: z.enum(['未开始', '进行中', '已完成', '已取消']),
  progress: z.number().min(0).max(100),
  priority: z.number().min(1).max(5),
  tags: z.array(z.string()).optional(),
  assignees: z.array(z.string()).optional(),
})

// 视频通话相关验证
export const createVideoCallSchema = z.object({
  title: z.string().min(1, '通话主题不能为空'),
  description: z.string().optional(),
  participants: z.array(z.string())
    .min(2, '至少需要2个参与者')
    .max(50, '最多支持50个参与者'),
  startTime: z.date().min(new Date(), '开始时间不能为空'),
  duration: z.number()
    .min(5, '通话时长至少5分钟')
    .max(120, '通话时长最多120分钟')
    .default(30),
  recording: z.boolean().default(false),
}) 