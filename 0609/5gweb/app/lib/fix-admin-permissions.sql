-- 修复管理员角色权限脚本
-- 此脚本用于确保ADMIN角色拥有角色管理的所有权限
-- 使用方法: psql -d 数据库名 -f fix-admin-permissions.sql

-- 添加角色管理相关权限
UPDATE "role"
SET "permissions" = array_append("permissions", 'role:create')
WHERE "code" = 'ADMIN' AND NOT ('role:create' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'role:update')
WHERE "code" = 'ADMIN' AND NOT ('role:update' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'role:delete')
WHERE "code" = 'ADMIN' AND NOT ('role:delete' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'role:view')
WHERE "code" = 'ADMIN' AND NOT ('role:view' = ANY("permissions"));

-- 添加ABAC资源:操作格式的权限
UPDATE "role"
SET "permissions" = array_append("permissions", 'roles:create')
WHERE "code" = 'ADMIN' AND NOT ('roles:create' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'roles:update')
WHERE "code" = 'ADMIN' AND NOT ('roles:update' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'roles:delete')
WHERE "code" = 'ADMIN' AND NOT ('roles:delete' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'roles:view')
WHERE "code" = 'ADMIN' AND NOT ('roles:view' = ANY("permissions"));

-- 添加传统大写格式的权限
UPDATE "role"
SET "permissions" = array_append("permissions", 'ROLE_CREATE')
WHERE "code" = 'ADMIN' AND NOT ('ROLE_CREATE' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'ROLE_UPDATE')
WHERE "code" = 'ADMIN' AND NOT ('ROLE_UPDATE' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'ROLE_DELETE')
WHERE "code" = 'ADMIN' AND NOT ('ROLE_DELETE' = ANY("permissions"));

UPDATE "role"
SET "permissions" = array_append("permissions", 'ROLE_VIEW')
WHERE "code" = 'ADMIN' AND NOT ('ROLE_VIEW' = ANY("permissions"));

-- 添加ROLE_MANAGE权限
UPDATE "role"
SET "permissions" = array_append("permissions", 'ROLE_MANAGE')
WHERE "code" = 'ADMIN' AND NOT ('ROLE_MANAGE' = ANY("permissions"));

-- 确保超级管理员有通配符权限
UPDATE "role"
SET "permissions" = array_append("permissions", '*')
WHERE "code" = 'ADMIN' AND NOT ('*' = ANY("permissions"));

-- 输出结果
SELECT code, permissions
FROM "role"
WHERE code = 'ADMIN'; 