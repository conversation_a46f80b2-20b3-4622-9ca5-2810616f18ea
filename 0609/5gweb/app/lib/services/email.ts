import nodemailer from 'nodemailer'

/**
 * 邮件服务配置
 */
const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.qq.com',
  port: parseInt(process.env.EMAIL_PORT || '465'),
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
}

/**
 * 邮件服务
 */
class EmailService {
  private transporter: nodemailer.Transporter

  constructor() {
    try {
      this.transporter = nodemailer.createTransport(emailConfig)
      this.verifyConnection().then(isValid => {
        if (isValid) {
          console.log('邮件服务初始化成功')
        } else {
          console.error('邮件服务验证失败')
        }
      })
    } catch (error) {
      console.error('邮件服务初始化失败:', error)
      throw new Error('邮件服务初始化失败')
    }
  }

  /**
   * 发送验证码邮件
   * @param to - 收件人邮箱
   * @param code - 验证码
   * @param type - 验证码类型（register=注册验证，reset=重置密码）
   */
  async sendVerificationEmail(to: string, code: string, type: 'register' | 'reset' = 'register'): Promise<void> {
    const subject = type === 'register' ? '注册验证码' : '重置密码验证码'
    const title = type === 'register' ? '欢迎注册' : '重置密码'
    const purpose = type === 'register' ? '完成注册' : '重置密码'
    const footer = type === 'register'
      ? '如果您没有注册账号，请忽略此邮件。'
      : '如果您没有申请重置密码，请忽略此邮件并确保您的账号安全。'

    const mailOptions = {
      from: `"系统通知" <${emailConfig.auth.user}>`,
      to,
      subject,
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">${title}</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.5;">您的验证码是：</p>
          <div style="background: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #333;">${code}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为 5 分钟，请尽快${purpose}。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">${footer}</p>
        </div>
      `
    }

    try {
      await this.transporter.sendMail(mailOptions)
      console.log(`${subject}邮件发送成功: ${to}`)
    } catch (error) {
      console.error(`${subject}邮件发送失败:`, error)
      throw new Error('邮件发送失败，请稍后重试')
    }
  }

  /**
   * 发送邮箱修改验证码邮件
   * @param to - 收件人邮箱
   * @param code - 验证码
   */
  async sendEmailChangeVerification(to: string, code: string): Promise<void> {
    const subject = '邮箱修改验证码'
    const title = '邮箱修改验证'

    const mailOptions = {
      from: `"系统通知" <${emailConfig.auth.user}>`,
      to,
      subject,
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">${title}</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.5;">您的邮箱修改验证码是：</p>
          <div style="background: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #333;">${code}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为 5 分钟，请尽快完成邮箱修改。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">如果您没有申请修改邮箱，请忽略此邮件并确保您的账号安全。</p>
        </div>
      `
    }

    try {
      await this.transporter.sendMail(mailOptions)
      console.log(`${subject}邮件发送成功: ${to}`)
    } catch (error) {
      console.error(`${subject}邮件发送失败:`, error)
      throw new Error('邮件发送失败，请稍后重试')
    }
  }

  /**
   * 验证邮件服务是否可用
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify()
      return true
    } catch (error) {
      console.error('邮件服务连接验证失败:', error)
      return false
    }
  }
}

// 导出邮件服务实例
export const emailService = new EmailService()