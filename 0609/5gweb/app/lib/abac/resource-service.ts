import { prismaClient } from "@/lib/prisma";
import { Cache } from "./cache";

export interface Resource {
  id: string;
  code: string;
  name: string;
  type: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  operations?: Array<{
    id: string;
    code: string;
    name: string;
    type: string;
  }>;
  roles?: Array<{
    id: string;
    code: string;
    name: string;
    type: string;
  }>;
  policies?: Array<{
    id: string;
    resourceCode: string;
    operationCode: string;
  }>;
}

export interface ResourceFormData {
  code: string;
  name: string;
  type: string;
  description?: string;
  operationIds?: string[];
}

/**
 * 资源服务类
 * 用于管理ABAC资源
 */
export class ResourceService {
  private cache: Cache<string, Resource>;

  constructor() {
    // 创建资源缓存，设置5分钟过期
    this.cache = new Cache<string, Resource>(300);
  }

  /**
   * 创建资源
   * @param data 资源数据
   * @returns 创建的资源
   */
  async createResource(data: ResourceFormData): Promise<Resource> {
    const { code, name, type, description, operationIds } = data;

    // 验证资源代码是否已存在
    const existingResource = await prismaClient.$queryRaw<Resource[]>`
      SELECT * FROM "Resource" WHERE code = ${code}
    `;

    if (existingResource.length > 0) {
      throw new Error("资源代码已存在");
    }

    // 创建资源
    const resources = await prismaClient.$queryRaw<Resource[]>`
      INSERT INTO "Resource" (id, code, name, type, description, "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${code}, ${name}, ${type}, ${description}, NOW(), NOW())
      RETURNING *
    `;

    const resource = resources[0];

    // 如果有操作ID，创建关联
    if (operationIds?.length) {
      await prismaClient.$executeRaw`
        INSERT INTO "_ResourceOperations" ("A", "B")
        SELECT ${resource.id}, unnest(array[${operationIds.join(',')}]::text[])
      `;
    }

    // 更新缓存
    this.cache.set(code, resource);

    return resource;
  }

  /**
   * 更新资源
   * @param id 资源ID
   * @param data 资源数据
   * @returns 更新后的资源
   */
  async updateResource(id: string, data: ResourceFormData): Promise<Resource> {
    const { code, name, type, description, operationIds } = data;

    // 验证资源是否存在
    const existingResources = await prismaClient.$queryRaw<Resource[]>`
      SELECT * FROM "Resource" WHERE id = ${id}
    `;

    if (existingResources.length === 0) {
      throw new Error("资源不存在");
    }

    const existingResource = existingResources[0];

    // 验证资源代码是否已存在
    if (code !== existingResource.code) {
      const codeExists = await prismaClient.$queryRaw<Resource[]>`
        SELECT * FROM "Resource" WHERE code = ${code}
      `;

      if (codeExists.length > 0) {
        throw new Error("资源代码已存在");
      }
    }

    // 更新资源
    const resources = await prismaClient.$queryRaw<Resource[]>`
      UPDATE "Resource"
      SET code = ${code},
          name = ${name},
          type = ${type},
          description = ${description},
          "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    const resource = resources[0];

    // 如果有操作ID，更新关联
    if (operationIds !== undefined) {
      // 先删除所有关联
      await prismaClient.$executeRaw`
        DELETE FROM "_ResourceOperations"
        WHERE "B" = ${id}
      `;

      // 如果有新的操作ID，创建新的关联
      if (operationIds.length > 0) {
        await prismaClient.$executeRaw`
          INSERT INTO "_ResourceOperations" ("A", "B")
          SELECT ${resource.id}, unnest(array[${operationIds.join(',')}]::text[])
        `;
      }
    }

    // 更新缓存
    this.cache.set(code, resource);
    if (code !== existingResource.code) {
      this.cache.delete(existingResource.code);
    }

    return resource;
  }

  /**
   * 删除资源
   * @param id 资源ID
   */
  async deleteResource(id: string): Promise<void> {
    // 验证资源是否存在
    const existingResources = await prismaClient.$queryRaw<Resource[]>`
      SELECT * FROM "Resource" WHERE id = ${id}
    `;

    if (existingResources.length === 0) {
      throw new Error("资源不存在");
    }

    const existingResource = existingResources[0];

    // 删除资源
    await prismaClient.$queryRaw`
      DELETE FROM "Resource" WHERE id = ${id}
    `;

    // 从缓存中删除
    this.cache.delete(existingResource.code);
  }

  /**
   * 获取资源
   * @param code 资源代码
   * @returns 资源
   */
  async getResource(code: string): Promise<Resource | null> {
    // 尝试从缓存获取
    const cachedResource = this.cache.get(code);
    if (cachedResource) {
      return cachedResource;
    }

    // 从数据库获取
    const resources = await prismaClient.$queryRaw<Resource[]>`
      SELECT r.*, 
             array_to_json(array_agg(DISTINCT o.*)) as operations,
             array_to_json(array_agg(DISTINCT rl.*)) as roles,
             array_to_json(array_agg(DISTINCT p.*)) as policies
      FROM "Resource" r
      LEFT JOIN "_ResourceOperations" ro ON ro."B" = r.id
      LEFT JOIN "Operation" o ON o.id = ro."A"
      LEFT JOIN "_RoleResources" rr ON rr."B" = r.id
      LEFT JOIN "Role" rl ON rl.id = rr."A"
      LEFT JOIN "Policy" p ON p."resourceCode" = r.code
      WHERE r.code = ${code}
      GROUP BY r.id
    `;

    const resource = resources[0] || null;

    // 更新缓存
    if (resource) {
      this.cache.set(code, resource);
    }

    return resource;
  }

  /**
   * 获取所有资源
   * @returns 资源列表
   */
  async getResources(): Promise<Resource[]> {
    return await prismaClient.$queryRaw<Resource[]>`
      SELECT r.*, 
             array_to_json(array_agg(DISTINCT o.*)) as operations,
             array_to_json(array_agg(DISTINCT rl.*)) as roles,
             array_to_json(array_agg(DISTINCT p.*)) as policies
      FROM "Resource" r
      LEFT JOIN "_ResourceOperations" ro ON ro."B" = r.id
      LEFT JOIN "Operation" o ON o.id = ro."A"
      LEFT JOIN "_RoleResources" rr ON rr."B" = r.id
      LEFT JOIN "Role" rl ON rl.id = rr."A"
      LEFT JOIN "Policy" p ON p."resourceCode" = r.code
      GROUP BY r.id
    `;
  }

  /**
   * 检查资源是否存在
   * @param code 资源代码
   * @returns 是否存在
   */
  async resourceExists(code: string): Promise<boolean> {
    const resource = await this.getResource(code);
    return resource !== null;
  }

  /**
   * 获取资源的操作列表
   * @param code 资源代码
   * @returns 操作列表
   */
  async getResourceOperations(code: string): Promise<string[]> {
    const resource = await this.getResource(code);
    if (!resource) {
      throw new Error("资源不存在");
    }

    return resource.operations?.map(op => op.code) || [];
  }

  /**
   * 获取资源的角色列表
   * @param code 资源代码
   * @returns 角色列表
   */
  async getResourceRoles(code: string): Promise<string[]> {
    const resource = await this.getResource(code);
    if (!resource) {
      throw new Error("资源不存在");
    }

    return resource.roles?.map(role => role.code) || [];
  }

  /**
   * 获取指定类型的资源列表
   * @param type 资源类型
   * @returns 资源列表
   */
  async getResourcesByType(type: string): Promise<Resource[]> {
    return await prismaClient.$queryRaw<Resource[]>`
      SELECT r.*, 
             array_to_json(array_agg(DISTINCT o.*)) as operations,
             array_to_json(array_agg(DISTINCT rl.*)) as roles,
             array_to_json(array_agg(DISTINCT p.*)) as policies
      FROM "Resource" r
      LEFT JOIN "_ResourceOperations" ro ON ro."B" = r.id
      LEFT JOIN "Operation" o ON o.id = ro."A"
      LEFT JOIN "_RoleResources" rr ON rr."B" = r.id
      LEFT JOIN "Role" rl ON rl.id = rr."A"
      LEFT JOIN "Policy" p ON p."resourceCode" = r.code
      WHERE r.type = ${type}
      GROUP BY r.id
    `;
  }

  /**
   * 获取指定操作的资源列表
   * @param operationId 操作ID
   * @returns 资源列表
   */
  async getResourcesByOperation(operationId: string): Promise<Resource[]> {
    return await prismaClient.$queryRaw<Resource[]>`
      SELECT r.*, 
             array_to_json(array_agg(DISTINCT o.*)) as operations,
             array_to_json(array_agg(DISTINCT rl.*)) as roles,
             array_to_json(array_agg(DISTINCT p.*)) as policies
      FROM "Resource" r
      INNER JOIN "_ResourceOperations" ro ON ro."B" = r.id
      LEFT JOIN "Operation" o ON o.id = ro."A"
      LEFT JOIN "_RoleResources" rr ON rr."B" = r.id
      LEFT JOIN "Role" rl ON rl.id = rr."A"
      LEFT JOIN "Policy" p ON p."resourceCode" = r.code
      WHERE ro."A" = ${operationId}
      GROUP BY r.id
    `;
  }

  /**
   * 获取指定角色的资源列表
   * @param roleId 角色ID
   * @returns 资源列表
   */
  async getResourcesByRole(roleId: string): Promise<Resource[]> {
    return await prismaClient.$queryRaw<Resource[]>`
      SELECT r.*, 
             array_to_json(array_agg(DISTINCT o.*)) as operations,
             array_to_json(array_agg(DISTINCT rl.*)) as roles,
             array_to_json(array_agg(DISTINCT p.*)) as policies
      FROM "Resource" r
      INNER JOIN "_RoleResources" rr ON rr."B" = r.id
      LEFT JOIN "_ResourceOperations" ro ON ro."B" = r.id
      LEFT JOIN "Operation" o ON o.id = ro."A"
      LEFT JOIN "Role" rl ON rl.id = rr."A"
      LEFT JOIN "Policy" p ON p."resourceCode" = r.code
      WHERE rr."A" = ${roleId}
      GROUP BY r.id
    `;
  }
}