/**
 * 缓存项接口
 */
interface CacheItem<T> {
  value: T;
  expireAt: number;
}

/**
 * 缓存类
 * 用于缓存数据，支持过期时间
 */
export class Cache<K extends string, V> {
  private cache: Map<K, CacheItem<V>>;
  private ttl: number;

  /**
   * 构造函数
   * @param ttl 过期时间（秒）
   */
  constructor(ttl: number) {
    this.cache = new Map();
    this.ttl = ttl * 1000; // 转换为毫秒
  }

  /**
   * 设置缓存
   * @param key 键
   * @param value 值
   */
  set(key: K, value: V): void {
    this.cache.set(key, {
      value,
      expireAt: Date.now() + this.ttl,
    });
  }

  /**
   * 获取缓存
   * @param key 键
   * @returns 值或undefined
   */
  get(key: K): V | undefined {
    const item = this.cache.get(key);
    if (!item) {
      return undefined;
    }

    // 检查是否过期
    if (Date.now() > item.expireAt) {
      this.cache.delete(key);
      return undefined;
    }

    return item.value;
  }

  /**
   * 删除缓存
   * @param key 键
   */
  delete(key: K): void {
    this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireAt) {
        this.cache.delete(key);
      }
    }
  }
}