import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { hasResourcePermission } from "./permission";
import { PermissionService } from "../permission-service";
import logger from '@/lib/utils/logger';

/**
 * 创建ABAC中间件
 * @param resourceCode 资源代码
 * @param operationCode 操作代码
 * @returns 中间件函数
 */
export function createAbacMiddleware(
  resourceCode: string,
  operationCode: string
) {
  return function abacMiddleware(handler: any) {
    return async function(request: NextRequest, params?: any) {
      try {
        // 获取用户token
        const token = await getToken({ req: request });
        if (!token || !token.sub) {
          logger.log(`[ABAC中间件] 未获取到有效token，拒绝访问`);
          return NextResponse.json({ error: "未授权" }, { status: 401 });
        }

        // 使用统一权限服务检查权限
        const hasPermission = await PermissionService.checkResourcePermission(
          token,
          resourceCode,
          operationCode
        );

        if (!hasPermission) {
          logger.log(`[ABAC中间件] 用户(${token.sub})没有所需权限(${resourceCode}:${operationCode})，拒绝访问`);
          return NextResponse.json({ 
            error: "无权访问", 
            resource: resourceCode,
            operation: operationCode
          }, { status: 403 });
        }

        // 权限检查通过，调用原始处理程序
        if (typeof handler === 'function') {
          return handler(request, params);
        }

        return NextResponse.next();
      } catch (error) {
        logger.error("ABAC中间件错误:", error);
        return NextResponse.json(
          { error: "权限检查失败" },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * 创建ABAC策略中间件
 * @param resourceCode 资源代码
 * @param operationCode 操作代码
 * @returns 中间件函数
 */
export function createPolicyMiddleware(
  resourceCode: string,
  operationCode: string
) {
  return function policyMiddleware(handler: any) {
    return async function(request: NextRequest, params?: any) {
      try {
        // 获取用户token
        const token = await getToken({ req: request });
        if (!token || !token.sub) {
          return NextResponse.json({ error: "未授权" }, { status: 401 });
        }

        // 使用统一权限服务检查权限
        const hasPermission = await PermissionService.checkResourcePermission(
          token,
          resourceCode,
          operationCode
        );

        if (!hasPermission) {
          return NextResponse.json({ error: "无权访问" }, { status: 403 });
        }

        // 权限检查通过，调用原始处理程序
        if (typeof handler === 'function') {
          return handler(request, params);
        }

        return NextResponse.next();
      } catch (error) {
        logger.error("策略中间件错误:", error);
        return NextResponse.json(
          { error: "权限检查失败" },
          { status: 500 }
        );
      }
    };
  };
}