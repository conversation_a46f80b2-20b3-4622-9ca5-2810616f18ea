import { prismaClient } from '@/lib/prisma'

export interface Operation {
  id: string
  code: string
  name: string
  type: string
  description?: string
  menuId?: string
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * ABAC操作服务
 */
export class OperationService {
  /**
   * 获取所有操作
   * @returns 操作列表
   */
  async getAllOperations(): Promise<Operation[]> {
    const result = await prismaClient.$queryRaw<Operation[]>`
      SELECT * FROM "Operation"
    `
    return result
  }

  /**
   * 获取操作
   * @param id 操作ID
   * @returns 操作
   */
  async getOperation(id: string): Promise<Operation | null> {
    const operations = await prismaClient.$queryRaw<Operation[]>`
      SELECT * FROM "Operation" WHERE id = ${id}
    `
    return operations[0] || null
  }

  /**
   * 创建操作
   * @param data 操作数据
   * @returns 操作
   */
  async createOperation(data: {
    code: string
    name: string
    type: string
    description?: string
    menuId?: string
  }): Promise<Operation> {
    const { code, name, type, description, menuId } = data
    const operations = await prismaClient.$queryRaw<Operation[]>`
      INSERT INTO "Operation" (id, code, name, type, description, "menuId", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${code}, ${name}, ${type}, ${description}, ${menuId}, NOW(), NOW())
      RETURNING *
    `
    return operations[0]
  }

  /**
   * 更新操作
   * @param id 操作ID
   * @param data 操作数据
   * @returns 操作
   */
  async updateOperation(id: string, data: {
    code?: string
    name?: string
    type?: string
    description?: string
    menuId?: string
  }): Promise<Operation> {
    const setClause = Object.entries(data)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `"${key}" = ${value}`)
      .join(', ')

    const operations = await prismaClient.$queryRaw<Operation[]>`
      UPDATE "Operation"
      SET ${prismaClient.$executeRaw`${setClause}`}, "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    `
    return operations[0]
  }

  /**
   * 删除操作
   * @param id 操作ID
   */
  async deleteOperation(id: string): Promise<void> {
    await prismaClient.$queryRaw`
      DELETE FROM "Operation" WHERE id = ${id}
    `
  }

  /**
   * 启用操作
   * @param id 操作ID
   * @returns 操作
   */
  async enableOperation(id: string): Promise<Operation> {
    const operations = await prismaClient.$queryRaw<Operation[]>`
      UPDATE "Operation"
      SET enabled = true, "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    `
    return operations[0]
  }

  /**
   * 禁用操作
   * @param id 操作ID
   * @returns 操作
   */
  async disableOperation(id: string): Promise<Operation> {
    const operations = await prismaClient.$queryRaw<Operation[]>`
      UPDATE "Operation"
      SET enabled = false, "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    `
    return operations[0]
  }

  /**
   * 检查操作是否存在
   * @param code 操作代码
   * @returns 是否存在
   */
  async hasOperation(code: string): Promise<boolean> {
    const result = await prismaClient.$queryRaw<{ count: number }[]>`
      SELECT COUNT(*) as count FROM "Operation" WHERE code = ${code}
    `
    return result[0].count > 0
  }

  /**
   * 获取操作列表
   * @param type 操作类型
   * @returns 操作列表
   */
  async getOperationsByType(type: string): Promise<Operation[]> {
    const result = await prismaClient.$queryRaw<Operation[]>`
      SELECT * FROM "Operation" WHERE type = ${type}
    `
    return result
  }

  /**
   * 获取操作列表
   * @param resourceId 资源ID
   * @returns 操作列表
   */
  async getOperationsByResource(resourceId: string): Promise<Operation[]> {
    const result = await prismaClient.$queryRaw<Operation[]>`
      SELECT o.* FROM "Operation" o
      INNER JOIN "_ResourceOperations" ro ON ro."A" = o.id
      WHERE ro."B" = ${resourceId}
    `
    return result
  }
}