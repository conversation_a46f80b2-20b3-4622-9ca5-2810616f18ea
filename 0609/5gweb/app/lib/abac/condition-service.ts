import { prismaClient } from "@/lib/prisma";
import { PolicyCondition, ConditionOperator } from "./types";

/**
 * 条件服务类
 * 用于管理和评估ABAC条件
 */
export class ConditionService {
  /**
   * 将 Prisma Condition 转换为 PolicyCondition
   */
  private toPolicyCondition(condition: any): PolicyCondition {
    return {
      id: condition.id,
      policyId: condition.policyId,
      attribute: condition.attribute,
      operator: condition.operator,
      value: condition.value,
      description: condition.description || undefined,
      createdAt: condition.createdAt,
      updatedAt: condition.updatedAt,
    };
  }

  /**
   * 创建条件
   * @param policyId 策略ID
   * @param data 条件数据
   * @returns 创建的条件
   */
  async createCondition(
    policyId: string,
    data: Omit<PolicyCondition, "id" | "policyId" | "createdAt" | "updatedAt">
  ): Promise<PolicyCondition> {
    const { attribute, operator, value, description } = data;

    // 验证策略是否存在
    const policy = await prismaClient.policy.findUnique({
      where: { id: policyId },
    });

    if (!policy) {
      throw new Error("策略不存在");
    }

    // 创建条件
    const condition = await prismaClient.condition.create({
      data: {
        policyId,
        attribute,
        operator,
        value,
        description: description || undefined,
      },
    });

    return this.toPolicyCondition(condition);
  }

  /**
   * 更新条件
   * @param id 条件ID
   * @param data 条件数据
   * @returns 更新后的条件
   */
  async updateCondition(
    id: string,
    data: Partial<Omit<PolicyCondition, "id" | "policyId" | "createdAt" | "updatedAt">>
  ): Promise<PolicyCondition> {
    // 验证条件是否存在
    const existingCondition = await prismaClient.condition.findUnique({
      where: { id },
    });

    if (!existingCondition) {
      throw new Error("条件不存在");
    }

    // 更新条件
    const condition = await prismaClient.condition.update({
      where: { id },
      data: {
        attribute: data.attribute,
        operator: data.operator,
        value: data.value,
        description: data.description,
      },
    });

    return this.toPolicyCondition(condition);
  }

  /**
   * 删除条件
   * @param id 条件ID
   */
  async deleteCondition(id: string): Promise<void> {
    // 验证条件是否存在
    const existingCondition = await prismaClient.condition.findUnique({
      where: { id },
    });

    if (!existingCondition) {
      throw new Error("条件不存在");
    }

    // 删除条件
    await prismaClient.condition.delete({
      where: { id },
    });
  }

  /**
   * 获取条件
   * @param id 条件ID
   * @returns 条件
   */
  async getCondition(id: string): Promise<PolicyCondition | null> {
    const condition = await prismaClient.condition.findUnique({
      where: { id },
    });

    return condition ? this.toPolicyCondition(condition) : null;
  }

  /**
   * 获取策略的所有条件
   * @param policyId 策略ID
   * @returns 条件列表
   */
  async getPolicyConditions(policyId: string): Promise<PolicyCondition[]> {
    const conditions = await prismaClient.condition.findMany({
      where: { policyId },
    });

    return conditions.map(this.toPolicyCondition);
  }

  /**
   * 评估条件
   * @param condition 条件
   * @param context 上下文数据
   * @returns 是否满足条件
   */
  evaluateCondition(
    condition: PolicyCondition,
    context: Record<string, any>
  ): boolean {
    const value = this.getAttributeValue(context, condition.attribute);
    
    switch (condition.operator as ConditionOperator) {
      case "equals":
        return value === condition.value;
      case "notEquals":
        return value !== condition.value;
      case "contains":
        return String(value).includes(String(condition.value));
      case "notContains":
        return !String(value).includes(String(condition.value));
      case "startsWith":
        return String(value).startsWith(String(condition.value));
      case "endsWith":
        return String(value).endsWith(String(condition.value));
      case "greaterThan":
        return Number(value) > Number(condition.value);
      case "lessThan":
        return Number(value) < Number(condition.value);
      case "greaterThanOrEquals":
        return Number(value) >= Number(condition.value);
      case "lessThanOrEquals":
        return Number(value) <= Number(condition.value);
      default:
        return false;
    }
  }

  /**
   * 获取属性值
   * @param context 上下文数据
   * @param attribute 属性路径
   * @returns 属性值
   */
  private getAttributeValue(
    context: Record<string, any>,
    attribute: string
  ): any {
    const parts = attribute.split(".");
    let value = context;

    for (const part of parts) {
      if (value === undefined || value === null) {
        return undefined;
      }
      value = value[part];
    }

    return value;
  }

  /**
   * 批量创建条件
   * @param policyId 策略ID
   * @param conditions 条件列表
   * @returns 创建的条件列表
   */
  async createConditions(
    policyId: string,
    conditions: Omit<PolicyCondition, "id" | "policyId" | "createdAt" | "updatedAt">[]
  ): Promise<PolicyCondition[]> {
    // 验证策略是否存在
    const policy = await prismaClient.policy.findUnique({
      where: { id: policyId },
    });

    if (!policy) {
      throw new Error("策略不存在");
    }

    // 创建条件
    const result = await prismaClient.condition.createMany({
      data: conditions.map(condition => ({
        policyId,
        attribute: condition.attribute,
        operator: condition.operator,
        value: condition.value,
        description: condition.description || undefined,
      })),
    });

    // 获取创建的条件
    const createdConditions = await prismaClient.condition.findMany({
      where: {
        policyId,
        createdAt: {
          gte: new Date(Date.now() - 1000), // 获取最近1秒创建的条件
        },
      },
      take: result.count,
    });

    return createdConditions.map(this.toPolicyCondition);
  }

  /**
   * 批量更新条件
   * @param conditions 条件列表
   * @returns 更新后的条件列表
   */
  async updateConditions(
    conditions: (PolicyCondition & { id: string })[]
  ): Promise<PolicyCondition[]> {
    // 批量更新条件
    await Promise.all(
      conditions.map(condition =>
        this.updateCondition(condition.id, condition)
      )
    );

    // 获取更新后的条件
    const updatedConditions = await prismaClient.condition.findMany({
      where: {
        id: {
          in: conditions.map(c => c.id),
        },
      },
    });

    return updatedConditions.map(this.toPolicyCondition);
  }

  /**
   * 批量删除条件
   * @param ids 条件ID列表
   */
  async deleteConditions(ids: string[]): Promise<void> {
    await prismaClient.condition.deleteMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
  }
}