/**
 * 权限检查错误基类
 */
export class PermissionError extends Error {
  constructor(message: string) {
    super(message)
    this.name = "PermissionError"
  }
}

/**
 * 未登录错误
 */
export class NotLoggedInError extends PermissionError {
  constructor() {
    super("用户未登录")
    this.name = "NotLoggedInError"
  }
}

/**
 * 角色权限错误
 */
export class RolePermissionError extends PermissionError {
  constructor(requiredRole: string) {
    super(`需要 ${requiredRole} 角色权限`)
    this.name = "RolePermissionError"
  }
}

/**
 * 权限拒绝错误
 */
export class PermissionDeniedError extends PermissionError {
  constructor(requiredPermissions: string[]) {
    super(`需要以下权限: ${requiredPermissions.join(", ")}`)
    this.name = "PermissionDeniedError"
  }
}

/**
 * 资源权限错误
 */
export class ResourcePermissionError extends PermissionError {
  constructor(resource: string, action: string) {
    super(`没有 ${resource} 资源的 ${action} 权限`)
    this.name = "ResourcePermissionError"
  }
}

/**
 * 路径权限错误
 */
export class PathPermissionError extends PermissionError {
  constructor(path: string) {
    super(`没有访问 ${path} 的权限`)
    this.name = "PathPermissionError"
  }
}

/**
 * 使用示例:
 * 
 * ```typescript
 * // 检查用户角色
 * if (!hasRole(user, "ADMIN")) {
 *   throw new RolePermissionError("ADMIN")
 * }
 * 
 * // 检查用户权限
 * if (!hasPermissions(user, ["user:create", "user:delete"])) {
 *   throw new PermissionDeniedError(["user:create", "user:delete"])
 * }
 * 
 * // 检查资源权限
 * if (!hasResourcePermission(user, "user", "edit")) {
 *   throw new ResourcePermissionError("user", "edit")
 * }
 * 
 * // 检查路径权限
 * if (!hasPathPermission(user, "/admin/users")) {
 *   throw new PathPermissionError("/admin/users")
 * }
 * ```
 */ 