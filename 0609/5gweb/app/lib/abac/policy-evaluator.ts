import { Policy, PolicyCondition, PolicyOperator } from "./types"

/**
 * 策略评估请求接口
 */
interface EvaluateRequest {
  user: {
    id: string
    role: {
      code: string
      permissions: Policy[]
    }
    [key: string]: any
  }
  resource: string
  action: string
  policies: Policy[]
}

/**
 * 策略评估结果接口
 */
interface EvaluateResult {
  allowed: boolean
  reason?: string
  policy?: Policy
}

/**
 * 策略评估器类
 * 用于评估用户是否有权限执行特定操作
 */
export class PolicyEvaluator {
  /**
   * 评估用户是否有权限执行特定操作
   * @param request 评估请求
   * @returns 评估结果
   */
  async evaluate(request: EvaluateRequest): Promise<EvaluateResult> {
    const { user, resource, action, policies } = request

    // 按优先级排序策略
    const sortedPolicies = policies
      .filter(policy => policy.enabled)
      .sort((a, b) => b.priority - a.priority)

    // 遍历策略
    for (const policy of sortedPolicies) {
      // 检查策略是否匹配
      if (this.matchesPolicy(policy, resource, action)) {
        // 检查条件
        const conditions = await this.evaluateConditions(policy, user)
        if (conditions) {
          return {
            allowed: policy.effect === "allow",
            reason: policy.description,
            policy
          }
        }
      }
    }

    // 默认拒绝
    return {
      allowed: false,
      reason: "没有匹配的权限策略",
    }
  }

  /**
   * 检查策略是否匹配
   * @param policy 策略
   * @param resource 资源标识
   * @param action 操作类型
   * @returns 是否匹配
   */
  private matchesPolicy(
    policy: Policy,
    resource: string,
    action: string
  ): boolean {
    // 检查资源匹配
    if (policy.resource !== "*" && !resource.startsWith(policy.resource)) {
      return false
    }

    // 检查操作匹配
    if (policy.action !== "*" && policy.action !== action) {
      return false
    }

    return true
  }

  /**
   * 评估策略条件
   * @param policy 策略
   * @param user 用户
   * @returns 是否满足条件
   */
  private async evaluateConditions(policy: Policy, user: any): Promise<boolean> {
    if (!policy.conditions) {
      return true
    }

    try {
      // 解析条件
      const conditions = JSON.parse(policy.conditions) as PolicyCondition[]

      // 评估所有条件
      for (const condition of conditions) {
        const result = await this.evaluateCondition(condition, user)
        if (!result) {
          return false
        }
      }

      return true
    } catch (error) {
      console.error("条件评估失败:", error)
      return false
    }
  }

  /**
   * 评估单个条件
   * @param condition 条件
   * @param user 用户
   * @returns 是否满足条件
   */
  private async evaluateCondition(
    condition: PolicyCondition,
    user: any
  ): Promise<boolean> {
    const { operator, value } = condition

    switch (operator) {
      case "equals":
        return user[condition.field] === value
      case "notEquals":
        return user[condition.field] !== value
      case "contains":
        return user[condition.field]?.includes(value)
      case "notContains":
        return !user[condition.field]?.includes(value)
      case "startsWith":
        return user[condition.field]?.startsWith(value)
      case "endsWith":
        return user[condition.field]?.endsWith(value)
      case "greaterThan":
        return user[condition.field] > value
      case "lessThan":
        return user[condition.field] < value
      default:
        return false
    }
  }
} 