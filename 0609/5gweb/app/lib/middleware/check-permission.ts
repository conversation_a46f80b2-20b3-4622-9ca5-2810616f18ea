import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

/**
 * JWT payload 类型定义
 */
interface JWTPayload {
  sub: string
  roles?: string[]
  permissions?: string[]
}

/**
 * 检查用户是否具有所需权限
 * @param request - Next.js 请求对象
 * @param requiredPermissions - 所需的权限列表
 * @returns 如果用户具有所需权限返回 true，否则返回 false
 * 
 * @example
 * // 检查用户是否具有读取任务的权限
 * const hasPermission = await checkPermission(request, ['TASK_READ'])
 * if (!hasPermission) {
 *   return NextResponse.json({ error: '没有权限访问此资源' }, { status: 403 })
 * }
 */
export async function checkPermission(
  request: NextRequest,
  requiredPermissions: string[]
) {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return false
    }

    // 管理员拥有所有权限
    if (token.roles?.includes('ADMIN')) {
      return true
    }

    // 检查用户是否具有所需的所有权限
    const userPermissions = token.permissions as string[]
    return requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    )
  } catch (error) {
    console.error('权限验证失败:', error)
    return false
  }
}

/**
 * 检查用户是否是资源所有者
 * @param request - Next.js 请求对象
 * @param resourceUserId - 资源所有者的用户 ID
 * @returns 如果用户是资源所有者或管理员返回 true，否则返回 false
 * 
 * @example
 * // 检查用户是否可以访问指定用户的信息
 * const hasPermission = await checkOwnership(request, userId)
 * if (!hasPermission) {
 *   return NextResponse.json({ error: '没有权限访问此资源' }, { status: 403 })
 * }
 */
export async function checkOwnership(
  request: NextRequest,
  resourceUserId: string
) {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return false
    }

    // 管理员可以访问所有资源
    if (token.roles?.includes('ADMIN')) {
      return true
    }

    // 普通用户只能访问自己的资源
    return token.sub === resourceUserId
  } catch (error) {
    console.error('所有权验证失败:', error)
    return false
  }
} 