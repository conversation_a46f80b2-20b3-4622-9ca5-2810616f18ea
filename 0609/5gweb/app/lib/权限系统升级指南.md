# 权限系统升级指南

本文档介绍了权限系统的升级内容、应用方法以及常见问题解决方案。

## 一、升级内容

本次升级主要解决以下问题：

1. 统一了 RBAC 和 ABAC 权限检查逻辑
2. 修复了角色创建时的权限检查问题
3. 支持多种权限格式 (`role:create`, `roles:create`, `ROLE_CREATE` 等)
4. 添加了权限检查 API 以便前端使用
5. 提供了权限修复脚本，确保 ADMIN 角色拥有必要权限

## 二、应用步骤

### 1. 部署更新文件

以下文件需要部署到对应位置：

- `app/lib/permission-service.ts` - 统一权限服务
- `app/lib/abac/middleware.ts` - 更新的 ABAC 中间件
- `app/api/roles/route.ts` - 修复的角色管理 API
- `app/api/permissions/check/route.ts` - 新增的权限检查 API

### 2. 应用数据库修复脚本

在部署前，应执行 SQL 修复脚本以确保权限正确配置：

```bash
# 连接到数据库
psql -d 你的数据库名称 -U 用户名

# 或者复制 SQL 内容直接在 psql 中执行
\i app/lib/fix-admin-permissions.sql
```

### 3. 验证权限配置

使用权限验证脚本检查权限是否正确配置：

```bash
node app/lib/check-permissions.js
```

## 三、架构说明

### 权限检查流程

本次升级后的权限检查流程如下：

1. 应用中间件接收请求
2. 中间件调用统一权限服务 `PermissionService`
3. `PermissionService` 按以下顺序检查权限：
   - 检查超级管理员通配符权限 (`*`)
   - 检查精确匹配权限 (如 `role:create`)
   - 检查替代格式权限 (如 `roles:create`)
   - 检查 ABAC 资源操作权限
   - 检查特殊角色管理权限 (如 `ROLE_CREATE`, `ROLE_MANAGE`)
4. 根据权限检查结果决定是否允许访问

### 权限格式说明

系统现在支持以下几种权限格式：

1. **资源:操作** 格式 (如 `role:create`, `menu:view`)
2. **多数资源:操作** 格式 (如 `roles:create`, `menus:view`)
3. **大写下划线** 格式 (如 `ROLE_CREATE`, `MENU_VIEW`)
4. **通配符** 权限 (`*` 表示拥有所有权限)

## 四、前端权限检查

前端可以使用新的权限检查 API 来验证用户权限：

```typescript
// 检查是否有创建角色的权限
async function checkCanCreateRole() {
  const response = await fetch('/api/permissions/check', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ permission: 'role:create' })
  });
  
  const data = await response.json();
  return data.allowed;
}

// 根据权限控制UI显示
const canCreateRole = await checkCanCreateRole();
if (canCreateRole) {
  // 显示创建角色按钮
}
```

## 五、故障排查

如果遇到权限问题，可以尝试以下步骤：

1. **检查角色权限**：确认用户角色是否有所需权限
   ```sql
   SELECT code, permissions FROM "role" WHERE code = '角色代码';
   ```

2. **检查用户权限**：确认用户是否有直接分配的权限
   ```sql
   SELECT username, permissions FROM "user" WHERE username = '用户名';
   ```

3. **检查日志**：查看控制台权限检查日志，包含形如 `[权限检查]` 的日志

4. **手动添加权限**：如需手动添加权限，可执行
   ```sql
   UPDATE "role" 
   SET "permissions" = array_append("permissions", '权限标识')
   WHERE "code" = '角色代码';
   ```

## 六、未来规划

1. 权限管理界面优化
2. 权限审计日志完善
3. 基于用户组的权限分配
4. 更细粒度的数据权限控制

---

如有问题，请联系系统管理员或开发团队。 