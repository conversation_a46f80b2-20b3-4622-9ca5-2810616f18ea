import { cookies } from 'next/headers';
import { verify } from 'jsonwebtoken';
import logger from '@/lib/utils/logger';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface TokenPayload {
  id: string;
  username: string;
  roleCode: string;
  exp: number;
}

export async function getToken(): Promise<TokenPayload | null> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return null;
    }

    const payload = verify(token, JWT_SECRET) as any;

    return {
      id: payload.sub as string,
      username: payload.username as string,
      roleCode: payload.roleCode as string,
      exp: payload.exp as number
    };
  } catch (error) {
    logger.error('Token verification failed:', error);
    return null;
  }
}