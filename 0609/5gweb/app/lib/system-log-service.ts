/**
 * 系统日志服务
 * 提供记录系统操作日志的功能
 * 
 * 功能：
 * - 记录用户操作
 * - 记录系统事件
 * - 查询操作日志
 * 
 * @module SystemLogService
 */

import { prisma } from "@/lib/prisma"
import { headers } from "next/headers"

export interface LogOptions {
  userId: string
  action: string
  module: string
  resourceId?: string
  resourceType?: string
  details?: any
  ipAddress?: string
  userAgent?: string
}

/**
 * 系统日志服务类
 * 提供记录和查询系统操作日志的功能
 */
export class SystemLogService {
  /**
   * 记录操作日志
   * @param options 日志选项
   * @returns 创建的日志记录
   */
  static async log(options: LogOptions) {
    try {
      const headersList = headers()
      const ipAddress = options.ipAddress || headersList.get('x-forwarded-for') || 'unknown'
      const userAgent = options.userAgent || headersList.get('user-agent') || 'unknown'

      const log = await prisma.systemLog.create({
        data: {
          userId: options.userId,
          action: options.action,
          module: options.module,
          resourceId: options.resourceId,
          resourceType: options.resourceType,
          details: options.details,
          ipAddress,
          userAgent,
        }
      })

      return log
    } catch (error) {
      console.error("记录系统日志失败:", error)
      // 日志记录失败不应影响主要业务流程
      return null
    }
  }

  /**
   * 查询操作日志
   * @param options 查询选项
   * @returns 日志记录列表和总数
   */
  static async query(options: {
    userId?: string
    action?: string
    module?: string
    resourceId?: string
    resourceType?: string
    startDate?: Date
    endDate?: Date
    page?: number
    pageSize?: number
  }) {
    const {
      userId,
      action,
      module,
      resourceId,
      resourceType,
      startDate,
      endDate,
      page = 1,
      pageSize = 10
    } = options

    // 构建查询条件
    const where = {
      ...(userId ? { userId } : {}),
      ...(action ? { action } : {}),
      ...(module ? { module } : {}),
      ...(resourceId ? { resourceId } : {}),
      ...(resourceType ? { resourceType } : {}),
      ...(startDate || endDate ? {
        createdAt: {
          ...(startDate ? { gte: startDate } : {}),
          ...(endDate ? { lte: endDate } : {})
        }
      } : {})
    }

    // 查询总数
    const total = await prisma.systemLog.count({ where })

    // 查询日志记录
    const logs = await prisma.systemLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true,
            image: true,
            roleCode: true
          }
        }
      }
    })

    return {
      logs,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }

  /**
   * 清理过期日志
   * @param days 保留天数，默认30天
   * @returns 清理的日志数量
   */
  static async cleanup(days = 30) {
    const date = new Date()
    date.setDate(date.getDate() - days)

    const result = await prisma.systemLog.deleteMany({
      where: {
        createdAt: {
          lt: date
        }
      }
    })

    return result.count
  }
}
