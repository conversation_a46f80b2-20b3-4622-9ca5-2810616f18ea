import CryptoJS from "crypto-js"

// Configuration values from environment variables
const keyText = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_KEY || ""
const ivText = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_IV || ""

export class AesUtils {
  /**
   * AES-128-CBC 加密
   * @param plaintext 明文
   * @returns 加密后的十六进制字符串
   */
  static cbcEncrypt(plaintext: string): string {
    // 将文本形式的密钥和初始化向量转换为 WordArray
    const key = CryptoJS.enc.Utf8.parse(keyText)
    const iv = CryptoJS.enc.Utf8.parse(ivText)

    // 加密
    const encrypted = CryptoJS.AES.encrypt(plaintext, key, { iv }).ciphertext

    // 返回十六进制字符串
    return encrypted.toString()
  }

  /**
   * AES-128-CBC 解密
   * @param encryptStr 加密后的十六进制字符串
   * @returns 解密后的明文
   */
  static cbcDecrypt(encryptStr: string): string {
    // 解析十六进制字符串为 WordArray
    const encrypted = CryptoJS.enc.Hex.parse(encryptStr)
    const key = CryptoJS.enc.Utf8.parse(keyText)
    const iv = CryptoJS.enc.Utf8.parse(ivText)

    // 解密
    const decrypted = CryptoJS.AES.decrypt({ ciphertext: encrypted }, key, { iv })

    // 将解密后的明文转换为 UTF-8 字符串
    return decrypted.toString(CryptoJS.enc.Utf8)
  }

  /**
   * 生成签名
   * @returns 签名字符串
   */
  static generateSignature(orgCode: string): string {
    const timestamp = Math.floor(Date.now() / 1000)
    const plaintext = `${orgCode}${timestamp}`
    return this.cbcEncrypt(plaintext)
  }
}

export default AesUtils

