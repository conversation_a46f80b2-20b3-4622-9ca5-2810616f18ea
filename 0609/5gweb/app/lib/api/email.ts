import nodemailer from 'nodemailer'

/**
 * 邮件服务配置接口
 */
interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
  tls?: {
    rejectUnauthorized: boolean
  }
}

/**
 * 邮件选项接口
 */
interface MailOptions {
  from: string
  to: string
  subject: string
  html: string
}

/**
 * 默认邮件服务配置
 */
const defaultConfig: EmailConfig = {
  host: 'smtp.qq.com',
  port: 465,
  secure: true,
  auth: {
    user: '<EMAIL>',
    pass: 'vkssqikbptbabjad'
  },
  tls: {
    rejectUnauthorized: false
  }
}

/**
 * 邮件服务类
 * 提供邮件发送相关功能
 */
class EmailService {
  private transporter: nodemailer.Transporter
  
  /**
   * 构造函数
   * @param config - 邮件服务配置，可选，默认使用QQ邮箱配置
   */
  constructor(config: EmailConfig = defaultConfig) {
    this.transporter = nodemailer.createTransport(config)
  }

  /**
   * 发送验证码邮件
   * @param to - 收件人邮箱地址
   * @param code - 验证码
   * @returns Promise<void>
   * @throws 如果邮件发送失败，将抛出错误
   * 
   * @example
   * ```typescript
   * const emailService = new EmailService()
   * try {
   *   await emailService.sendVerificationEmail('<EMAIL>', '123456')
   *   console.log('验证码发送成功')
   * } catch (error) {
   *   console.error('验证码发送失败:', error)
   * }
   * ```
   */
  async sendVerificationEmail(to: string, code: string): Promise<void> {
    const mailOptions: MailOptions = {
      from: '"注册验证" <<EMAIL>>',
      to,
      subject: '注册验证码',
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">欢迎注册</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.5;">您的验证码是：</p>
          <div style="background: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #333;">${code}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为 5 分钟，请尽快完成注册。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">如果您没有注册账号，请忽略此邮件。</p>
        </div>
      `
    }

    try {
      await this.transporter.sendMail(mailOptions)
    } catch (error) {
      console.error('发送验证码邮件失败:', error)
      throw error
    }
  }

  /**
   * 发送自定义邮件
   * @param options - 邮件选项
   * @returns Promise<void>
   * @throws 如果邮件发送失败，将抛出错误
   * 
   * @example
   * ```typescript
   * const emailService = new EmailService()
   * try {
   *   await emailService.sendCustomEmail({
   *     from: '"系统通知" <<EMAIL>>',
   *     to: '<EMAIL>',
   *     subject: '重要通知',
   *     html: '<h1>您的账户已激活</h1>'
   *   })
   * } catch (error) {
   *   console.error('邮件发送失败:', error)
   * }
   * ```
   */
  async sendCustomEmail(options: MailOptions): Promise<void> {
    try {
      await this.transporter.sendMail(options)
    } catch (error) {
      console.error('发送自定义邮件失败:', error)
      throw error
    }
  }
}

// 导出默认的邮件服务实例
const emailService = new EmailService()
export default emailService

// 导出类型定义，以便其他模块使用
export type { EmailConfig, MailOptions } 