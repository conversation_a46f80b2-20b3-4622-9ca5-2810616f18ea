/**
 * API接口定义文件
 * 本文件集中定义了所有与后端交互的API接口
 *
 * 使用方法:
 * 1. 导入需要的API函数: import { login, getTasks } from "@/lib/api";
import logger from '@/lib/utils/logger';
 * 2. 在组件中调用API函数: const response = await login(username, password);
 * 3. 处理响应结果: if (response.success) { ... }
 */

// 导入API类型定义
import type {
  User,
  Customer,
  Admin,
  Task,
  VideoCallTask,
  LoginHistory,
  Notification,
  RechargeRecord,
  SystemSettings,
} from "./types"

// API基础URL，可以根据环境变量配置不同环境的API地址
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "/api"

/**
 * 通用请求方法
 * 封装了fetch API，统一处理请求和响应
 *
 * @param endpoint API端点路径，不包含基础URL
 * @param options 请求选项，包括方法、请求体、请求头等
 * @returns Promise<T> 返回请求结果
 * @throws Error 当请求失败时抛出错误
 */
async function fetchAPI<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`

  // 默认请求头设置
  const headers = {
    "Content-Type": "application/json",
    ...options.headers,
  }

  // 不再从 localStorage 获取 token
  // 而是依赖服务器端设置的 HttpOnly cookie 自动发送

  // 合并选项
  const fetchOptions: RequestInit = {
    ...options,
    headers,
  }

  try {
    const response = await fetch(url, fetchOptions)

    // 检查响应状态
    if (!response.ok) {
      // 尝试解析错误响应
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `请求失败: ${response.status} ${response.statusText}`)
    }

    // 解析响应数据
    return await response.json()
  } catch (error) {
    logger.error(`API请求错误 [${endpoint}]:`, error)
    throw error
  }
}

/**
 * 通用响应接口
 * 所有API响应都应遵循此格式
 *
 * @template T 响应数据类型
 * @property {number} code 状态码，200表示成功，其他值表示失败
 * @property {T} data 响应数据
 * @property {string} message 响应消息
 * @property {boolean} success 请求是否成功
 */
export interface ApiResponse<T> {
  code: number
  data: T
  message: string
  success: boolean
}

/**
 * 分页响应接口
 * 用于返回分页数据
 *
 * @template T 列表项类型
 * @property {T[]} list 数据列表
 * @property {number} total 总记录数
 * @property {number} page 当前页码
 * @property {number} pageSize 每页记录数
 * @property {number} totalPages 总页数
 */
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// ==================== 用户认证相关API ====================

/**
 * 用户登录接口
 * 验证用户凭据并返回认证令牌
 *
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise<ApiResponse<{token: string; user: User}>>} 包含认证令牌和用户信息的响应
 * @throws {Error} 当登录失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await login('admin', 'password123');
 *   if (response.success) {
 *     // 保存令牌和用户信息
 *     localStorage.setItem('auth_token', response.data.token);
 *     // 跳转到仪表盘
 *     router.push('/dashboard');
 *   }
 * } catch (error) {
 *   logger.error('登录失败:', error);
 * }
 * ```
 */
export async function login(username: string, password: string) {
  return fetchAPI<ApiResponse<{ token: string; user: User }>>("/auth/login", {
    method: "POST",
    body: JSON.stringify({ username, password }),
  })
}

/**
 * 用户登出接口
 * 使当前用户的认证令牌失效
 *
 * @returns {Promise<ApiResponse<null>>} 登出操作的响应
 * @throws {Error} 当登出失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   await logout();
 *   // 清除本地存储的认证信息
 *   localStorage.removeItem('auth_token');
 *   // 跳转到登录页
 *   router.push('/login');
 * } catch (error) {
 *   logger.error('登出失败:', error);
 * }
 * ```
 */
export async function logout() {
  return fetchAPI<ApiResponse<null>>("/auth/logout", {
    method: "POST",
  })
}

/**
 * 获取当前用户信息
 * 根据认证令牌获取当前登录用户的详细信息
 *
 * @returns {Promise<ApiResponse<User>>} 包含用户信息的响应
 * @throws {Error} 当获取用户信息失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getCurrentUser();
 *   if (response.success) {
 *     setUser(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取用户信息失败:', error);
 *   // 可能是令牌已过期，重定向到登录页
 *   router.push('/login');
 * }
 * ```
 */
export async function getCurrentUser() {
  return fetchAPI<ApiResponse<User>>("/auth/current-user", {
    method: "GET",
  })
}

/**
 * 重置密码
 * 向指定邮箱发送密码重置链接
 *
 * @param {string} email 用户邮箱
 * @returns {Promise<ApiResponse<null>>} 密码重置操作的响应
 * @throws {Error} 当重置密码请求失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await resetPassword('<EMAIL>');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "密码重置邮件已发送",
 *       description: "请检查您的邮箱",
 *       variant: "success"
 *     });
 *   }
 * } catch (error) {
 *   logger.error('重置密码失败:', error);
 * }
 * ```
 */
export async function resetPassword(email: string) {
  return fetchAPI<ApiResponse<null>>("/auth/reset-password", {
    method: "POST",
    body: JSON.stringify({ email }),
  })
}

// ==================== 客户账户相关API ====================

/**
 * 获取客户列表
 * 支持分页、搜索和状态筛选
 *
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {string} [params.search] 搜索关键词，可选
 * @param {string} [params.status] 客户状态筛选，可选
 * @returns {Promise<ApiResponse<PaginatedResponse<Customer>>>} 包含客户列表的分页响应
 * @throws {Error} 当获取客户列表失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getCustomers({
 *     page: 1,
 *     pageSize: 10,
 *     search: '张三',
 *     status: 'active'
 *   });
 *   if (response.success) {
 *     setCustomers(response.data.list);
 *     setTotalCustomers(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取客户列表失败:', error);
 * }
 * ```
 */
export async function getCustomers(params: {
  page: number
  pageSize: number
  search?: string
  status?: string
}) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<Customer>>>(`/customers?${queryParams.toString()}`, {
    method: "GET",
  })
}

/**
 * 获取客户详情
 * 根据客户ID获取客户的详细信息
 *
 * @param {string} id 客户ID
 * @returns {Promise<ApiResponse<Customer>>} 包含客户详情的响应
 * @throws {Error} 当获取客户详情失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getCustomerById('customer-123');
 *   if (response.success) {
 *     setCustomer(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取客户详情失败:', error);
 * }
 * ```
 */
export async function getCustomerById(id: string) {
  return fetchAPI<ApiResponse<Customer>>(`/customers/${id}`, {
    method: "GET",
  })
}

/**
 * 创建客户
 * 创建新的客户账户
 *
 * @param {Partial<Customer>} data 客户数据
 * @returns {Promise<ApiResponse<Customer>>} 包含新创建客户信息的响应
 * @throws {Error} 当创建客户失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await createCustomer({
 *     name: '张三企业',
 *     contactPerson: '张三',
 *     phone: '13800138000',
 *     email: '<EMAIL>',
 *     address: '北京市朝阳区',
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "创建成功",
 *       description: "客户账户已成功创建",
 *       variant: "success"
 *     });
 *     // 跳转到客户列表页
 *     router.push('/customers');
 *   }
 * } catch (error) {
 *   logger.error('创建客户失败:', error);
 * }
 * ```
 */
export async function createCustomer(data: Partial<Customer>) {
  return fetchAPI<ApiResponse<Customer>>("/customers", {
    method: "POST",
    body: JSON.stringify(data),
  })
}

/**
 * 更新客户信息
 * 根据客户ID更新客户信息
 *
 * @param {string} id 客户ID
 * @param {Partial<Customer>} data 更新数据
 * @returns {Promise<ApiResponse<Customer>>} 包含更新后客户信息的响应
 * @throws {Error} 当更新客户信息失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await updateCustomer('customer-123', {
 *     name: '张三科技有限公司',
 *     phone: '13900139000',
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "更新成功",
 *       description: "客户信息已成功更新",
 *       variant: "success"
 *     });
 *   }
 * } catch (error) {
 *   logger.error('更新客户信息失败:', error);
 * }
 * ```
 */
export async function updateCustomer(id: string, data: Partial<Customer>) {
  return fetchAPI<ApiResponse<Customer>>(`/customers/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  })
}

/**
 * 停用客户账户
 * 将客户账户状态设置为停用
 *
 * @param {string} id 客户ID
 * @param {string} reason 停用原因
 * @returns {Promise<ApiResponse<null>>} 停用操作的响应
 * @throws {Error} 当停用客户账户失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await disableCustomer('customer-123', '账户欠费');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "停用成功",
 *       description: "客户账户已成功停用",
 *       variant: "success"
 *     });
 *     // 刷新客户列表
 *     fetchCustomers();
 *   }
 * } catch (error) {
 *   logger.error('停用客户账户失败:', error);
 * }
 * ```
 */
export async function disableCustomer(id: string, reason: string) {
  return fetchAPI<ApiResponse<null>>(`/customers/${id}/disable`, {
    method: "POST",
    body: JSON.stringify({ reason }),
  })
}

/**
 * 为客户账户充值
 * 增加客户账户余额
 *
 * @param {string} id 客户ID
 * @param {number} amount 充值金额
 * @param {string} paymentMethod 支付方式
 * @param {string} [remarks] 备注信息，可选
 * @returns {Promise<ApiResponse<{balance: number}>>} 包含更新后余额的响应
 * @throws {Error} 当充值操作失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await rechargeCustomer('customer-123', 1000, 'bank', '季度预付款');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "充值成功",
 *       description: `已成功充值 ¥1000，当前余额: ¥${response.data.balance}`,
 *       variant: "success"
 *     });
 *   }
 * } catch (error) {
 *   logger.error('充值失败:', error);
 * }
 * ```
 */
export async function rechargeCustomer(id: string, amount: number, paymentMethod: string, remarks?: string) {
  return fetchAPI<ApiResponse<{ balance: number }>>(`/customers/${id}/recharge`, {
    method: "POST",
    body: JSON.stringify({ amount, paymentMethod, remarks }),
  })
}

/**
 * 获取客户充值记录
 * 查询客户的充值和消费记录
 *
 * @param {string} id 客户ID
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {string} [params.startDate] 开始日期，可选，格式：YYYY-MM-DD
 * @param {string} [params.endDate] 结束日期，可选，格式：YYYY-MM-DD
 * @param {string} [params.type] 记录类型，可选，如：'充值'、'消费'
 * @returns {Promise<ApiResponse<PaginatedResponse<RechargeRecord>>>} 包含充值记录的分页响应
 * @throws {Error} 当获取充值记录失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getCustomerRechargeHistory('customer-123', {
 *     page: 1,
 *     pageSize: 10,
 *     startDate: '2023-01-01',
 *     endDate: '2023-12-31',
 *     type: '充值'
 *   });
 *   if (response.success) {
 *     setRechargeRecords(response.data.list);
 *     setTotalRecords(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取充值记录失败:', error);
 * }
 * ```
 */
export async function getCustomerRechargeHistory(
  id: string,
  params: {
    page: number
    pageSize: number
    startDate?: string
    endDate?: string
    type?: string
  },
) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<RechargeRecord>>>(
    `/customers/${id}/recharge-history?${queryParams.toString()}`,
    {
      method: "GET",
    },
  )
}

// ==================== 管理员账户相关API ====================

/**
 * 获取管理员列表
 * 支持分页、搜索和角色筛选
 *
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {string} [params.search] 搜索关键词，可选
 * @param {string} [params.role] 管理员角色筛选，可选
 * @returns {Promise<ApiResponse<PaginatedResponse<Admin>>>} 包含管理员列表的分页响应
 * @throws {Error} 当获取管理员列表失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getAdmins({
 *     page: 1,
 *     pageSize: 10,
 *     search: '张',
 *     role: 'admin'
 *   });
 *   if (response.success) {
 *     setAdmins(response.data.list);
 *     setTotalAdmins(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取管理员列表失败:', error);
 * }
 * ```
 */
export async function getAdmins(params: {
  page: number
  pageSize: number
  search?: string
  role?: string
}) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<Admin>>>(`/admins?${queryParams.toString()}`, {
    method: "GET",
  })
}

/**
 * 获取管理员详情
 * 根据管理员ID获取管理员的详细信息
 *
 * @param {string} id 管理员ID
 * @returns {Promise<ApiResponse<Admin>>} 包含管理员详情的响应
 * @throws {Error} 当获取管理员详情失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getAdminById('admin-123');
 *   if (response.success) {
 *     setAdmin(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取管理员详情失败:', error);
 * }
 * ```
 */
export async function getAdminById(id: string) {
  return fetchAPI<ApiResponse<Admin>>(`/admins/${id}`, {
    method: "GET",
  })
}

/**
 * 创建管理员
 * 创建新的管理员账户
 *
 * @param {Partial<Admin>} data 管理员数据
 * @returns {Promise<ApiResponse<Admin>>} 包含新创建管理员信息的响应
 * @throws {Error} 当创建管理员失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await createAdmin({
 *     username: 'zhangsan',
 *     name: '张三',
 *     email: '<EMAIL>',
 *     phone: '13800138000',
 *     role: 'admin',
 *     permissions: ['user:read', 'user:write']
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "创建成功",
 *       description: "管理员账户已成功创建",
 *       variant: "success"
 *     });
 *     // 跳转到管理员列表页
 *     router.push('/admins');
 *   }
 * } catch (error) {
 *   logger.error('创建管理员失败:', error);
 * }
 * ```
 */
export async function createAdmin(data: Partial<Admin>) {
  return fetchAPI<ApiResponse<Admin>>("/admins", {
    method: "POST",
    body: JSON.stringify(data),
  })
}

/**
 * 更新管理员信息
 * 根据管理员ID更新管理员信息
 *
 * @param {string} id 管理员ID
 * @param {Partial<Admin>} data 更新数据
 * @returns {Promise<ApiResponse<Admin>>} 包含更新后管理员信息的响应
 * @throws {Error} 当更新管理员信息失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await updateAdmin('admin-123', {
 *     name: '张三(主管)',
 *     phone: '13900139000',
 *     role: 'super',
 *     permissions: ['user:read', 'user:write', 'user:delete']
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "更新成功",
 *       description: "管理员信息已成功更新",
 *       variant: "success"
 *     });
 *   }
 * } catch (error) {
 *   logger.error('更新管理员信息失败:', error);
 * }
 * ```
 */
export async function updateAdmin(id: string, data: Partial<Admin>) {
  return fetchAPI<ApiResponse<Admin>>(`/admins/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  })
}

// ==================== 任务相关API ====================

/**
 * 获取任务列表
 * 支持分页、搜索和多种筛选条件
 *
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {string} [params.search] 搜索关键词，可选
 * @param {string} [params.status] 任务状态筛选，可选
 * @param {string} [params.type] 任务类型筛选，可选
 * @param {string} [params.startDate] 开始日期筛选，可选，格式：YYYY-MM-DD
 * @param {string} [params.endDate] 结束日期筛选，可选，格式：YYYY-MM-DD
 * @returns {Promise<ApiResponse<PaginatedResponse<Task>>>} 包含任务列表的分页响应
 * @throws {Error} 当获取任务列表失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getTasks({
 *     page: 1,
 *     pageSize: 10,
 *     search: '促销',
 *     status: 'processing',
 *     type: 'video-call',
 *     startDate: '2023-01-01',
 *     endDate: '2023-12-31'
 *   });
 *   if (response.success) {
 *     setTasks(response.data.list);
 *     setTotalTasks(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取任务列表失败:', error);
 * }
 * ```
 */
export async function getTasks(params: {
  page: number
  pageSize: number
  search?: string
  status?: string
  type?: string
  startDate?: string
  endDate?: string
}) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<Task>>>(`/tasks?${queryParams.toString()}`, {
    method: "GET",
  })
}

/**
 * 获取任务详情
 * 根据任务ID获取任务的详细信息
 *
 * @param {string} id 任务ID
 * @returns {Promise<ApiResponse<Task>>} 包含任务详情的响应
 * @throws {Error} 当获取任务详情失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getTaskById('task-123');
 *   if (response.success) {
 *     setTask(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取任务详情失败:', error);
 * }
 * ```
 */
export async function getTaskById(id: string) {
  return fetchAPI<ApiResponse<Task>>(`/tasks/${id}`, {
    method: "GET",
  })
}

/**
 * 创建5G视频通话任务
 * 创建新的5G视频通话任务
 *
 * @param {Object} data 任务数据
 * @param {string} data.name 任务名称
 * @param {string} data.content 视频外呼内容
 * @param {string} data.callType 外呼类型
 * @param {string} data.startTime 开始时间，格式：YYYY-MM-DD HH:mm:ss
 * @param {string} data.resource 视频外呼资源
 * @param {string} data.phoneNumber 呼叫号码，多个号码用逗号分隔
 * @param {string} data.smsType 短信方式
 * @param {string} data.smsTemplate 短信模板
 * @returns {Promise<ApiResponse<VideoCallTask>>} 包含新创建任务信息的响应
 * @throws {Error} 当创建任务失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await createVideoCallTask({
 *     name: '汽车清洗促销活动',
 *     content: '汽车清洗',
 *     callType: '5G视频通知',
 *     startTime: '2023-12-01 09:00:00',
 *     resource: '0755本地中继',
 *     phoneNumber: '13800138000,13900139000',
 *     smsType: '文本短信',
 *     smsTemplate: '模板1'
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "创建成功",
 *       description: "5G视频通话任务已成功创建",
 *       variant: "success"
 *     });
 *     // 跳转到任务列表页
 *     router.push('/tasks');
 *   }
 * } catch (error) {
 *   logger.error('创建任务失败:', error);
 * }
 * ```
 */
export async function createVideoCallTask(data: {
  name: string
  content: string
  callType: string
  startTime: string
  resource: string
  phoneNumber: string
  smsType: string
  smsTemplate: string
}) {
  return fetchAPI<ApiResponse<VideoCallTask>>("/tasks/video-call", {
    method: "POST",
    body: JSON.stringify(data),
  })
}

/**
 * 更新任务状态
 * 修改任务的状态，如启动、暂停、完成等
 *
 * @param {string} id 任务ID
 * @param {string} status 新状态，如 'pending', 'processing', 'completed', 'failed'
 * @returns {Promise<ApiResponse<null>>} 状态更新操作的响应
 * @throws {Error} 当更新任务状态失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   // 启动任务
 *   const response = await updateTaskStatus('task-123', 'processing');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "启动成功",
 *       description: "任务已成功启动",
 *       variant: "success"
 *     });
 *     // 刷新任务列表
 *     fetchTasks();
 *   }
 * } catch (error) {
 *   logger.error('更新任务状态失败:', error);
 * }
 * ```
 */
export async function updateTaskStatus(id: string, status: string) {
  return fetchAPI<ApiResponse<null>>(`/tasks/${id}/status`, {
    method: "PUT",
    body: JSON.stringify({ status }),
  })
}

/**
 * 删除任务
 * 删除指定的任务
 *
 * @param {string} id 任务ID
 * @returns {Promise<ApiResponse<null>>} 删除操作的响应
 * @throws {Error} 当删除任务失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await deleteTask('task-123');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "删除成功",
 *       description: "任务已成功删除",
 *       variant: "success"
 *     });
 *     // 刷新任务列表
 *     fetchTasks();
 *   }
 * } catch (error) {
 *   logger.error('删除任务失败:', error);
 * }
 * ```
 */
export async function deleteTask(id: string) {
  return fetchAPI<ApiResponse<null>>(`/tasks/${id}`, {
    method: "DELETE",
  })
}

/**
 * 批量导入任务
 * 通过文件批量导入任务
 *
 * @param {File} file 任务文件，支持CSV、Excel格式
 * @param {Object} [options] 导入选项，可选
 * @param {string} [options.type] 任务类型，可选
 * @param {string} [options.resource] 视频外呼资源，可选
 * @returns {Promise<ApiResponse<{imported: number; failed: number}>>} 导入结果的响应
 * @throws {Error} 当导入任务失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const fileInput = document.getElementById('file-input') as HTMLInputElement;
 *   const file = fileInput.files?.[0];
 *   if (file) {
 *     const response = await importTasks(file, {
 *       type: '5G视频通知',
 *       resource: '0755本地中继'
 *     });
 *     if (response.success) {
 *       // 显示成功消息
 *       toast({
 *         title: "导入成功",
 *         description: `成功导入 ${response.data.imported} 条任务，失败 ${response.data.failed} 条`,
 *         variant: "success"
 *       });
 *       // 刷新任务列表
 *       fetchTasks();
 *     }
 *   }
 * } catch (error) {
 *   logger.error('导入任务失败:', error);
 * }
 * ```
 */
export async function importTasks(file: File, options?: { type?: string; resource?: string }) {
  const formData = new FormData()
  formData.append("file", file)

  if (options) {
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value)
      }
    })
  }

  return fetchAPI<ApiResponse<{ imported: number; failed: number }>>("/tasks/import", {
    method: "POST",
    body: formData,
    headers: {}, // 让浏览器自动设置Content-Type为multipart/form-data
  })
}

/**
 * 导出任务列表
 * 将任务列表导出为指定格式的文件
 *
 * @param {string} format 导出格式，支持 'csv', 'excel', 'pdf'
 * @param {Object} [filters] 筛选条件，可选
 * @returns {Promise<Blob>} 导出文件的二进制数据
 * @throws {Error} 当导出任务列表失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const blob = await exportTasks('excel', {
 *     search: '促销',
 *     status: 'completed',
 *     startDate: '2023-01-01',
 *     endDate: '2023-12-31'
 *   });
 *
 *   // 创建下载链接
 *   const url = URL.createObjectURL(blob);
 *   const link = document.createElement('a');
 *   link.href = url;
 *   link.download = `任务列表_${new Date().toISOString().slice(0, 10)}.xlsx`;
 *   document.body.appendChild(link);
 *   link.click();
 *   document.body.removeChild(link);
 * } catch (error) {
 *   logger.error('导出任务列表失败:', error);
 * }
 * ```
 */
export async function exportTasks(format: "csv" | "excel" | "pdf", filters?: any): Promise<Blob> {
  const queryParams = new URLSearchParams()
  queryParams.append("format", format)

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString())
      }
    })
  }

  const url = `${API_BASE_URL}/tasks/export?${queryParams.toString()}`

  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
    },
  })

  if (!response.ok) {
    throw new Error("导出任务列表失败")
  }

  return response.blob()
}

/**
 * 下载任务导入模板
 * 获取用于批量导入任务的模板文件
 *
 * @param {string} [type] 任务类型，可选
 * @returns {Promise<Blob>} 模板文件的二进制数据
 * @throws {Error} 当下载模板失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const blob = await downloadTaskTemplate('5G视频通知');
 *
 *   // 创建下载链接
 *   const url = URL.createObjectURL(blob);
 *   const link = document.createElement('a');
 *   link.href = url;
 *   link.download = '任务导入模板.xlsx';
 *   document.body.appendChild(link);
 *   link.click();
 *   document.body.removeChild(link);
 * } catch (error) {
 *   logger.error('下载模板失败:', error);
 * }
 * ```
 */
export async function downloadTaskTemplate(type?: string): Promise<Blob> {
  const queryParams = new URLSearchParams()
  if (type) {
    queryParams.append("type", type)
  }

  const url = `${API_BASE_URL}/tasks/template?${queryParams.toString()}`

  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
    },
  })

  if (!response.ok) {
    throw new Error("下载任务导入模板失败")
  }

  return response.blob()
}

// ==================== 用户设置相关API ====================

/**
 * 更新用户个人资料
 * 修改当前登录用户的个人资料
 *
 * @param {Partial<User>} data 用户资料数据
 * @returns {Promise<ApiResponse<User>>} 包含更新后用户信息的响应
 * @throws {Error} 当更新用户资料失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await updateUserProfile({
 *     name: '张三',
 *     email: '<EMAIL>',
 *     phone: '13800138000',
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "更新成功",
 *       description: "个人资料已成功更新",
 *       variant: "success"
 *     });
 *     // 更新本地用户信息
 *     setUser(response.data);
 *   }
 * } catch (error) {
 *   logger.error('更新个人资料失败:', error);
 * }
 * ```
 */
export async function updateUserProfile(data: Partial<User>) {
  return fetchAPI<ApiResponse<User>>("/user/profile", {
    method: "PUT",
    body: JSON.stringify(data),
  })
}

/**
 * 更新用户密码
 * 修改当前登录用户的密码
 *
 * @param {string} oldPassword 旧密码
 * @param {string} newPassword 新密码
 * @returns {Promise<ApiResponse<null>>} 密码更新操作的响应
 * @throws {Error} 当更新密码失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await updateUserPassword('oldPassword123', 'newPassword456');
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "更新成功",
 *       description: "密码已成功更新",
 *       variant: "success"
 *     });
 *     // 清空密码输入框
 *     setOldPassword('');
 *     setNewPassword('');
 *     setConfirmPassword('');
 *   }
 * } catch (error) {
 *   logger.error('更新密码失败:', error);
 * }
 * ```
 */
export async function updateUserPassword(oldPassword: string, newPassword: string) {
  return fetchAPI<ApiResponse<null>>("/user/password", {
    method: "PUT",
    body: JSON.stringify({ oldPassword, newPassword }),
  })
}

/**
 * 获取用户登录历史
 * 查询当前用户的登录历史记录
 *
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {string} [params.startDate] 开始日期，可选，格式：YYYY-MM-DD
 * @param {string} [params.endDate] 结束日期，可选，格式：YYYY-MM-DD
 * @param {string} [params.status] 登录状态筛选，可选，如：'success', 'failed'
 * @returns {Promise<ApiResponse<PaginatedResponse<LoginHistory>>>} 包含登录历史的分页响应
 * @throws {Error} 当获取登录历史失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getUserLoginHistory({
 *     page: 1,
 *     pageSize: 10,
 *     startDate: '2023-01-01',
 *     endDate: '2023-12-31',
 *     status: 'success'
 *   });
 *   if (response.success) {
 *     setLoginHistory(response.data.list);
 *     setTotalRecords(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取登录历史失败:', error);
 * }
 * ```
 */
export async function getUserLoginHistory(params: {
  page: number
  pageSize: number
  startDate?: string
  endDate?: string
  status?: string
}) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<LoginHistory>>>(`/user/login-history?${queryParams.toString()}`, {
    method: "GET",
  })
}

/**
 * 导出用户登录历史
 * 将用户登录历史导出为指定格式的文件
 *
 * @param {string} format 导出格式，支持 'csv', 'excel', 'pdf'
 * @param {Object} [filters] 筛选条件，可选
 * @returns {Promise<Blob>} 导出文件的二进制数据
 * @throws {Error} 当导出登录历史失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const blob = await exportLoginHistory('excel', {
 *     startDate: '2023-01-01',
 *     endDate: '2023-12-31',
 *     status: 'success'
 *   });
 *
 *   // 创建下载链接
 *   const url = URL.createObjectURL(blob);
 *   const link = document.createElement('a');
 *   link.href = url;
 *   link.download = `登录历史_${new Date().toISOString().slice(0, 10)}.xlsx`;
 *   document.body.appendChild(link);
 *   link.click();
 *   document.body.removeChild(link);
 * } catch (error) {
 *   logger.error('导出登录历史失败:', error);
 * }
 * ```
 */
export async function exportLoginHistory(format: "csv" | "excel" | "pdf", filters?: any): Promise<Blob> {
  const queryParams = new URLSearchParams()
  queryParams.append("format", format)

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString())
      }
    })
  }

  const url = `${API_BASE_URL}/user/login-history/export?${queryParams.toString()}`

  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
    },
  })

  if (!response.ok) {
    throw new Error("导出登录历史失败")
  }

  return response.blob()
}

// ==================== 通知中心相关API ====================

/**
 * 获取通知列表
 * 查询当前用户的通知列表
 *
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页记录数
 * @param {boolean} [params.read] 是否已读筛选，可选
 * @param {string} [params.type] 通知类型筛选，可选
 * @param {string} [params.startDate] 开始日期，可选，格式：YYYY-MM-DD
 * @param {string} [params.endDate] 结束日期，可选，格式：YYYY-MM-DD
 * @returns {Promise<ApiResponse<PaginatedResponse<Notification>>>} 包含通知列表的分页响应
 * @throws {Error} 当获取通知列表失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getNotifications({
 *     page: 1,
 *     pageSize: 10,
 *     read: false,
 *     type: 'system'
 *   });
 *   if (response.success) {
 *     setNotifications(response.data.list);
 *     setTotalNotifications(response.data.total);
 *   }
 * } catch (error) {
 *   logger.error('获取通知列表失败:', error);
 * }
 * ```
 */
export async function getNotifications(params: {
  page: number
  pageSize: number
  read?: boolean
  type?: string
  startDate?: string
  endDate?: string
}) {
  const queryParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return fetchAPI<ApiResponse<PaginatedResponse<Notification>>>(`/notifications?${queryParams.toString()}`, {
    method: "GET",
  })
}

/**
 * 获取未读通知数量
 * 获取当前用户的未读通知数量
 *
 * @returns {Promise<ApiResponse<{unreadCount: number}>>} 包含未读通知数量的响应
 * @throws {Error} 当获取未读通知数量失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getUnreadNotificationCount();
 *   if (response.success) {
 *     setUnreadCount(response.data.unreadCount);
 *   }
 * } catch (error) {
 *   logger.error('获取未读通知数量失败:', error);
 * }
 * ```
 */
export async function getUnreadNotificationCount() {
  return fetchAPI<ApiResponse<{ unreadCount: number }>>("/notifications/unread-count", {
    method: "GET",
  })
}

/**
 * 获取通知详情
 * 根据通知ID获取通知的详细信息
 *
 * @param {string} id 通知ID
 * @returns {Promise<ApiResponse<Notification>>} 包含通知详情的响应
 * @throws {Error} 当获取通知详情失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getNotificationById('notification-123');
 *   if (response.success) {
 *     setNotification(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取通知详情失败:', error);
 * }
 * ```
 */
export async function getNotificationById(id: string) {
  return fetchAPI<ApiResponse<Notification>>(`/notifications/${id}`, {
    method: "GET",
  })
}

/**
 * 标记通知为已读
 * 将指定通知标记为已读状态
 *
 * @param {string} id 通知ID
 * @returns {Promise<ApiResponse<null>>} 标记操作的响应
 * @throws {Error} 当标记通知为已读失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await markNotificationAsRead('notification-123');
 *   if (response.success) {
 *     // 更新通知列表中的已读状态
 *     setNotifications(notifications.map(notification =>
 *       notification.id === 'notification-123'
 *         ? { ...notification, read: true }
 *         : notification
 *     ));
 *   }
 * } catch (error) {
 *   logger.error('标记通知为已读失败:', error);
 * }
 * ```
 */
export async function markNotificationAsRead(id: string) {
  return fetchAPI<ApiResponse<null>>(`/notifications/${id}/read`, {
    method: "PUT",
  })
}

/**
 * 标记所有通知为已读
 * 将当前用户的所有未读通知标记为已读
 *
 * @returns {Promise<ApiResponse<{count: number}>>} 包含标记数量的响应
 * @throws {Error} 当标记所有通知为已读失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await markAllNotificationsAsRead();
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "标记成功",
 *       description: `已将 ${response.data.count} 条通知标记为已读`,
 *       variant: "success"
 *     });
 *     // 更新通知列表中的已读状态
 *     setNotifications(notifications.map(notification => ({ ...notification, read: true })));
 *   }
 * } catch (error) {
 *   logger.error('标记所有通知为已读失败:', error);
 * }
 * ```
 */
export async function markAllNotificationsAsRead() {
  return fetchAPI<ApiResponse<{ count: number }>>("/notifications/read-all", {
    method: "PUT",
  })
}

/**
 * 创建新通知
 * 创建新的系统通知（仅管理员可用）
 *
 * @param {Object} data 通知数据
 * @param {string} data.title 通知标题
 * @param {string} data.content 通知内容
 * @param {string} data.type 通知类型
 * @param {string[]} [data.recipients] 接收者ID列表，可选
 * @returns {Promise<ApiResponse<Notification>>} 包含新创建通知信息的响应
 * @throws {Error} 当创建通知失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await createNotification({
 *     title: '系统维护通知',
 *     content: '系统将于本周六凌晨2:00-4:00进行例行维护，期间系统将不可用，请提前做好工作安排。',
 *     type: 'system',
 *     recipients: ['all'] // 'all' 表示所有用户
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "创建成功",
 *       description: "通知已成功创建",
 *       variant: "success"
 *     });
 *     // 跳转到通知列表页
 *     router.push('/notifications/manage');
 *   }
 * } catch (error) {
 *   logger.error('创建通知失败:', error);
 * }
 * ```
 */
export async function createNotification(data: {
  title: string
  content: string
  type: string
  recipients?: string[]
}) {
  return fetchAPI<ApiResponse<Notification>>("/notifications", {
    method: "POST",
    body: JSON.stringify(data),
  })
}

// ==================== 系统设置相关API ====================

/**
 * 获取系统设置
 * 获取系统的全局设置（仅管理员可用）
 *
 * @returns {Promise<ApiResponse<SystemSettings>>} 包含系统设置的响应
 * @throws {Error} 当获取系统设置失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await getSystemSettings();
 *   if (response.success) {
 *     setSettings(response.data);
 *   }
 * } catch (error) {
 *   logger.error('获取系统设置失败:', error);
 * }
 * ```
 */
export async function getSystemSettings() {
  return fetchAPI<ApiResponse<SystemSettings>>("/settings", {
    method: "GET",
  })
}

/**
 * 更新系统设置
 * 修改系统的全局设置（仅管理员可用）
 *
 * @param {Partial<SystemSettings>} data 设置数据
 * @returns {Promise<ApiResponse<SystemSettings>>} 包含更新后系统设置的响应
 * @throws {Error} 当更新系统设置失败时抛出错误
 *
 * @example
 * ```typescript
 * try {
 *   const response = await updateSystemSettings({
 *     siteName: '5G视频外呼管理系统',
 *     theme: {
 *       primaryColor: '#3b82f6',
 *       mode: 'light'
 *     },
 *     features: {
 *       enableRegistration: false,
 *       enablePasswordReset: true,
 *       enableNotifications: true
 *     }
 *   });
 *   if (response.success) {
 *     // 显示成功消息
 *     toast({
 *       title: "更新成功",
 *       description: "系统设置已成功更新",
 *       variant: "success"
 *     });
 *   }
 * } catch (error) {
 *   logger.error('更新系统设置失败:', error);
 * }
 * ```
 */
export async function updateSystemSettings(data: Partial<SystemSettings>) {
  return fetchAPI<ApiResponse<SystemSettings>>("/settings", {
    method: "PUT",
    body: JSON.stringify(data),
  })
}

