/**
 * API类型定义文件
 * 定义了与API交互相关的所有类型
 */

// ==================== 用户相关类型 ====================

/**
 * 用户类型
 * 表示系统中的用户实体
 *
 * @property {string} id 用户唯一标识符
 * @property {string} username 用户名
 * @property {string} email 电子邮箱
 * @property {string} name 姓名
 * @property {string} [avatar] 头像URL，可选
 * @property {string} role 用户角色，如 'admin', 'customer'
 * @property {'active' | 'inactive'} status 用户状态
 * @property {string} createdAt 创建时间，ISO 8601格式
 * @property {string} [lastLogin] 最后登录时间，ISO 8601格式，可选
 */
export interface User {
  id: string
  username: string
  email: string
  name: string
  avatar?: string
  role: string
  status: "active" | "inactive"
  createdAt: string
  lastLogin?: string
}

/**
 * 用户登录请求类型
 * 用于登录API的请求参数
 *
 * @property {string} username 用户名
 * @property {string} password 密码
 */
export interface LoginRequest {
  username: string
  password: string
}

/**
 * 用户登录响应类型
 * 登录API的响应数据
 *
 * @property {string} token 认证令牌
 * @property {User} user 用户信息
 */
export interface LoginResponse {
  token: string
  user: User
}

// ==================== 客户相关类型 ====================

/**
 * 客户类型
 * 表示系统中的客户实体
 *
 * @property {string} id 客户唯一标识符
 * @property {string} name 客户名称
 * @property {string} contactPerson 联系人
 * @property {string} phone 联系电话
 * @property {string} email 电子邮箱
 * @property {string} address 地址
 * @property {number} balance 账户余额
 * @property {'active' | 'disabled'} status 客户状态
 * @property {string} createdAt 创建时间，ISO 8601格式
 * @property {string} updatedAt 更新时间，ISO 8601格式
 */
export interface Customer {
  id: string
  name: string
  contactPerson: string
  phone: string
  email: string
  address: string
  balance: number
  status: "active" | "disabled"
  createdAt: string
  updatedAt: string
}

/**
 * 充值记录类型
 * 表示客户账户的充值或消费记录
 *
 * @property {string} id 记录唯一标识符
 * @property {string} customerId 客户ID
 * @property {number} amount 金额，正数表示充值，负数表示消费
 * @property {number} beforeBalance 操作前余额
 * @property {number} afterBalance 操作后余额
 * @property {string} operatorId 操作人ID
 * @property {string} operatorName 操作人姓名
 * @property {string} remarks 备注信息
 * @property {string} createdAt 创建时间，ISO 8601格式
 */
export interface RechargeRecord {
  id: string
  customerId: string
  amount: number
  beforeBalance: number
  afterBalance: number
  operatorId: string
  operatorName: string
  remarks: string
  createdAt: string
}

// ==================== 管理员相关类型 ====================

/**
 * 管理员类型
 * 表示系统中的管理员实体
 *
 * @property {string} id 管理员唯一标识符
 * @property {string} username 用户名
 * @property {string} name 姓名
 * @property {string} email 电子邮箱
 * @property {string} phone 联系电话
 * @property {'super' | 'admin' | 'operator'} role 管理员角色
 * @property {'active' | 'inactive'} status 管理员状态
 * @property {string[]} permissions 权限列表
 * @property {string} createdAt 创建时间，ISO 8601格式
 * @property {string} [lastLogin] 最后登录时间，ISO 8601格式，可选
 */
export interface Admin {
  id: string
  username: string
  name: string
  email: string
  phone: string
  role: "super" | "admin" | "operator"
  status: "active" | "inactive"
  permissions: string[]
  createdAt: string
  lastLogin?: string
}

// ==================== 任务相关类型 ====================

/**
 * 任务类型
 * 表示系统中的任务实体
 *
 * @property {string} id 任务唯一标识符
 * @property {string} name 任务名称
 * @property {'video-call' | 'message' | 'other'} type 任务类型
 * @property {string} content 任务内容
 * @property {'pending' | 'processing' | 'completed' | 'failed'} status 任务状态
 * @property {string} createdBy 创建人ID
 * @property {string} createdAt 创建时间，ISO 8601格式
 * @property {string} [startTime] 开始时间，ISO 8601格式，可选
 * @property {string} [endTime] 结束时间，ISO 8601格式，可选
 * @property {any} details 任务详细信息，根据任务类型不同而不同
 */
export interface Task {
  id: string
  name: string
  type: "video-call" | "message" | "other"
  content: string
  status: "pending" | "processing" | "completed" | "failed"
  createdBy: string
  createdAt: string
  startTime?: string
  endTime?: string
  details: any
}

/**
 * 视频通话任务类型
 * 表示5G视频通话类型的任务
 *
 * @extends Task
 * @property {string} type 固定为 'video-call'
 * @property {Object} details 任务详细信息
 * @property {string} details.callType 外呼类型，如 '5G视频通知', '5G视频互动'
 * @property {string} details.resource 视频外呼资源
 * @property {string} details.phoneNumber 呼叫号码
 * @property {string} details.smsType 短信方式
 * @property {string} details.smsTemplate 短信模板
 */
export interface VideoCallTask extends Task {
  type: "video-call"
  details: {
    callType: string
    resource: string
    phoneNumber: string
    smsType: string
    smsTemplate: string
  }
}

/**
 * 任务统计类型
 * 表示任务的统计信息
 *
 * @property {number} total 总任务数
 * @property {number} pending 待处理任务数
 * @property {number} processing 处理中任务数
 * @property {number} completed 已完成任务数
 * @property {number} failed 失败任务数
 */
export interface TaskStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
}

// ==================== 登录历史相关类型 ====================

/**
 * 登录历史记录类型
 * 表示用户的登录历史
 *
 * @property {string} id 记录唯一标识符
 * @property {string} userId 用户ID
 * @property {string} username 用户名
 * @property {string} ip IP地址
 * @property {string} userAgent 用户代理信息
 * @property {string} location 登录位置
 * @property {'success' | 'failed'} status 登录状态
 * @property {string} timestamp 登录时间，ISO 8601格式
 */
export interface LoginHistory {
  id: string
  userId: string
  username: string
  ip: string
  userAgent: string
  location: string
  status: "success" | "failed"
  timestamp: string
}

// ==================== 通知相关类型 ====================

/**
 * 通知类型
 * 表示系统中的通知实体
 *
 * @property {string} id 通知唯一标识符
 * @property {string} title 通知标题
 * @property {string} content 通知内容
 * @property {string} type 通知类型，如 'system', 'task', 'security'
 * @property {'draft' | 'published'} status 通知状态
 * @property {string} createdBy 创建人ID
 * @property {string} createdAt 创建时间，ISO 8601格式
 * @property {string} [publishedAt] 发布时间，ISO 8601格式，可选
 * @property {string[]} [recipients] 接收者ID列表，可选
 */
export interface Notification {
  id: string
  title: string
  content: string
  type: string
  status: "draft" | "published"
  createdBy: string
  createdAt: string
  publishedAt?: string
  recipients?: string[]
}

/**
 * 用户通知类型
 * 表示发送给特定用户的通知
 *
 * @property {string} id 用户通知唯一标识符
 * @property {string} notificationId 通知ID
 * @property {string} userId 用户ID
 * @property {boolean} read 是否已读
 * @property {string} [readAt] 阅读时间，ISO 8601格式，可选
 * @property {string} createdAt 创建时间，ISO 8601格式
 */
export interface UserNotification {
  id: string
  notificationId: string
  userId: string
  read: boolean
  readAt?: string
  createdAt: string
}

/**
 * 通知类型定义
 * 表示系统中的通知类型配置
 *
 * @property {string} id 类型唯一标识符
 * @property {string} name 类型名称
 * @property {string} color 类型颜色
 * @property {string} icon 类型图标
 * @property {boolean} enabled 是否启用
 * @property {string} description 类型描述
 * @property {'low' | 'medium' | 'high'} priority 默认优先级
 * @property {string} [parentId] 父类型ID，可选
 */
export interface NotificationType {
  id: string
  name: string
  color: string
  icon: string
  enabled: boolean
  description: string
  priority: "low" | "medium" | "high"
  parentId?: string
}

// ==================== 系统设置相关类型 ====================

/**
 * 系统设置类型
 * 表示系统的全局设置
 *
 * @property {string} siteName 站点名称
 * @property {string} logo Logo URL
 * @property {string} [footerText] 页脚文本
 * @property {Object} theme 主题设置
 * @property {string} theme.primaryColor 主色调
 * @property {'light' | 'dark' | 'system'} theme.mode 主题模式
 * @property {Object} features 功能设置
 * @property {boolean} features.enableRegistration 是否启用注册
 * @property {boolean} features.enablePasswordReset 是否启用密码重置
 * @property {boolean} features.enableNotifications 是否启用通知
 * @property {Object} [security] 安全设置
 * @property {number} [security.passwordMinLength] 密码最小长度
 * @property {boolean} [security.requireSpecialChar] 是否要求特殊字符
 * @property {boolean} [security.requireNumber] 是否要求数字
 * @property {boolean} [security.requireUppercase] 是否要求大写字母
 * @property {number} [security.loginAttempts] 登录尝试次数
 * @property {number} [security.sessionTimeout] 会话超时时间(分钟)
 */
export interface SystemSettings {
  siteName: string
  logo: string
  footerText?: string
  theme: {
    primaryColor: string
    mode: "light" | "dark" | "system"
  }
  features: {
    enableRegistration: boolean
    enablePasswordReset: boolean
    enableNotifications: boolean
  }
  security?: {
    passwordMinLength?: number
    requireSpecialChar?: boolean
    requireNumber?: boolean
    requireUppercase?: boolean
    loginAttempts?: number
    sessionTimeout?: number
  }
}

/**
 * 系统菜单项类型
 * 表示系统导航菜单中的项目
 *
 * @property {string} id 菜单项唯一标识符
 * @property {string} name 菜单项名称
 * @property {string} path 菜单项路径
 * @property {string} [icon] 菜单项图标，可选
 * @property {string} [parentId] 父菜单项ID，可选
 * @property {number} order 排序顺序
 * @property {MenuItem[]} [children] 子菜单项，可选
 * @property {string[]} [permissions] 所需权限，可选
 */
export interface MenuItem {
  id: string
  name: string
  path: string
  icon?: string
  parentId?: string
  order: number
  children?: MenuItem[]
  permissions?: string[]
}

