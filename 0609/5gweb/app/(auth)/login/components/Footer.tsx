"use client"

import React from "react"
import { LoginPageSettings } from "./types"

interface FooterProps {
  settings: LoginPageSettings;
}

export function Footer({ settings }: FooterProps) {
  // 如果页脚文本为空，则不显示任何内容
  if (!settings.footerText) {
    return null;
  }

  return (
    <footer className="py-2 text-center text-sm text-gray-500 dark:text-gray-400">
      <div
        className="container mx-auto px-4"
        dangerouslySetInnerHTML={{
          __html: settings.footerText
        }}
      />
    </footer>
  )
}
