/**
 * 登录页面组件共享类型定义
 */

// 登录页面使用的设置接口
export interface LoginPageSettings {
  backgroundImage: string;
  footerText: string;
  backgroundEffect?: string;
  title?: string;
  subtitle?: string;
  logo?: string;
  features?: Array<{
    icon: string;
    text: string;
  }>;
}

// 粒子类型
export interface Particle {
  id: number;
  x: string;
  y: string;
  size: number;
  delay: string;
}

// 数据线类型
export interface DataLine {
  id: number;
  startX: string;
  delay: string;
  width: number;
  opacity: number;
}

// 装饰元素类型
export interface DecorElement {
  id: number;
  delay: string;
  left: string;
  top: string;
}

// 对话框内容类型
export interface DialogContent {
  title: string;
  description: string;
  action: (() => void) | null;
}

// 密码强度类型
export interface PasswordStrength {
  score: number;
  hasLowerCase: boolean;
  hasUpperCase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
  isLongEnough: boolean;
}

// 登录表单错误类型
export interface LoginErrors {
  username: string;
  password: string;
}

// 注册表单错误类型
export interface RegisterErrors {
  username: string;
  email: string;
  verificationCode: string;
  password: string;
  confirmPassword: string;
}
