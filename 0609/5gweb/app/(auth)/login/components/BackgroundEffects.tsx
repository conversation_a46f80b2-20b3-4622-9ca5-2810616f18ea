"use client"

import React, { useState, useEffect } from "react"
import { Particle, DataLine, DecorElement, LoginPageSettings } from "./types"
import { generateParticles, generateDataLines } from "./utils"

interface BackgroundEffectsProps {
  settings: LoginPageSettings;
}

export function BackgroundEffects({ settings }: BackgroundEffectsProps) {
  // 添加粒子和数据线状态
  const [particles, setParticles] = useState<Particle[]>([])
  const [dataLines, setDataLines] = useState<DataLine[]>([])
  const [decorElements, setDecorElements] = useState<DecorElement[]>([])

  // 初始化粒子和数据线
  useEffect(() => {
    // 根据设置中的背景特效类型来控制特效的显示
    const effect = settings.backgroundEffect || 'all';

    if (effect === 'particles' || effect === 'all') {
      setParticles(generateParticles(30))
    } else {
      setParticles([])
    }

    if (effect === 'dataLines' || effect === 'all') {
      setDataLines(generateDataLines(15))
    } else {
      setDataLines([])
    }

    setDecorElements([
      { id: 1, delay: '0.2s', left: '10%', top: '20%' },
      { id: 2, delay: '0.5s', left: '25%', top: '60%' },
      { id: 3, delay: '0.8s', left: '60%', top: '30%' },
      { id: 4, delay: '1.2s', left: '80%', top: '70%' },
      { id: 5, delay: '1.5s', left: '40%', top: '80%' },
    ])
  }, [settings.backgroundEffect])

  return (
    <div className="absolute inset-0 bg-cover bg-center transition-transform duration-1000 hover:scale-110"
      style={{
        backgroundImage: `url(${settings.backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        width: '100%',
        height: '100%',
      }}
    >
      {/* 暗色遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/50 to-transparent animate-gradient" />

      {/* 科技网格背景 */}
      {(settings.backgroundEffect === 'grid' || settings.backgroundEffect === 'all') && (
        <div className="absolute inset-0 bg-grid animate-pulse-slow" />
      )}

      {/* 粒子效果 */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 rounded-full bg-blue-400 animate-float"
          style={{
            left: particle.x,
            top: particle.y,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDelay: particle.delay,
            opacity: Math.random() * 0.5 + 0.3,
          }}
        />
      ))}

      {/* 数据线效果 */}
      {dataLines.map((line) => (
        <div
          key={line.id}
          className="absolute h-px bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0 animate-data-line"
          style={{
            left: line.startX,
            top: `${Math.random() * 100}%`,
            width: `${line.width}px`,
            opacity: line.opacity,
            animationDelay: line.delay,
          }}
        />
      ))}

      {/* 装饰元素 */}
      {decorElements.map((elem) => (
        <div
          key={elem.id}
          className="absolute w-16 h-16 animate-pulse-slow"
          style={{
            left: elem.left,
            top: elem.top,
            animationDelay: elem.delay,
            borderRadius: '30% 70% 70% 30% / 30% 30% 70% 70%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0) 70%)',
            border: '1px solid rgba(59, 130, 246, 0.2)',
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.1)',
          }}
        >
          <div className="absolute inset-0 bg-grid-small animate-pulse-slow opacity-30" />
        </div>
      ))}
    </div>
  )
}
