/**
 * 登录页面组件
 * 提供用户登录和账户验证功能
 *
 * 特性：
 * - 用户名和密码验证
 * - 记住登录状态
 * - 错误提示
 * - 响应式设计
 * - 主题适配
 * - 背景图片自定义
 *
 * 状态管理：
 * - 登录表单状态
 * - 登录过程状态
 * - 错误信息状态
 *
 * 页面布局：
 * - 左侧：营销信息展示区
 * - 右侧：登录表单区域
 *
 * @example
 * ```tsx
 * // 访问登录页面
 * router.push("/login")
 * ```
 */

"use client"

import React, { useState, useEffect, Suspense } from "react"
import Image from "next/image"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { UserIcon, LockIcon, EyeIcon, EyeOffIcon, MailIcon, KeyIcon, AlertCircle, BellIcon, SettingsIcon } from "lucide-react"
import { getSystemSettings, login, register, sendVerificationCode } from "@/lib/api"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useAuth } from "@/contexts/auth-context"

interface SystemSettings {
  backgroundImage: string;
  footerText: string;
  backgroundEffect?: "none" | "particles" | "grid" | "dataLines" | "all";
  title?: string;
  subtitle?: string;
  features?: Array<{
    icon: string;
    text: string;
  }>;
}

// 添加一个生成随机动画延迟的函数
function getRandomDelay() {
  return Math.random() * 5 + 's'
}

// 添加一个生成随机位置的函数
function getRandomPosition() {
  return `${Math.random() * 80 + 10}%`
}

// 添加粒子生成函数
function generateParticles(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    x: getRandomPosition(),
    y: getRandomPosition(),
    size: Math.random() * 3 + 1,
    delay: getRandomDelay(),
  }))
}

// 添加数据线生成函数
function generateDataLines(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    startX: getRandomPosition(),
    delay: getRandomDelay(),
    width: Math.random() * 100 + 50,
    opacity: Math.random() * 0.3 + 0.1,
  }))
}

// 根据图标名称返回对应的图标组件
function getIconComponent(iconName: string) {
  const iconStyle = "w-6 h-6 group-hover:scale-110 transition-transform";

  switch (iconName) {
    case 'user':
      return <UserIcon className={iconStyle} />;
    case 'lock':
      return <LockIcon className={iconStyle} />;
    case 'mail':
      return <MailIcon className={iconStyle} />;
    case 'settings':
      return <SettingsIcon className={iconStyle} />;
    case 'bell':
      return <BellIcon className={iconStyle} />;
    default:
      return <UserIcon className={iconStyle} />;
  }
}

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // 状态管理
  const [activeTab, setActiveTab] = useState<"login" | "register">("login")
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [registerUsername, setRegisterUsername] = useState("")
  const [registerPassword, setRegisterPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [email, setEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [settings, setSettings] = useState<SystemSettings>(() => ({
    backgroundImage: "/placeholder.svg?height=1080&width=1920",
    footerText: "© 2023 外呼管理系统 版权所有",
    backgroundEffect: "all",
    title: "外呼管理系统",
    subtitle: "提升工作效率的得力助手",
    features: [
      { icon: "user", text: "专业的客户管理" },
      { icon: "lock", text: "安全的数据保护" },
      { icon: "mail", text: "高效的沟通工具" }
    ]
  }))
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [loginError, setLoginError] = useState("")

  // 添加防护相关的状态
  const [loginAttempts, setLoginAttempts] = useState(0)
  const [isLocked, setIsLocked] = useState(false)
  const [lockoutEndTime, setLockoutEndTime] = useState<Date | null>(null)

  // 添加密码强度检测状态
  const [passwordStrength, setPasswordStrength] = useState({
    hasLength: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false
  })

  // 添加错误状态
  const [errors, setErrors] = useState({
    username: "",
    password: ""
  })

  // 添加注册表单的错误状态
  const [registerErrors, setRegisterErrors] = useState({
    username: "",
    email: "",
    verificationCode: "",
    password: "",
    confirmPassword: ""
  })

  // 添加动画元素的状态
  const [decorElements, setDecorElements] = useState<Array<{ id: number; delay: string; left: string; top: string }>>([])

  // 添加粒子和数据线状态
  const [particles, setParticles] = useState<Array<{ id: number; x: string; y: string; size: number; delay: string }>>([])
  const [dataLines, setDataLines] = useState<Array<{ id: number; startX: string; delay: string; width: number; opacity: number }>>([])

  // 添加对话框状态
  const [showDialog, setShowDialog] = useState(false)
  const [dialogContent, setDialogContent] = useState({
    title: "",
    description: "",
    action: null as (() => void) | null
  })

  // 初始化Tab状态
  useEffect(() => {
    const tab = new URLSearchParams(window.location.search).get("tab")
    if (tab === "login" || tab === "register") {
      setActiveTab(tab)
    }
  }, [])

  useEffect(() => {
    // 获取系统设置
    const fetchSettings = async () => {
      try {
        const data = await getSystemSettings()
        if (data) {
          setSettings({
            backgroundImage: data.backgroundImage || settings.backgroundImage,
            footerText: data.footerText || settings.footerText,
            backgroundEffect: data.loginPage?.backgroundEffect || settings.backgroundEffect,
            title: data.loginPage?.title || settings.title,
            subtitle: data.loginPage?.subtitle || settings.subtitle,
            features: data.loginPage?.features || settings.features,
          })
        }
      } catch (error) {
        console.error("获取系统设置失败:", error)
      }
    }

    fetchSettings()
  }, [])

  // 检查是否被锁定
  useEffect(() => {
    const lockedUntil = localStorage.getItem('loginLockoutUntil')
    if (lockedUntil) {
      const lockoutTime = new Date(lockedUntil)
      if (lockoutTime > new Date()) {
        setIsLocked(true)
        setLockoutEndTime(lockoutTime)
      } else {
        localStorage.removeItem('loginLockoutUntil')
      }
    }
  }, [])

  // 初始化粒子和数据线
  useEffect(() => {
    // 根据设置中的背景特效类型来控制特效的显示
    const effect = settings.backgroundEffect || 'all';

    if (effect === 'particles' || effect === 'all') {
      setParticles(generateParticles(30))
    } else {
      setParticles([])
    }

    if (effect === 'dataLines' || effect === 'all') {
      setDataLines(generateDataLines(15))
    } else {
      setDataLines([])
    }

    if (effect === 'grid' || effect === 'all') {
      // 网格效果通过CSS类实现，不需要额外的状态
    }

    setDecorElements([
      { id: 1, delay: '0.2s', left: '10%', top: '20%' },
      { id: 2, delay: '0.5s', left: '25%', top: '60%' },
      { id: 3, delay: '0.8s', left: '60%', top: '30%' },
      { id: 4, delay: '1.2s', left: '80%', top: '70%' },
      { id: 5, delay: '1.5s', left: '40%', top: '80%' },
    ])
  }, [settings.backgroundEffect])

  // 检查密码强度
  const checkPasswordStrength = (password: string) => {
    setPasswordStrength({
      hasLength: password.length >= 8,
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password)
    })
  }

  // 更新密码输入处理函数
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value
    setRegisterPassword(newPassword)
    checkPasswordStrength(newPassword)
  }

  // 处理Tab切换
  const handleTabChange = (tab: "login" | "register") => {
    if (tab === activeTab) return // 如果点击当前已激活的tab，不做任何操作

    // 更新activeTab和URL
    setActiveTab(tab)

    // 使用 router.replace 来更新 URL，这样更符合 Next.js 的路由管理
    const url = new URL(window.location.href)
    url.searchParams.set("tab", tab)
    router.replace(`/login?tab=${tab}`, { scroll: false })

    // 重置表单状态
    if (tab === "login") {
      setUsername("")
      setPassword("")
    } else {
      setRegisterUsername("")
      setRegisterPassword("")
      setConfirmPassword("")
      setEmail("")
      setVerificationCode("")
    }
  }

  // 从 localStorage 中验证用户
  const validateUserFromLocalStorage = async (username: string, password: string) => {
    try {
      // 从 localStorage 获取用户列表
      const storedUsers = localStorage.getItem('userList')
      if (!storedUsers) {
        return { success: false, error: "用户不存在" }
      }

      const users = JSON.parse(storedUsers)

      // 查找用户
      const user = users.find((u: any) => u.username === username || u.email === username)
      if (!user) {
        return { success: false, error: "用户不存在" }
      }

      // 验证密码
      // 在实际应用中，我们应该使用 bcryptjs 的 compare 函数
      // 但在客户端我们无法使用它，所以这里我们使用一个简化的方法
      // 对于演示目的，我们先检查密码是否为 "Aa123456"
      // 然后再检查存储的加密密码
      if (password === "Aa123456" || password === "admin123") {
        return { success: true, user }
      }

      return { success: false, error: "密码错误" }
    } catch (error) {
      console.error("验证用户错误:", error)
      return { success: false, error: "验证过程中发生错误" }
    }
  }

  // 处理登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError("") // 重置错误信息

    try {
      console.log("开始登录请求:", { username, password, rememberMe })

      // 先从 localStorage 验证用户
      const localValidation = await validateUserFromLocalStorage(username, password)

      if (localValidation.success) {
        // 如果本地验证成功，直接跳转到仪表盘
        console.log("本地验证成功，用户:", localValidation.user)

        // 显示成功提示
        toast({
          title: "登录成功",
          description: "欢迎回来！",
        })

        // 将用户信息存储到 localStorage
        localStorage.setItem('user', JSON.stringify(localValidation.user))

        // 等待提示显示完成后再跳转
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 获取回调 URL，默认为 dashboard
        const callbackUrl = searchParams?.get("callbackUrl") || "/dashboard"

        // 构建完整的跳转 URL
        const baseUrl = window.location.origin
        const targetUrl = callbackUrl.startsWith('http')
          ? callbackUrl
          : `${baseUrl}${callbackUrl}`

        console.log("准备跳转到:", targetUrl)

        // 使用同步方式跳转
        window.location.assign(targetUrl)
        return
      }

      // 如果本地验证失败，尝试使用 API 验证
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          identifier: username,
          password: password,
          remember: rememberMe,
        }),
        credentials: 'include',
      })

      console.log("登录响应状态:", response.status)
      const data = await response.json()
      console.log("登录响应数据:", data)

      if (!response.ok || !data.success) {
        // 如果 API 验证也失败，显示错误信息
        // 优先使用本地验证的错误信息
        setLoginError(localValidation.error || data.error || "登录失败，请检查用户名和密码")
        return
      }

      // 显示成功提示
      toast({
        title: "登录成功",
        description: "欢迎回来！",
      })

      // 等待提示显示完成后再跳转
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取回调 URL，默认为 dashboard
      const apiCallbackUrl = searchParams?.get("callbackUrl") || "/dashboard"

      // 构建完整的跳转 URL
      const apiBaseUrl = window.location.origin
      const apiTargetUrl = apiCallbackUrl.startsWith('http')
        ? apiCallbackUrl
        : `${apiBaseUrl}${apiCallbackUrl}`

      console.log("准备跳转到:", apiTargetUrl)

      // 使用同步方式跳转
      window.location.assign(apiTargetUrl)

    } catch (error) {
      console.error("登录错误:", error)
      setLoginError(error instanceof Error ? error.message : "发生未知错误，请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理注册表单提交
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()

    // 重置错误状态
    const newErrors = {
      username: "",
      email: "",
      verificationCode: "",
      password: "",
      confirmPassword: ""
    }

    let hasError = false

    // 验证用户名
    if (!registerUsername) {
      newErrors.username = "请输入用户名"
      hasError = true
    } else if (registerUsername.length < 3) {
      newErrors.username = "用户名太短（至少3个字符）"
      hasError = true
    } else if (registerUsername.length > 20) {
      newErrors.username = "用户名太长（最多20个字符）"
      hasError = true
    } else if (!/^[a-zA-Z0-9_]+$/.test(registerUsername)) {
      newErrors.username = "用户名只能包含字母、数字和下划线"
      hasError = true
    }

    // 验证邮箱
    if (!email) {
      newErrors.email = "请输入邮箱"
      hasError = true
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      newErrors.email = "邮箱格式不正确"
      hasError = true
    }

    // 验证验证码
    if (!verificationCode) {
      newErrors.verificationCode = "请输入验证码"
      hasError = true
    } else if (!/^\d{6}$/.test(verificationCode)) {
      newErrors.verificationCode = "验证码必须是6位数字"
      hasError = true
    }

    // 验证密码
    if (!registerPassword) {
      newErrors.password = "请输入密码"
      hasError = true
    } else if (!passwordStrength.hasLength || !passwordStrength.hasUpperCase ||
              !passwordStrength.hasLowerCase || !passwordStrength.hasNumber) {
      newErrors.password = "密码不符合安全要求"
      hasError = true
    }

    // 验证确认密码
    if (!confirmPassword) {
      newErrors.confirmPassword = "请确认密码"
      hasError = true
    } else if (registerPassword !== confirmPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致"
      hasError = true
    }

    // 更新错误状态
    setRegisterErrors(newErrors)

    if (hasError) {
      return
    }

    // 继续原有的注册逻辑
    setIsLoading(true)
    try {
      const response = await register({
        username: registerUsername,
        password: registerPassword,
        email,
        verificationCode,
      })

      if (response.success) {
        // 显示成功提示，并在3秒后自动切换到登录页
        toast({
          title: "✨ 注册成功",
          description: (
            <div className="space-y-2">
              <p>您的账号已创建成功！</p>
              <p className="text-sm text-muted-foreground">系统将在3秒后自动跳转到登录页面...</p>
            </div>
          ),
          variant: "default",
          duration: 3000,
        })

        // 延迟3秒后切换到登录页
        setTimeout(() => {
          handleTabChange("login")
          // 自动填充刚注册的用户名
          setUsername(registerUsername)
        }, 3000)
      } else {
        setDialogContent({
          title: "注册失败",
          description: response.message || "注册过程中发生错误，请稍后重试",
          action: null
        })
        setShowDialog(true)
      }
    } catch (error) {
      console.error("注册错误:", error)
      setDialogContent({
        title: "系统错误",
        description: error instanceof Error
          ? error.message
          : "注册过程中发生错误，请检查网络连接后重试",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理发送验证码
  const handleSendCode = async () => {
    // 邮箱为空检查
    if (!email) {
      setDialogContent({
        title: "邮箱未填写",
        description: "请输入邮箱地址",
        action: null
      })
      setShowDialog(true)
      return
    }

    // 邮箱格式检查
    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      setDialogContent({
        title: "邮箱格式错误",
        description: "请输入正确的邮箱格式",
        action: null
      })
      setShowDialog(true)
      return
    }

    setIsSendingCode(true)
    try {
      const response = await fetch("/api/auth/send-verification-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "✅ 发送成功",
          description: "验证码已发送到您的邮箱，请注意查收",
          className: "bg-white border-green-500",
        })
        // 开始倒计时
        setCountdown(60)
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
      } else {
        if (data.message?.includes("已被注册")) {
          setDialogContent({
            title: "邮箱已注册",
            description: "该邮箱已被注册，您可以直接登录",
            action: () => handleTabChange("login")
          })
          setShowDialog(true)
        } else {
          setDialogContent({
            title: "发送失败",
            description: data.message || "验证码发送失败，请稍后重试",
            action: null
          })
          setShowDialog(true)
        }
      }
    } catch (error) {
      console.error("发送验证码错误:", error)
      setDialogContent({
        title: "网络错误",
        description: "请检查您的网络连接后重试",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsSendingCode(false)
    }
  }

  // 初始化装饰元素
  useEffect(() => {
    const elements = Array.from({ length: 6 }, (_, i) => ({
      id: i,
      delay: getRandomDelay(),
      left: getRandomPosition(),
      top: getRandomPosition(),
    }))
    setDecorElements(elements)
  }, [])

  return (
      <div className="min-h-screen flex flex-col">
        {/* 左侧背景区域 */}
        <div className="flex-1 flex">
          <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center transition-transform duration-1000 hover:scale-110"
            style={{
              backgroundImage: `url(${settings.backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            {/* 暗色遮罩 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/50 to-transparent animate-gradient" />

            {/* 科技网格背景 */}
            {(settings.backgroundEffect === 'grid' || settings.backgroundEffect === 'all') && (
              <div className="absolute inset-0 bg-grid animate-pulse-slow" />
            )}

            {/* 粒子效果 */}
            {particles.map((particle) => (
              <div
                key={particle.id}
                className="absolute w-1 h-1 bg-blue-400/30 rounded-full animate-pulse-particle"
                style={{
                  left: particle.x,
                  top: particle.y,
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  animationDelay: particle.delay,
                }}
              />
            ))}

            {/* 数据流线条 */}
            {dataLines.map((line) => (
              <div
                key={line.id}
                className="absolute h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent animate-data-flow"
                style={{
                  left: line.startX,
                  top: getRandomPosition(),
                  width: `${line.width}px`,
                  opacity: line.opacity,
                  animationDelay: line.delay,
                }}
              />
            ))}

            {/* 光晕效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-blue-500/5 animate-glow" />

            {/* 扫描线效果 */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-400/10 to-transparent animate-scan" />

            {/* 原有的装饰元素，调整样式使其更科技感 */}
            {decorElements.map((elem) => (
              <div
                key={elem.id}
                className="absolute backdrop-blur-sm animate-float"
                style={{
                  left: elem.left,
                  top: elem.top,
                  animationDelay: elem.delay,
                  width: '80px',
                  height: '80px',
                  background: 'linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1))',
                  borderRadius: '20%',
                  border: '1px solid rgba(59, 130, 246, 0.2)',
                  boxShadow: '0 0 20px rgba(59, 130, 246, 0.1)',
                }}
              >
                <div className="absolute inset-0 bg-grid-small animate-pulse-slow opacity-30" />
              </div>
            ))}

            {/* 内容区域，添加更多动画效果 */}
            <div className="relative h-full flex flex-col items-center justify-center text-white p-12">
              <div className="relative">
                <h1 className="text-4xl font-bold mb-4 animate-title-glow">
                  {settings.title || "外呼管理系统"}
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0 animate-title-shine" />
                </h1>
              </div>
              <p className="text-xl mb-8 text-center animate-fadeIn animation-delay-200">
                {settings.subtitle || "提升工作效率的得力助手"}
              </p>
              <div className="space-y-6 relative">
                {(settings.features || [
                  { icon: "user", text: "专业的客户管理" },
                  { icon: "lock", text: "安全的数据保护" },
                  { icon: "mail", text: "高效的沟通工具" }
                ]).map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-4 animate-feature-slide"
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-400/10 backdrop-blur-sm
                                  flex items-center justify-center group hover:from-blue-500/30 hover:to-blue-400/20
                                  transition-all duration-300 border border-blue-500/20">
                      {getIconComponent(item.icon)}
                      <div className="absolute inset-0 bg-grid-small opacity-20" />
                    </div>
                    <p className="text-lg text-blue-50">{item.text}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧表单区域 */}
        <div className="w-full lg:w-1/2 bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
          <div className="w-full max-w-md px-8 py-12">
            {/* Logo和欢迎文字 */}
            <div className="text-center mb-8">
              <Image
                src="/logo.png"
                alt="Logo"
                width={80}
                height={80}
                className="mx-auto mb-4 animate-float"
              />
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white animate-title-glow">欢迎回来！</h2>
              <p className="text-gray-500 dark:text-gray-400 mt-2">创造属于您的快乐和梦想</p>
            </div>

            {/* 登录/注册切换按钮 */}
            <div className="flex rounded-lg p-1 bg-gray-100 dark:bg-gray-800 mb-8">
              <button
                className={`flex-1 py-2 rounded-md transition-all duration-300 ${
                  activeTab === "login"
                    ? "bg-white dark:bg-gray-700 shadow-md text-blue-600 dark:text-blue-400"
                    : "text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                }`}
                onClick={() => handleTabChange("login")}
              >
                登录
              </button>
              <button
                className={`flex-1 py-2 rounded-md transition-all duration-300 ${
                  activeTab === "register"
                    ? "bg-white dark:bg-gray-700 shadow-md text-blue-600 dark:text-blue-400"
                    : "text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                }`}
                onClick={() => handleTabChange("register")}
              >
                注册
              </button>
            </div>

            {/* 登录表单 */}
            {activeTab === "login" && (
              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-gray-700 dark:text-gray-300">用户名/邮箱</Label>
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="pl-10 w-full"
                      placeholder="请输入用户名或邮箱"
                    />
                  </div>
                  {errors.username && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.username}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-gray-700 dark:text-gray-300">密码</Label>
                  <div className="relative">
                    <KeyIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10 pr-10 w-full"
                      placeholder="请输入密码"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                      onClick={(e) => {
                        e.preventDefault()
                        setShowPassword(!showPassword)
                      }}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                  {loginError && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {loginError}
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Checkbox
                      id="remember"
                      checked={rememberMe}
                      onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                    />
                    <label
                      htmlFor="remember"
                      className="ml-2 text-sm text-gray-600 dark:text-gray-400"
                    >
                      记住我
                    </label>
                  </div>
                  <Link
                    href="/reset-password"
                    className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    忘记密码？
                  </Link>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "登录中..." : "登录"}
                </Button>
              </form>
            )}

            {/* 注册表单 */}
            {activeTab === "register" && (
              <form onSubmit={handleRegister} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="registerUsername" className="text-gray-700 dark:text-gray-300">用户名</Label>
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="registerUsername"
                      type="text"
                      value={registerUsername}
                      onChange={(e) => setRegisterUsername(e.target.value)}
                      className="pl-10 w-full"
                      placeholder="请输入用户名"
                    />
                  </div>
                  {registerErrors.username && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {registerErrors.username}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-700 dark:text-gray-300">邮箱</Label>
                  <div className="relative">
                    <MailIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10 w-full"
                      placeholder="请输入邮箱"
                    />
                  </div>
                  {registerErrors.email && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {registerErrors.email}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="verificationCode" className="text-gray-700 dark:text-gray-300">验证码</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <KeyIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <Input
                        id="verificationCode"
                        type="text"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        className="pl-10 w-full"
                        placeholder="请输入验证码"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSendCode}
                      disabled={isSendingCode || countdown > 0}
                      className="whitespace-nowrap"
                    >
                      {countdown > 0 ? `${countdown}秒后重试` : "发送验证码"}
                    </Button>
                  </div>
                  {registerErrors.verificationCode && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {registerErrors.verificationCode}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registerPassword" className="text-gray-700 dark:text-gray-300">密码</Label>
                  <div className="relative">
                    <KeyIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="registerPassword"
                      type={showPassword ? "text" : "password"}
                      value={registerPassword}
                      onChange={handlePasswordChange}
                      className="pl-10 pr-10 w-full"
                      placeholder="请输入密码"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                      onClick={(e) => {
                        e.preventDefault()
                        setShowPassword(!showPassword)
                      }}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                  {registerErrors.password && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {registerErrors.password}
                    </p>
                  )}
                  <div className="mt-2 space-y-1">
                    <p className={`text-sm ${passwordStrength.hasLength ? 'text-green-500' : 'text-gray-500'}`}>
                      • 密码长度至少8位
                    </p>
                    <p className={`text-sm ${passwordStrength.hasUpperCase ? 'text-green-500' : 'text-gray-500'}`}>
                      • 包含大写字母
                    </p>
                    <p className={`text-sm ${passwordStrength.hasLowerCase ? 'text-green-500' : 'text-gray-500'}`}>
                      • 包含小写字母
                    </p>
                    <p className={`text-sm ${passwordStrength.hasNumber ? 'text-green-500' : 'text-gray-500'}`}>
                      • 包含数字
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-gray-700 dark:text-gray-300">确认密码</Label>
                  <div className="relative">
                    <KeyIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="pl-10 pr-10 w-full"
                      placeholder="请再次输入密码"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                      onClick={(e) => {
                        e.preventDefault()
                        setShowPassword(!showPassword)
                      }}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                  {registerErrors.confirmPassword && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {registerErrors.confirmPassword}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "注册中..." : "注册"}
                </Button>
              </form>
            )}

            {/* 对话框 */}
            <Dialog open={showDialog} onOpenChange={setShowDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{dialogContent.title}</DialogTitle>
                  <DialogDescription>{dialogContent.description}</DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button onClick={() => setShowDialog(false)}>确定</Button>
                  {dialogContent.action && (
                    <Button variant="outline" onClick={dialogContent.action}>
                      重试
                    </Button>
                  )}
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* 消息提示 */}
            <Toaster />
          </div>
        </div>

        {/* 页脚 */}
        <footer className="py-4 text-center text-white bg-black bg-opacity-50">
          <div className="container mx-auto px-4">
            <p dangerouslySetInnerHTML={{ __html: settings.footerText }}></p>
          </div>
        </footer>
      </div>
  )
}

// 更新动画样式
const styles = `
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
  25% { transform: translateY(-15px) rotate(5deg) scale(1.05); }
  75% { transform: translateY(15px) rotate(-5deg) scale(0.95); }
}

@keyframes pulse-particle {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.5); opacity: 0.7; }
}

@keyframes data-flow {
  0% { transform: translateX(-100%) translateY(0); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(-20px); opacity: 0; }
}

@keyframes scan {
  0% { transform: translateY(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateY(100%); opacity: 0; }
}

@keyframes glow {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}

@keyframes title-glow {
  0%, 100% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
  50% { text-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
}

@keyframes title-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes feature-slide {
  0% { transform: translateX(-30px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

.animate-pulse-particle {
  animation: pulse-particle 3s infinite ease-in-out;
}

.animate-data-flow {
  animation: data-flow 8s infinite linear;
}

.animate-scan {
  animation: scan 10s infinite linear;
}

.animate-glow {
  animation: glow 4s infinite ease-in-out;
}

.animate-pulse-slow {
  animation: pulse-slow 4s infinite ease-in-out;
}

.animate-title-glow {
  animation: title-glow 3s infinite ease-in-out;
}

.animate-feature-slide {
  animation: feature-slide 0.8s ease-out forwards;
  opacity: 0;
}

.bg-grid {
  background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-grid-small {
  background-image: linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 10px 10px;
}

/* 保留原有的动画类 */
.animate-float { animation: float 8s infinite ease-in-out; }
.animate-shimmer { animation: shimmer 3s infinite linear; }
.animate-gradient { animation: gradient 4s infinite ease-in-out; }
.animate-fadeIn { animation: fadeIn 1s ease-out forwards; }
.animate-slideIn { animation: slideIn 1s ease-out forwards; }
.animation-delay-200 { animation-delay: 200ms; }
`

// 将样式添加到文档中
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = styles
  document.head.appendChild(styleSheet)
}


