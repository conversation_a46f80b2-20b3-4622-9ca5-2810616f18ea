/**
 * 权限中间件
 * 用于API路由的权限检查
 */

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-service'
import { PermissionService } from '@/lib/permissions'

/**
 * 创建权限检查中间件
 * @param permission 需要的权限
 * @returns 中间件处理函数
 */
export function withPermission(permission: string) {
  return async (req: NextRequest, params?: any) => {
    try {
      // 获取当前用户
      const user = await AuthService.getCurrentUser()
      if (!user) {
        return NextResponse.json(
          { success: false, message: "未授权，请登录后再试", error: "Unauthorized" },
          { status: 401 }
        )
      }

      // 检查用户是否有权限
      const hasPermission = await PermissionService.hasPermission(user.id, permission)
      if (!hasPermission) {
        return NextResponse.json(
          { success: false, message: "您没有执行此操作的权限", error: "Forbidden" },
          { status: 403 }
        )
      }

      // 继续处理请求
      return null
    } catch (error) {
      console.error('权限检查失败:', error)
      return NextResponse.json(
        { success: false, message: "权限检查失败", error: "Internal Server Error" },
        { status: 500 }
      )
    }
  }
}

/**
 * 使用权限中间件包装API处理函数
 * @param permission 需要的权限
 * @param handler API处理函数
 * @returns 包装后的处理函数
 */
export function withPermissionHandler(permission: string, handler: Function) {
  return async (req: NextRequest, params?: any) => {
    // 执行权限检查
    const permissionResponse = await withPermission(permission)(req, params)
    if (permissionResponse) {
      return permissionResponse
    }
    
    // 权限检查通过，执行原始处理函数
    return handler(req, params)
  }
}
