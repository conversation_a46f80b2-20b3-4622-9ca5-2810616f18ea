# 生产环境配置
# 部署前请确保所有值都已正确设置

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=5gweb_production
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public
DIRECT_URL=${DATABASE_URL}

# JWT配置
# 注意：生产环境应使用强密钥，至少32个字符
JWT_SECRET=2gyZ3GDw3LHZQKDhPmPDL3sjREVRXPr8
JWT_REFRESH_SECRET=2gyZ3GDw3LHZQKDhPmPDL3sjREVRXPr8_refresh
JWT_EXPIRES_IN=1d

# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-production-domain.com
NODE_ENV=production
PORT=3000

# 邮件配置
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=465
EMAIL_USER=<EMAIL>
EMAIL_PASS=vkssqikbptbabjad

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# NextAuth配置
# 注意：确保NEXTAUTH_URL使用正确的生产域名
NEXTAUTH_URL=https://your-production-domain.com
NEXTAUTH_SECRET=2gyZ3GDw3LHZQKDhPmPDL3sjREVRXPr8

# 前端配置
NEXT_PUBLIC_API_BASE_URL=https://services.vcrm.vip:8000

# 视频通话配置
NEXT_PUBLIC_VIDEO_CALL_ORG_CODE=XUNMENGdorg
NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME=XUNMENG001
NEXT_PUBLIC_VIDEO_CALL_APP_ID=your-app-id-here
NEXT_PUBLIC_VIDEO_CALL_AES_KEY=f4d9145303db415a
NEXT_PUBLIC_VIDEO_CALL_AES_IV=6778fd576ed3dfff

# 回调配置
CALLBACK_BASE_URL=https://your-production-domain.com
CALL_RESULT_CALLBACK_PATH=/api/callbacks/call-result
SMS_STATUS_CALLBACK_PATH=/api/callbacks/sms-status

# 禁用模拟数据
USE_MOCK_DATA=false

# 日志配置
LOG_LEVEL=1  # 0=DEBUG, 1=INFO, 2=WARN, 3=ERROR, 4=NONE

# 性能优化
NEXT_OPTIMIZE_IMAGES=true
NEXT_OPTIMIZE_FONTS=true
NEXT_COMPRESS=true
