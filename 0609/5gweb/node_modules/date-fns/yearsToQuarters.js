import { quartersInYear } from "./constants.js";

/**
 * @name yearsToQuarters
 * @category Conversion Helpers
 * @summary Convert years to quarters.
 *
 * @description
 * Convert a number of years to a full number of quarters.
 *
 * @param years - The number of years to be converted
 *
 * @returns The number of years converted in quarters
 *
 * @example
 * // Convert 2 years to quarters
 * const result = yearsToQuarters(2)
 * //=> 8
 */
export function yearsToQuarters(years) {
  return Math.trunc(years * quartersInYear);
}

// Fallback for modularized imports:
export default yearsToQuarters;
