{"name": "quill", "version": "1.3.7", "description": "Your powerful, rich text editor", "author": "<PERSON> <<EMAIL>>", "homepage": "http://quilljs.com", "main": "dist/quill.js", "files": ["assets", "blots", "core", "formats", "modules", "themes", "ui", "dist/quill.bubble.css", "dist/quill.snow.css", "dist/quill.core.css", "dist/quill.js", "dist/quill.core.js", "dist/quill.min.js.map", "dist/quill.min.js", "core.js", "quill.js"], "config": {"ports": {"proxy": "9000", "jekyll": "4000", "karma": "9876", "webpack": "9080"}}, "dependencies": {"clone": "^2.1.1", "deep-equal": "^1.0.1", "eventemitter3": "^2.0.3", "extend": "^3.0.2", "parchment": "^1.1.4", "quill-delta": "^3.6.2"}, "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-plugin-istanbul": "^4.1.5", "babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-preset-es2015": "^6.24.1", "css-loader": "~0.28.7", "eslint": "^4.9.0", "eslint-loader": "^1.9.0", "extract-text-webpack-plugin": "^3.0.1", "html-loader": "~0.5.1", "http-proxy": "^1.16.2", "jasmine-core": "^2.8.0", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-jasmine": "^1.1.0", "karma-sauce-launcher": "^1.2.0", "lodash": "^4.17.4", "style-loader": "~0.19.0", "stylus": "~0.54.5", "stylus-loader": "^3.0.1", "ts-loader": "^3.0.3", "typescript": "^2.5.3", "wdio-jasmine-framework": "~0.3.2", "wdio-spec-reporter": "~0.1.2", "webdriver-manager": "^12.0.6", "webdriverio": "^4.8.0", "webpack": "^3.8.1", "webpack-dev-server": "^2.9.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/quilljs/quill"}, "bugs": {"url": "https://github.com/quilljs/quill/issues"}, "scripts": {"build": "webpack --config _develop/webpack.config.js; rm dist/quill.core dist/quill.bubble dist/quill.snow;", "build:release": "./_develop/scripts/release.sh", "start": "npm run build; bundle exec foreman start -f _develop/procfile", "test": "npm run test:unit", "test:all": "npm run test:unit; npm run test:functional", "test:functional": "./_develop/scripts/webdriver.sh", "test:unit": "npm run build; karma start _develop/karma.config.js", "test:coverage": "webpack --env.coverage --config _develop/webpack.config.js; karma start _develop/karma.config.js --reporters coverage", "travis": "karma start _develop/karma.config.js --reporters dots,saucelabs", "webdriver:start": "webdriver-manager start", "webdriver:update": "webdriver-manager update"}, "keywords": ["editor", "rich text", "wysiwyg"]}