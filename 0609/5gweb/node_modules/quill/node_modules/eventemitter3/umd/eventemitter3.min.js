(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.EventEmitter3=e()}})(function(){var e,t,n;return function e(t,n,r){function s(o,f){if(!n[o]){if(!t[o]){var u=typeof require=="function"&&require;if(!f&&u)return u(o,!0);if(i)return i(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s}({1:[function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty,s="~";function i(){}if(Object.create){i.prototype=Object.create(null);if(!(new i).__proto__)s=false}function o(e,t,n){this.fn=e;this.context=t;this.once=n||false}function f(){this._events=new i;this._eventsCount=0}f.prototype.eventNames=function e(){var t=[],n,i;if(this._eventsCount===0)return t;for(i in n=this._events){if(r.call(n,i))t.push(s?i.slice(1):i)}if(Object.getOwnPropertySymbols){return t.concat(Object.getOwnPropertySymbols(n))}return t};f.prototype.listeners=function e(t,n){var r=s?s+t:t,i=this._events[r];if(n)return!!i;if(!i)return[];if(i.fn)return[i.fn];for(var o=0,f=i.length,u=new Array(f);o<f;o++){u[o]=i[o].fn}return u};f.prototype.emit=function e(t,n,r,i,o,f){var u=s?s+t:t;if(!this._events[u])return false;var c=this._events[u],l=arguments.length,h,v;if(c.fn){if(c.once)this.removeListener(t,c.fn,undefined,true);switch(l){case 1:return c.fn.call(c.context),true;case 2:return c.fn.call(c.context,n),true;case 3:return c.fn.call(c.context,n,r),true;case 4:return c.fn.call(c.context,n,r,i),true;case 5:return c.fn.call(c.context,n,r,i,o),true;case 6:return c.fn.call(c.context,n,r,i,o,f),true}for(v=1,h=new Array(l-1);v<l;v++){h[v-1]=arguments[v]}c.fn.apply(c.context,h)}else{var a=c.length,p;for(v=0;v<a;v++){if(c[v].once)this.removeListener(t,c[v].fn,undefined,true);switch(l){case 1:c[v].fn.call(c[v].context);break;case 2:c[v].fn.call(c[v].context,n);break;case 3:c[v].fn.call(c[v].context,n,r);break;case 4:c[v].fn.call(c[v].context,n,r,i);break;default:if(!h)for(p=1,h=new Array(l-1);p<l;p++){h[p-1]=arguments[p]}c[v].fn.apply(c[v].context,h)}}}return true};f.prototype.on=function e(t,n,r){var i=new o(n,r||this),f=s?s+t:t;if(!this._events[f])this._events[f]=i,this._eventsCount++;else if(!this._events[f].fn)this._events[f].push(i);else this._events[f]=[this._events[f],i];return this};f.prototype.once=function e(t,n,r){var i=new o(n,r||this,true),f=s?s+t:t;if(!this._events[f])this._events[f]=i,this._eventsCount++;else if(!this._events[f].fn)this._events[f].push(i);else this._events[f]=[this._events[f],i];return this};f.prototype.removeListener=function e(t,n,r,o){var f=s?s+t:t;if(!this._events[f])return this;if(!n){if(--this._eventsCount===0)this._events=new i;else delete this._events[f];return this}var u=this._events[f];if(u.fn){if(u.fn===n&&(!o||u.once)&&(!r||u.context===r)){if(--this._eventsCount===0)this._events=new i;else delete this._events[f]}}else{for(var c=0,l=[],h=u.length;c<h;c++){if(u[c].fn!==n||o&&!u[c].once||r&&u[c].context!==r){l.push(u[c])}}if(l.length)this._events[f]=l.length===1?l[0]:l;else if(--this._eventsCount===0)this._events=new i;else delete this._events[f]}return this};f.prototype.removeAllListeners=function e(t){var n;if(t){n=s?s+t:t;if(this._events[n]){if(--this._eventsCount===0)this._events=new i;else delete this._events[n]}}else{this._events=new i;this._eventsCount=0}return this};f.prototype.off=f.prototype.removeListener;f.prototype.addListener=f.prototype.on;f.prototype.setMaxListeners=function e(){return this};f.prefixed=s;f.EventEmitter=f;if("undefined"!==typeof t){t.exports=f}},{}]},{},[1])(1)});