{"version": 3, "file": "composite-xform.js", "names": ["BaseXform", "require", "CompositeXform", "createNewModel", "node", "parseOpen", "parser", "map", "name", "tag", "model", "parseText", "text", "onParserClose", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../lib/xlsx/xform/composite-xform.js"], "sourcesContent": ["const BaseXform = require('./base-xform');\n\n/* 'virtual' methods used as a form of documentation */\n/* eslint-disable class-methods-use-this */\n\n// base class for xforms that are composed of other xforms\n// offers some default implementations\nclass CompositeXform extends BaseXform {\n  createNewModel(node) {\n    return {};\n  }\n\n  parseOpen(node) {\n    // Typical pattern for composite xform\n    this.parser = this.parser || this.map[node.name];\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    if (node.name === this.tag) {\n      this.model = this.createNewModel(node);\n      return true;\n    }\n\n    return false;\n  }\n\n  parseText(text) {\n    // Default implementation. Send text to child parser\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  onParserClose(name, parser) {\n    // parseClose has seen a child parser close\n    // now need to incorporate into this.model somehow\n    this.model[name] = parser.model;\n  }\n\n  parseClose(name) {\n    // Default implementation\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.onParserClose(name, this.parser);\n        this.parser = undefined;\n      }\n      return true;\n    }\n\n    return name !== this.tag;\n  }\n}\n\nmodule.exports = CompositeXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEzC;AACA;;AAEA;AACA;AACA,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrCG,cAAcA,CAACC,IAAI,EAAE;IACnB,OAAO,CAAC,CAAC;EACX;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd;IACA,IAAI,CAACE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,IAAI,CAAC;IAChD,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACD,SAAS,CAACD,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,IAAIA,IAAI,CAACI,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC1B,IAAI,CAACC,KAAK,GAAG,IAAI,CAACP,cAAc,CAACC,IAAI,CAAC;MACtC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAO,SAASA,CAACC,IAAI,EAAE;IACd;IACA,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACK,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,aAAaA,CAACL,IAAI,EAAEF,MAAM,EAAE;IAC1B;IACA;IACA,IAAI,CAACI,KAAK,CAACF,IAAI,CAAC,GAAGF,MAAM,CAACI,KAAK;EACjC;EAEAI,UAAUA,CAACN,IAAI,EAAE;IACf;IACA,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACQ,UAAU,CAACN,IAAI,CAAC,EAAE;QACjC,IAAI,CAACK,aAAa,CAACL,IAAI,EAAE,IAAI,CAACF,MAAM,CAAC;QACrC,IAAI,CAACA,MAAM,GAAGS,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IAEA,OAAOP,IAAI,KAAK,IAAI,CAACC,GAAG;EAC1B;AACF;AAEAO,MAAM,CAACC,OAAO,GAAGf,cAAc"}