{"version": 3, "file": "vml-client-data-xform.js", "names": ["BaseXform", "require", "VmlAnchorXform", "VmlProtectionXform", "VmlPositionXform", "POSITION_TYPE", "VmlClientDataXform", "constructor", "map", "tag", "render", "xmlStream", "model", "protection", "editAs", "note", "openNode", "ObjectType", "locked", "leafNode", "lockText", "refAddress", "row", "col", "closeNode", "parseOpen", "node", "name", "reset", "anchor", "parser", "parseText", "text", "parseClose", "undefined", "normalizeModel", "position", "Object", "assign", "len", "keys", "length", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/comment/vml-client-data-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst VmlAnchorXform = require('./vml-anchor-xform');\nconst VmlProtectionXform = require('./style/vml-protection-xform');\nconst VmlPositionXform = require('./style/vml-position-xform');\n\nconst POSITION_TYPE = ['twoCells', 'oneCells', 'absolute'];\n\nclass VmlClientDataXform extends BaseXform {\n  constructor() {\n    super();\n    this.map = {\n      'x:Anchor': new VmlAnchorXform(),\n      'x:Locked': new VmlProtectionXform({tag: 'x:Locked'}),\n      'x:LockText': new VmlProtectionXform({tag: 'x:LockText'}),\n      'x:SizeWithCells': new VmlPositionXform({tag: 'x:SizeWithCells'}),\n      'x:MoveWithCells': new VmlPositionXform({tag: 'x:MoveWithCells'}),\n    };\n  }\n\n  get tag() {\n    return 'x:ClientData';\n  }\n\n  render(xmlStream, model) {\n    const {protection, editAs} = model.note;\n    xmlStream.openNode(this.tag, {ObjectType: 'Note'});\n    this.map['x:MoveWithCells'].render(xmlStream, editAs, POSITION_TYPE);\n    this.map['x:SizeWithCells'].render(xmlStream, editAs, POSITION_TYPE);\n    this.map['x:Anchor'].render(xmlStream, model);\n    this.map['x:Locked'].render(xmlStream, protection.locked);\n    xmlStream.leafNode('x:AutoFill', null, 'False');\n    this.map['x:LockText'].render(xmlStream, protection.lockText);\n    xmlStream.leafNode('x:Row', null, model.refAddress.row - 1);\n    xmlStream.leafNode('x:Column', null, model.refAddress.col - 1);\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          anchor: [],\n          protection: {},\n          editAs: '',\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.normalizeModel();\n        return false;\n      default:\n        return true;\n    }\n  }\n\n  normalizeModel() {\n    const position = Object.assign(\n      {},\n      this.map['x:MoveWithCells'].model,\n      this.map['x:SizeWithCells'].model\n    );\n    const len = Object.keys(position).length;\n    this.model.editAs = POSITION_TYPE[len];\n    this.model.anchor = this.map['x:Anchor'].text;\n    this.model.protection.locked = this.map['x:Locked'].text;\n    this.model.protection.lockText = this.map['x:LockText'].text;\n  }\n}\n\nmodule.exports = VmlClientDataXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAME,kBAAkB,GAAGF,OAAO,CAAC,8BAA8B,CAAC;AAClE,MAAMG,gBAAgB,GAAGH,OAAO,CAAC,4BAA4B,CAAC;AAE9D,MAAMI,aAAa,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;AAE1D,MAAMC,kBAAkB,SAASN,SAAS,CAAC;EACzCO,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,GAAG,GAAG;MACT,UAAU,EAAE,IAAIN,cAAc,CAAC,CAAC;MAChC,UAAU,EAAE,IAAIC,kBAAkB,CAAC;QAACM,GAAG,EAAE;MAAU,CAAC,CAAC;MACrD,YAAY,EAAE,IAAIN,kBAAkB,CAAC;QAACM,GAAG,EAAE;MAAY,CAAC,CAAC;MACzD,iBAAiB,EAAE,IAAIL,gBAAgB,CAAC;QAACK,GAAG,EAAE;MAAiB,CAAC,CAAC;MACjE,iBAAiB,EAAE,IAAIL,gBAAgB,CAAC;QAACK,GAAG,EAAE;MAAiB,CAAC;IAClE,CAAC;EACH;EAEA,IAAIA,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAM;MAACC,UAAU;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,IAAI;IACvCJ,SAAS,CAACK,QAAQ,CAAC,IAAI,CAACP,GAAG,EAAE;MAACQ,UAAU,EAAE;IAAM,CAAC,CAAC;IAClD,IAAI,CAACT,GAAG,CAAC,iBAAiB,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEG,MAAM,EAAET,aAAa,CAAC;IACpE,IAAI,CAACG,GAAG,CAAC,iBAAiB,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEG,MAAM,EAAET,aAAa,CAAC;IACpE,IAAI,CAACG,GAAG,CAAC,UAAU,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IAC7C,IAAI,CAACJ,GAAG,CAAC,UAAU,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEE,UAAU,CAACK,MAAM,CAAC;IACzDP,SAAS,CAACQ,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC;IAC/C,IAAI,CAACX,GAAG,CAAC,YAAY,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEE,UAAU,CAACO,QAAQ,CAAC;IAC7DT,SAAS,CAACQ,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAEP,KAAK,CAACS,UAAU,CAACC,GAAG,GAAG,CAAC,CAAC;IAC3DX,SAAS,CAACQ,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAEP,KAAK,CAACS,UAAU,CAACE,GAAG,GAAG,CAAC,CAAC;IAC9DZ,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAAClB,GAAG;QACX,IAAI,CAACmB,KAAK,CAAC,CAAC;QACZ,IAAI,CAAChB,KAAK,GAAG;UACXiB,MAAM,EAAE,EAAE;UACVhB,UAAU,EAAE,CAAC,CAAC;UACdC,MAAM,EAAE;QACV,CAAC;QACD;MACF;QACE,IAAI,CAACgB,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACkB,IAAI,CAACC,IAAI,CAAC;QACjC,IAAI,IAAI,CAACG,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACL,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAK,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACC,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACN,IAAI,EAAE;IACf,IAAI,IAAI,CAACG,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACG,UAAU,CAACN,IAAI,CAAC,EAAE;QACjC,IAAI,CAACG,MAAM,GAAGI,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQP,IAAI;MACV,KAAK,IAAI,CAAClB,GAAG;QACX,IAAI,CAAC0B,cAAc,CAAC,CAAC;QACrB,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAC5B,CAAC,CAAC,EACF,IAAI,CAAC9B,GAAG,CAAC,iBAAiB,CAAC,CAACI,KAAK,EACjC,IAAI,CAACJ,GAAG,CAAC,iBAAiB,CAAC,CAACI,KAC9B,CAAC;IACD,MAAM2B,GAAG,GAAGF,MAAM,CAACG,IAAI,CAACJ,QAAQ,CAAC,CAACK,MAAM;IACxC,IAAI,CAAC7B,KAAK,CAACE,MAAM,GAAGT,aAAa,CAACkC,GAAG,CAAC;IACtC,IAAI,CAAC3B,KAAK,CAACiB,MAAM,GAAG,IAAI,CAACrB,GAAG,CAAC,UAAU,CAAC,CAACwB,IAAI;IAC7C,IAAI,CAACpB,KAAK,CAACC,UAAU,CAACK,MAAM,GAAG,IAAI,CAACV,GAAG,CAAC,UAAU,CAAC,CAACwB,IAAI;IACxD,IAAI,CAACpB,KAAK,CAACC,UAAU,CAACO,QAAQ,GAAG,IAAI,CAACZ,GAAG,CAAC,YAAY,CAAC,CAACwB,IAAI;EAC9D;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGrC,kBAAkB"}