{"version": 3, "file": "dxf-xform.js", "names": ["BaseXform", "require", "AlignmentXform", "BorderXform", "FillXform", "FontXform", "NumFmtXform", "ProtectionXform", "DxfXform", "constructor", "map", "alignment", "border", "fill", "font", "numFmt", "protection", "tag", "render", "xmlStream", "model", "openNode", "numFmtId", "numFmtModel", "id", "formatCode", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "text", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/dxf-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst AlignmentXform = require('./alignment-xform');\nconst BorderXform = require('./border-xform');\nconst FillXform = require('./fill-xform');\nconst FontXform = require('./font-xform');\nconst NumFmtXform = require('./numfmt-xform');\nconst ProtectionXform = require('./protection-xform');\n\n// <xf numFmtId=\"[numFmtId]\" fontId=\"[fontId]\" fillId=\"[fillId]\" borderId=\"[xf.borderId]\" xfId=\"[xfId]\">\n//   Optional <alignment>\n//   Optional <protection>\n// </xf>\n\n// Style assists translation from style model to/from xlsx\nclass DxfXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      alignment: new AlignmentXform(),\n      border: new BorderXform(),\n      fill: new FillXform(),\n      font: new FontXform(),\n      numFmt: new NumFmtXform(),\n      protection: new ProtectionXform(),\n    };\n  }\n\n  get tag() {\n    return 'dxf';\n  }\n\n  // how do we generate dxfid?\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    if (model.font) {\n      this.map.font.render(xmlStream, model.font);\n    }\n    if (model.numFmt && model.numFmtId) {\n      const numFmtModel = {id: model.numFmtId, formatCode: model.numFmt};\n      this.map.numFmt.render(xmlStream, numFmtModel);\n    }\n    if (model.fill) {\n      this.map.fill.render(xmlStream, model.fill);\n    }\n    if (model.alignment) {\n      this.map.alignment.render(xmlStream, model.alignment);\n    }\n    if (model.border) {\n      this.map.border.render(xmlStream, model.border);\n    }\n    if (model.protection) {\n      this.map.protection.render(xmlStream, model.protection);\n    }\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case this.tag:\n        // this node is often repeated. Need to reset children\n        this.reset();\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        return true;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    if (name === this.tag) {\n      this.model = {\n        alignment: this.map.alignment.model,\n        border: this.map.border.model,\n        fill: this.map.fill.model,\n        font: this.map.font.model,\n        numFmt: this.map.numFmt.model,\n        protection: this.map.protection.model,\n      };\n      return false;\n    }\n\n    return true;\n  }\n}\n\nmodule.exports = DxfXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AACnD,MAAME,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMG,SAAS,GAAGH,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMI,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMK,WAAW,GAAGL,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMM,eAAe,GAAGN,OAAO,CAAC,oBAAoB,CAAC;;AAErD;AACA;AACA;AACA;;AAEA;AACA,MAAMO,QAAQ,SAASR,SAAS,CAAC;EAC/BS,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,SAAS,EAAE,IAAIT,cAAc,CAAC,CAAC;MAC/BU,MAAM,EAAE,IAAIT,WAAW,CAAC,CAAC;MACzBU,IAAI,EAAE,IAAIT,SAAS,CAAC,CAAC;MACrBU,IAAI,EAAE,IAAIT,SAAS,CAAC,CAAC;MACrBU,MAAM,EAAE,IAAIT,WAAW,CAAC,CAAC;MACzBU,UAAU,EAAE,IAAIT,eAAe,CAAC;IAClC,CAAC;EACH;EAEA,IAAIU,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;;EAEA;;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAE5B,IAAIG,KAAK,CAACN,IAAI,EAAE;MACd,IAAI,CAACJ,GAAG,CAACI,IAAI,CAACI,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACN,IAAI,CAAC;IAC7C;IACA,IAAIM,KAAK,CAACL,MAAM,IAAIK,KAAK,CAACE,QAAQ,EAAE;MAClC,MAAMC,WAAW,GAAG;QAACC,EAAE,EAAEJ,KAAK,CAACE,QAAQ;QAAEG,UAAU,EAAEL,KAAK,CAACL;MAAM,CAAC;MAClE,IAAI,CAACL,GAAG,CAACK,MAAM,CAACG,MAAM,CAACC,SAAS,EAAEI,WAAW,CAAC;IAChD;IACA,IAAIH,KAAK,CAACP,IAAI,EAAE;MACd,IAAI,CAACH,GAAG,CAACG,IAAI,CAACK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACP,IAAI,CAAC;IAC7C;IACA,IAAIO,KAAK,CAACT,SAAS,EAAE;MACnB,IAAI,CAACD,GAAG,CAACC,SAAS,CAACO,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACT,SAAS,CAAC;IACvD;IACA,IAAIS,KAAK,CAACR,MAAM,EAAE;MAChB,IAAI,CAACF,GAAG,CAACE,MAAM,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACR,MAAM,CAAC;IACjD;IACA,IAAIQ,KAAK,CAACJ,UAAU,EAAE;MACpB,IAAI,CAACN,GAAG,CAACM,UAAU,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACJ,UAAU,CAAC;IACzD;IAEAG,SAAS,CAACO,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACb,GAAG;QACX;QACA,IAAI,CAACc,KAAK,CAAC,CAAC;QACZ,OAAO,IAAI;MACb;QACE,IAAI,CAACF,MAAM,GAAG,IAAI,CAACnB,GAAG,CAACkB,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA,OAAO,IAAI;IACf;EACF;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,IAAIL,IAAI,KAAK,IAAI,CAACb,GAAG,EAAE;MACrB,IAAI,CAACG,KAAK,GAAG;QACXT,SAAS,EAAE,IAAI,CAACD,GAAG,CAACC,SAAS,CAACS,KAAK;QACnCR,MAAM,EAAE,IAAI,CAACF,GAAG,CAACE,MAAM,CAACQ,KAAK;QAC7BP,IAAI,EAAE,IAAI,CAACH,GAAG,CAACG,IAAI,CAACO,KAAK;QACzBN,IAAI,EAAE,IAAI,CAACJ,GAAG,CAACI,IAAI,CAACM,KAAK;QACzBL,MAAM,EAAE,IAAI,CAACL,GAAG,CAACK,MAAM,CAACK,KAAK;QAC7BJ,UAAU,EAAE,IAAI,CAACN,GAAG,CAACM,UAAU,CAACI;MAClC,CAAC;MACD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAG7B,QAAQ"}