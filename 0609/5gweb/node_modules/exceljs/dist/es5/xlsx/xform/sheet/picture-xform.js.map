{"version": 3, "file": "picture-xform.js", "names": ["BaseXform", "require", "PictureXform", "tag", "render", "xmlStream", "model", "leafNode", "rId", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/picture-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass PictureXform extends BaseXform {\n  get tag() {\n    return 'picture';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.leafNode(this.tag, {'r:id': model.rId});\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          rId: node.attributes['r:id'],\n        };\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PictureXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,YAAY,SAASF,SAAS,CAAC;EACnC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;QAAC,MAAM,EAAEG,KAAK,CAACE;MAAG,CAAC,CAAC;IACnD;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACR,GAAG;QACX,IAAI,CAACG,KAAK,GAAG;UACXE,GAAG,EAAEE,IAAI,CAACE,UAAU,CAAC,MAAM;QAC7B,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,YAAY"}