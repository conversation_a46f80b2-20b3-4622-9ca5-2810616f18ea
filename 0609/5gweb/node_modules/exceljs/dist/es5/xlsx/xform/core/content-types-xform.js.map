{"version": 3, "file": "content-types-xform.js", "names": ["XmlStream", "require", "BaseXform", "ContentTypesXform", "render", "xmlStream", "model", "openXml", "StdDocAttributes", "openNode", "PROPERTY_ATTRIBUTES", "mediaHash", "media", "for<PERSON>ach", "medium", "type", "imageType", "extension", "leafNode", "Extension", "ContentType", "PartName", "worksheets", "worksheet", "name", "id", "hasSharedStrings", "sharedStrings", "count", "tables", "table", "target", "drawings", "drawing", "commentRefs", "_ref", "commentName", "closeNode", "parseOpen", "parseText", "parseClose", "xmlns", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/content-types-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\n\n// used for rendering the [Content_Types].xml file\n// not used for parsing\nclass ContentTypesXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n\n    xmlStream.openNode('Types', ContentTypesXform.PROPERTY_ATTRIBUTES);\n\n    const mediaHash = {};\n    (model.media || []).forEach(medium => {\n      if (medium.type === 'image') {\n        const imageType = medium.extension;\n        if (!mediaHash[imageType]) {\n          mediaHash[imageType] = true;\n          xmlStream.leafNode('Default', {Extension: imageType, ContentType: `image/${imageType}`});\n        }\n      }\n    });\n\n    xmlStream.leafNode('Default', {\n      Extension: 'rels',\n      ContentType: 'application/vnd.openxmlformats-package.relationships+xml',\n    });\n    xmlStream.leafNode('Default', {Extension: 'xml', ContentType: 'application/xml'});\n\n    xmlStream.leafNode('Override', {\n      PartName: '/xl/workbook.xml',\n      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml',\n    });\n\n    model.worksheets.forEach(worksheet => {\n      const name = `/xl/worksheets/sheet${worksheet.id}.xml`;\n      xmlStream.leafNode('Override', {\n        PartName: name,\n        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml',\n      });\n    });\n\n    xmlStream.leafNode('Override', {\n      PartName: '/xl/theme/theme1.xml',\n      ContentType: 'application/vnd.openxmlformats-officedocument.theme+xml',\n    });\n    xmlStream.leafNode('Override', {\n      PartName: '/xl/styles.xml',\n      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml',\n    });\n\n    const hasSharedStrings = model.sharedStrings && model.sharedStrings.count;\n    if (hasSharedStrings) {\n      xmlStream.leafNode('Override', {\n        PartName: '/xl/sharedStrings.xml',\n        ContentType:\n          'application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml',\n      });\n    }\n\n    if (model.tables) {\n      model.tables.forEach(table => {\n        xmlStream.leafNode('Override', {\n          PartName: `/xl/tables/${table.target}`,\n          ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml',\n        });\n      });\n    }\n\n    if (model.drawings) {\n      model.drawings.forEach(drawing => {\n        xmlStream.leafNode('Override', {\n          PartName: `/xl/drawings/${drawing.name}.xml`,\n          ContentType: 'application/vnd.openxmlformats-officedocument.drawing+xml',\n        });\n      });\n    }\n\n    if (model.commentRefs) {\n      xmlStream.leafNode('Default', {\n        Extension: 'vml',\n        ContentType: 'application/vnd.openxmlformats-officedocument.vmlDrawing',\n      });\n\n      model.commentRefs.forEach(({commentName}) => {\n        xmlStream.leafNode('Override', {\n          PartName: `/xl/${commentName}.xml`,\n          ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml',\n        });\n      });\n    }\n\n    xmlStream.leafNode('Override', {\n      PartName: '/docProps/core.xml',\n      ContentType: 'application/vnd.openxmlformats-package.core-properties+xml',\n    });\n    xmlStream.leafNode('Override', {\n      PartName: '/docProps/app.xml',\n      ContentType: 'application/vnd.openxmlformats-officedocument.extended-properties+xml',\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen() {\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nContentTypesXform.PROPERTY_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/package/2006/content-types',\n};\n\nmodule.exports = ContentTypesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA,MAAME,iBAAiB,SAASD,SAAS,CAAC;EACxCE,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,OAAO,CAACP,SAAS,CAACQ,gBAAgB,CAAC;IAE7CH,SAAS,CAACI,QAAQ,CAAC,OAAO,EAAEN,iBAAiB,CAACO,mBAAmB,CAAC;IAElE,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,CAACL,KAAK,CAACM,KAAK,IAAI,EAAE,EAAEC,OAAO,CAACC,MAAM,IAAI;MACpC,IAAIA,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;QAC3B,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;QAClC,IAAI,CAACN,SAAS,CAACK,SAAS,CAAC,EAAE;UACzBL,SAAS,CAACK,SAAS,CAAC,GAAG,IAAI;UAC3BX,SAAS,CAACa,QAAQ,CAAC,SAAS,EAAE;YAACC,SAAS,EAAEH,SAAS;YAAEI,WAAW,EAAG,SAAQJ,SAAU;UAAC,CAAC,CAAC;QAC1F;MACF;IACF,CAAC,CAAC;IAEFX,SAAS,CAACa,QAAQ,CAAC,SAAS,EAAE;MAC5BC,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFf,SAAS,CAACa,QAAQ,CAAC,SAAS,EAAE;MAACC,SAAS,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAiB,CAAC,CAAC;IAEjFf,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;MAC7BG,QAAQ,EAAE,kBAAkB;MAC5BD,WAAW,EAAE;IACf,CAAC,CAAC;IAEFd,KAAK,CAACgB,UAAU,CAACT,OAAO,CAACU,SAAS,IAAI;MACpC,MAAMC,IAAI,GAAI,uBAAsBD,SAAS,CAACE,EAAG,MAAK;MACtDpB,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;QAC7BG,QAAQ,EAAEG,IAAI;QACdJ,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;MAC7BG,QAAQ,EAAE,sBAAsB;MAChCD,WAAW,EAAE;IACf,CAAC,CAAC;IACFf,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;MAC7BG,QAAQ,EAAE,gBAAgB;MAC1BD,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,MAAMM,gBAAgB,GAAGpB,KAAK,CAACqB,aAAa,IAAIrB,KAAK,CAACqB,aAAa,CAACC,KAAK;IACzE,IAAIF,gBAAgB,EAAE;MACpBrB,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;QAC7BG,QAAQ,EAAE,uBAAuB;QACjCD,WAAW,EACT;MACJ,CAAC,CAAC;IACJ;IAEA,IAAId,KAAK,CAACuB,MAAM,EAAE;MAChBvB,KAAK,CAACuB,MAAM,CAAChB,OAAO,CAACiB,KAAK,IAAI;QAC5BzB,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;UAC7BG,QAAQ,EAAG,cAAaS,KAAK,CAACC,MAAO,EAAC;UACtCX,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAId,KAAK,CAAC0B,QAAQ,EAAE;MAClB1B,KAAK,CAAC0B,QAAQ,CAACnB,OAAO,CAACoB,OAAO,IAAI;QAChC5B,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;UAC7BG,QAAQ,EAAG,gBAAeY,OAAO,CAACT,IAAK,MAAK;UAC5CJ,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAId,KAAK,CAAC4B,WAAW,EAAE;MACrB7B,SAAS,CAACa,QAAQ,CAAC,SAAS,EAAE;QAC5BC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE;MACf,CAAC,CAAC;MAEFd,KAAK,CAAC4B,WAAW,CAACrB,OAAO,CAACsB,IAAA,IAAmB;QAAA,IAAlB;UAACC;QAAW,CAAC,GAAAD,IAAA;QACtC9B,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;UAC7BG,QAAQ,EAAG,OAAMe,WAAY,MAAK;UAClChB,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAf,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;MAC7BG,QAAQ,EAAE,oBAAoB;MAC9BD,WAAW,EAAE;IACf,CAAC,CAAC;IACFf,SAAS,CAACa,QAAQ,CAAC,UAAU,EAAE;MAC7BG,QAAQ,EAAE,mBAAmB;MAC7BD,WAAW,EAAE;IACf,CAAC,CAAC;IAEFf,SAAS,CAACgC,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAAA,EAAG;IACV,OAAO,KAAK;EACd;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEArC,iBAAiB,CAACO,mBAAmB,GAAG;EACtC+B,KAAK,EAAE;AACT,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGxC,iBAAiB"}