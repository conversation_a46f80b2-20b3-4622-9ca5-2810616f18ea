{"version": 3, "file": "blip-fill-xform.js", "names": ["BaseXform", "require", "BlipXform", "BlipFillXform", "constructor", "map", "tag", "render", "xmlStream", "model", "openNode", "leafNode", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/blip-fill-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst BlipXform = require('./blip-xform');\n\nclass BlipFillXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'a:blip': new BlipXform(),\n    };\n  }\n\n  get tag() {\n    return 'xdr:blipFill';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    this.map['a:blip'].render(xmlStream, model);\n\n    // TODO: options for this + parsing\n    xmlStream.openNode('a:stretch');\n    xmlStream.leafNode('a:fillRect');\n    xmlStream.closeNode();\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        break;\n\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model = this.map['a:blip'].model;\n        return false;\n\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = BlipFillXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAME,aAAa,SAASH,SAAS,CAAC;EACpCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,QAAQ,EAAE,IAAIH,SAAS,CAAC;IAC1B,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAE5B,IAAI,CAACD,GAAG,CAAC,QAAQ,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;;IAE3C;IACAD,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC;IAC/BF,SAAS,CAACG,QAAQ,CAAC,YAAY,CAAC;IAChCH,SAAS,CAACI,SAAS,CAAC,CAAC;IAErBJ,SAAS,CAACI,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACV,GAAG;QACX,IAAI,CAACW,KAAK,CAAC,CAAC;QACZ;MAEF;QACE,IAAI,CAACF,MAAM,GAAG,IAAI,CAACV,GAAG,CAACS,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGK,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQJ,IAAI;MACV,KAAK,IAAI,CAACV,GAAG;QACX,IAAI,CAACG,KAAK,GAAG,IAAI,CAACJ,GAAG,CAAC,QAAQ,CAAC,CAACI,KAAK;QACrC,OAAO,KAAK;MAEd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAY,MAAM,CAACC,OAAO,GAAGnB,aAAa"}