{"version": 3, "file": "color-scale-xform.js", "names": ["CompositeXform", "require", "ColorXform", "CfvoXform", "ColorScaleXform", "constructor", "map", "cfvo", "cfvoXform", "color", "colorXform", "tag", "render", "xmlStream", "model", "openNode", "for<PERSON>ach", "closeNode", "createNewModel", "node", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/color-scale-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst ColorXform = require('../../style/color-xform');\nconst CfvoXform = require('./cfvo-xform');\n\nclass ColorScaleXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      cfvo: (this.cfvoXform = new CfvoXform()),\n      color: (this.colorXform = new ColorXform()),\n    };\n  }\n\n  get tag() {\n    return 'colorScale';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    model.cfvo.forEach(cfvo => {\n      this.cfvoXform.render(xmlStream, cfvo);\n    });\n    model.color.forEach(color => {\n      this.colorXform.render(xmlStream, color);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel(node) {\n    return {\n      cfvo: [],\n      color: [],\n    };\n  }\n\n  onParserClose(name, parser) {\n    this.model[name].push(parser.model);\n  }\n}\n\nmodule.exports = ColorScaleXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,UAAU,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAMG,eAAe,SAASJ,cAAc,CAAC;EAC3CK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,IAAI,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIL,SAAS,CAAC,CAAE;MACxCM,KAAK,EAAG,IAAI,CAACC,UAAU,GAAG,IAAIR,UAAU,CAAC;IAC3C,CAAC;EACH;EAEA,IAAIS,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAE5BG,KAAK,CAACP,IAAI,CAACS,OAAO,CAACT,IAAI,IAAI;MACzB,IAAI,CAACC,SAAS,CAACI,MAAM,CAACC,SAAS,EAAEN,IAAI,CAAC;IACxC,CAAC,CAAC;IACFO,KAAK,CAACL,KAAK,CAACO,OAAO,CAACP,KAAK,IAAI;MAC3B,IAAI,CAACC,UAAU,CAACE,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAC1C,CAAC,CAAC;IAEFI,SAAS,CAACI,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAACC,IAAI,EAAE;IACnB,OAAO;MACLZ,IAAI,EAAE,EAAE;MACRE,KAAK,EAAE;IACT,CAAC;EACH;EAEAW,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACR,KAAK,CAACO,IAAI,CAAC,CAACE,IAAI,CAACD,MAAM,CAACR,KAAK,CAAC;EACrC;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGrB,eAAe"}