{"version": 3, "file": "c-nv-pr-xform.js", "names": ["BaseXform", "require", "HlickClickXform", "ExtLstXform", "CNvPrXform", "constructor", "map", "tag", "render", "xmlStream", "model", "openNode", "id", "index", "name", "closeNode", "parseOpen", "node", "parser", "reset", "parseText", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/c-nv-pr-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst HlickClickXform = require('./hlink-click-xform');\nconst ExtLstXform = require('./ext-lst-xform');\n\nclass CNvPrXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'a:hlinkClick': new HlickClickXform(),\n      'a:extLst': new ExtLstXform(),\n    };\n  }\n\n  get tag() {\n    return 'xdr:cNvPr';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      id: model.index,\n      name: `Picture ${model.index}`,\n    });\n    this.map['a:hlinkClick'].render(xmlStream, model);\n    this.map['a:extLst'].render(xmlStream, model);\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model = this.map['a:hlinkClick'].model;\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = CNvPrXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,eAAe,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAME,WAAW,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAE9C,MAAMG,UAAU,SAASJ,SAAS,CAAC;EACjCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,cAAc,EAAE,IAAIJ,eAAe,CAAC,CAAC;MACrC,UAAU,EAAE,IAAIC,WAAW,CAAC;IAC9B,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,EAAE,EAAEF,KAAK,CAACG,KAAK;MACfC,IAAI,EAAG,WAAUJ,KAAK,CAACG,KAAM;IAC/B,CAAC,CAAC;IACF,IAAI,CAACP,GAAG,CAAC,cAAc,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IACjD,IAAI,CAACJ,GAAG,CAAC,UAAU,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IAC7CD,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACH,IAAI;MACf,KAAK,IAAI,CAACP,GAAG;QACX,IAAI,CAACY,KAAK,CAAC,CAAC;QACZ;MACF;QACE,IAAI,CAACD,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACW,IAAI,CAACH,IAAI,CAAC;QACjC,IAAI,IAAI,CAACI,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAG,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACP,IAAI,EAAE;IACf,IAAI,IAAI,CAACI,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACG,UAAU,CAACP,IAAI,CAAC,EAAE;QACjC,IAAI,CAACI,MAAM,GAAGI,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQR,IAAI;MACV,KAAK,IAAI,CAACP,GAAG;QACX,IAAI,CAACG,KAAK,GAAG,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACI,KAAK;QAC3C,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAa,MAAM,CAACC,OAAO,GAAGpB,UAAU"}