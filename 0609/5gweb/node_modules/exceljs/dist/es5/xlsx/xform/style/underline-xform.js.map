{"version": 3, "file": "underline-xform.js", "names": ["BaseXform", "require", "UnderlineXform", "constructor", "model", "tag", "render", "xmlStream", "leafNode", "attr", "Attributes", "parseOpen", "node", "name", "attributes", "val", "parseText", "parseClose", "single", "double", "singleAccounting", "doubleAccounting", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/underline-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass UnderlineXform extends BaseXform {\n  constructor(model) {\n    super();\n\n    this.model = model;\n  }\n\n  get tag() {\n    return 'u';\n  }\n\n  render(xmlStream, model) {\n    model = model || this.model;\n\n    if (model === true) {\n      xmlStream.leafNode('u');\n    } else {\n      const attr = UnderlineXform.Attributes[model];\n      if (attr) {\n        xmlStream.leafNode('u', attr);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === 'u') {\n      this.model = node.attributes.val || true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nUnderlineXform.Attributes = {\n  single: {},\n  double: {val: 'double'},\n  singleAccounting: {val: 'singleAccounting'},\n  doubleAccounting: {val: 'doubleAccounting'},\n};\n\nmodule.exports = UnderlineXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrCG,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,GAAG;EACZ;EAEAC,MAAMA,CAACC,SAAS,EAAEH,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK;IAE3B,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBG,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC;IACzB,CAAC,MAAM;MACL,MAAMC,IAAI,GAAGP,cAAc,CAACQ,UAAU,CAACN,KAAK,CAAC;MAC7C,IAAIK,IAAI,EAAE;QACRF,SAAS,CAACC,QAAQ,CAAC,GAAG,EAAEC,IAAI,CAAC;MAC/B;IACF;EACF;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;MACrB,IAAI,CAACT,KAAK,GAAGQ,IAAI,CAACE,UAAU,CAACC,GAAG,IAAI,IAAI;IAC1C;EACF;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAf,cAAc,CAACQ,UAAU,GAAG;EAC1BQ,MAAM,EAAE,CAAC,CAAC;EACVC,MAAM,EAAE;IAACJ,GAAG,EAAE;EAAQ,CAAC;EACvBK,gBAAgB,EAAE;IAACL,GAAG,EAAE;EAAkB,CAAC;EAC3CM,gBAAgB,EAAE;IAACN,GAAG,EAAE;EAAkB;AAC5C,CAAC;AAEDO,MAAM,CAACC,OAAO,GAAGrB,cAAc"}