{"version": 3, "file": "merges.js", "names": ["_", "require", "Range", "co<PERSON><PERSON><PERSON>", "Enums", "<PERSON><PERSON>", "constructor", "merges", "add", "merge", "master", "expandToAddress", "address", "range", "mergeCells", "map", "reconcile", "rows", "each", "dimensions", "decode", "i", "top", "bottom", "row", "j", "left", "right", "cell", "cells", "type", "ValueType", "<PERSON><PERSON>", "encodeAddress", "<PERSON><PERSON>", "tl", "getMasterAddress", "hash", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/merges.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\n\nconst Range = require('../../../doc/range');\nconst colCache = require('../../../utils/col-cache');\nconst Enums = require('../../../doc/enums');\n\nclass Merges {\n  constructor() {\n    // optional mergeCells is array of ranges (like the xml)\n    this.merges = {};\n  }\n\n  add(merge) {\n    // merge is {address, master}\n    if (this.merges[merge.master]) {\n      this.merges[merge.master].expandToAddress(merge.address);\n    } else {\n      const range = `${merge.master}:${merge.address}`;\n      this.merges[merge.master] = new Range(range);\n    }\n  }\n\n  get mergeCells() {\n    return _.map(this.merges, merge => merge.range);\n  }\n\n  reconcile(mergeCells, rows) {\n    // reconcile merge list with merge cells\n    _.each(mergeCells, merge => {\n      const dimensions = colCache.decode(merge);\n      for (let i = dimensions.top; i <= dimensions.bottom; i++) {\n        const row = rows[i - 1];\n        for (let j = dimensions.left; j <= dimensions.right; j++) {\n          const cell = row.cells[j - 1];\n          if (!cell) {\n            // nulls are not included in document - so if master cell has no value - add a null one here\n            row.cells[j] = {\n              type: Enums.ValueType.Null,\n              address: colCache.encodeAddress(i, j),\n            };\n          } else if (cell.type === Enums.ValueType.Merge) {\n            cell.master = dimensions.tl;\n          }\n        }\n      }\n    });\n  }\n\n  getMasterAddress(address) {\n    // if address has been merged, return its master's address. Assumes reconcile has been called\n    const range = this.hash[address];\n    return range && range.tl;\n  }\n}\n\nmodule.exports = Merges;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAE9C,MAAMC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC3C,MAAME,QAAQ,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMG,KAAK,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAE3C,MAAMI,MAAM,CAAC;EACXC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAClB;EAEAC,GAAGA,CAACC,KAAK,EAAE;IACT;IACA,IAAI,IAAI,CAACF,MAAM,CAACE,KAAK,CAACC,MAAM,CAAC,EAAE;MAC7B,IAAI,CAACH,MAAM,CAACE,KAAK,CAACC,MAAM,CAAC,CAACC,eAAe,CAACF,KAAK,CAACG,OAAO,CAAC;IAC1D,CAAC,MAAM;MACL,MAAMC,KAAK,GAAI,GAAEJ,KAAK,CAACC,MAAO,IAAGD,KAAK,CAACG,OAAQ,EAAC;MAChD,IAAI,CAACL,MAAM,CAACE,KAAK,CAACC,MAAM,CAAC,GAAG,IAAIR,KAAK,CAACW,KAAK,CAAC;IAC9C;EACF;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAOd,CAAC,CAACe,GAAG,CAAC,IAAI,CAACR,MAAM,EAAEE,KAAK,IAAIA,KAAK,CAACI,KAAK,CAAC;EACjD;EAEAG,SAASA,CAACF,UAAU,EAAEG,IAAI,EAAE;IAC1B;IACAjB,CAAC,CAACkB,IAAI,CAACJ,UAAU,EAAEL,KAAK,IAAI;MAC1B,MAAMU,UAAU,GAAGhB,QAAQ,CAACiB,MAAM,CAACX,KAAK,CAAC;MACzC,KAAK,IAAIY,CAAC,GAAGF,UAAU,CAACG,GAAG,EAAED,CAAC,IAAIF,UAAU,CAACI,MAAM,EAAEF,CAAC,EAAE,EAAE;QACxD,MAAMG,GAAG,GAAGP,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC;QACvB,KAAK,IAAII,CAAC,GAAGN,UAAU,CAACO,IAAI,EAAED,CAAC,IAAIN,UAAU,CAACQ,KAAK,EAAEF,CAAC,EAAE,EAAE;UACxD,MAAMG,IAAI,GAAGJ,GAAG,CAACK,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC;UAC7B,IAAI,CAACG,IAAI,EAAE;YACT;YACAJ,GAAG,CAACK,KAAK,CAACJ,CAAC,CAAC,GAAG;cACbK,IAAI,EAAE1B,KAAK,CAAC2B,SAAS,CAACC,IAAI;cAC1BpB,OAAO,EAAET,QAAQ,CAAC8B,aAAa,CAACZ,CAAC,EAAEI,CAAC;YACtC,CAAC;UACH,CAAC,MAAM,IAAIG,IAAI,CAACE,IAAI,KAAK1B,KAAK,CAAC2B,SAAS,CAACG,KAAK,EAAE;YAC9CN,IAAI,CAAClB,MAAM,GAAGS,UAAU,CAACgB,EAAE;UAC7B;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAACxB,OAAO,EAAE;IACxB;IACA,MAAMC,KAAK,GAAG,IAAI,CAACwB,IAAI,CAACzB,OAAO,CAAC;IAChC,OAAOC,KAAK,IAAIA,KAAK,CAACsB,EAAE;EAC1B;AACF;AAEAG,MAAM,CAACC,OAAO,GAAGlC,MAAM"}