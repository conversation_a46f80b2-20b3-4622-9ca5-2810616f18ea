{"version": 3, "file": "custom-filter-xform.js", "names": ["BaseXform", "require", "CustomFilterXform", "tag", "render", "xmlStream", "model", "leafNode", "val", "operator", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/custom-filter-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass CustomFilterXform extends BaseXform {\n  get tag() {\n    return 'customFilter';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      val: model.val,\n      operator: model.operator,\n    });\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = {\n        val: node.attributes.val,\n        operator: node.attributes.operator,\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = CustomFilterXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,iBAAiB,SAASF,SAAS,CAAC;EACxC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,GAAG,EAAEF,KAAK,CAACE,GAAG;MACdC,QAAQ,EAAEH,KAAK,CAACG;IAClB,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACT,GAAG,EAAE;MAC1B,IAAI,CAACG,KAAK,GAAG;QACXE,GAAG,EAAEG,IAAI,CAACE,UAAU,CAACL,GAAG;QACxBC,QAAQ,EAAEE,IAAI,CAACE,UAAU,CAACJ;MAC5B,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGf,iBAAiB"}