{"version": 3, "file": "col-xform.js", "names": ["utils", "require", "BaseXform", "ColXform", "tag", "prepare", "model", "options", "styleId", "styles", "addStyleModel", "style", "render", "xmlStream", "openNode", "addAttribute", "min", "max", "width", "hidden", "bestFit", "outlineLevel", "collapsed", "closeNode", "parseOpen", "node", "name", "parseInt", "attributes", "undefined", "parseFloat", "parseBoolean", "parseText", "parseClose", "reconcile", "getStyleModel", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/col-xform.js"], "sourcesContent": ["const utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\n\nclass ColXform extends BaseXform {\n  get tag() {\n    return 'col';\n  }\n\n  prepare(model, options) {\n    const styleId = options.styles.addStyleModel(model.style || {});\n    if (styleId) {\n      model.styleId = styleId;\n    }\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('col');\n    xmlStream.addAttribute('min', model.min);\n    xmlStream.addAttribute('max', model.max);\n    if (model.width) {\n      xmlStream.addAttribute('width', model.width);\n    }\n    if (model.styleId) {\n      xmlStream.addAttribute('style', model.styleId);\n    }\n    if (model.hidden) {\n      xmlStream.addAttribute('hidden', '1');\n    }\n    if (model.bestFit) {\n      xmlStream.addAttribute('bestFit', '1');\n    }\n    if (model.outlineLevel) {\n      xmlStream.addAttribute('outlineLevel', model.outlineLevel);\n    }\n    if (model.collapsed) {\n      xmlStream.addAttribute('collapsed', '1');\n    }\n    xmlStream.addAttribute('customWidth', '1');\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (node.name === 'col') {\n      const model = (this.model = {\n        min: parseInt(node.attributes.min || '0', 10),\n        max: parseInt(node.attributes.max || '0', 10),\n        width:\n          node.attributes.width === undefined\n            ? undefined\n            : parseFloat(node.attributes.width || '0'),\n      });\n      if (node.attributes.style) {\n        model.styleId = parseInt(node.attributes.style, 10);\n      }\n      if (utils.parseBoolean(node.attributes.hidden)) {\n        model.hidden = true;\n      }\n      if (utils.parseBoolean(node.attributes.bestFit)) {\n        model.bestFit = true;\n      }\n      if (node.attributes.outlineLevel) {\n        model.outlineLevel = parseInt(node.attributes.outlineLevel, 10);\n      }\n      if (utils.parseBoolean(node.attributes.collapsed)) {\n        model.collapsed = true;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n\n  reconcile(model, options) {\n    // reconcile column styles\n    if (model.styleId) {\n      model.style = options.styles.getStyleModel(model.styleId);\n    }\n  }\n}\n\nmodule.exports = ColXform;\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,QAAQ,SAASD,SAAS,CAAC;EAC/B,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtB,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAACC,aAAa,CAACJ,KAAK,CAACK,KAAK,IAAI,CAAC,CAAC,CAAC;IAC/D,IAAIH,OAAO,EAAE;MACXF,KAAK,CAACE,OAAO,GAAGA,OAAO;IACzB;EACF;EAEAI,MAAMA,CAACC,SAAS,EAAEP,KAAK,EAAE;IACvBO,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;IACzBD,SAAS,CAACE,YAAY,CAAC,KAAK,EAAET,KAAK,CAACU,GAAG,CAAC;IACxCH,SAAS,CAACE,YAAY,CAAC,KAAK,EAAET,KAAK,CAACW,GAAG,CAAC;IACxC,IAAIX,KAAK,CAACY,KAAK,EAAE;MACfL,SAAS,CAACE,YAAY,CAAC,OAAO,EAAET,KAAK,CAACY,KAAK,CAAC;IAC9C;IACA,IAAIZ,KAAK,CAACE,OAAO,EAAE;MACjBK,SAAS,CAACE,YAAY,CAAC,OAAO,EAAET,KAAK,CAACE,OAAO,CAAC;IAChD;IACA,IAAIF,KAAK,CAACa,MAAM,EAAE;MAChBN,SAAS,CAACE,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IACvC;IACA,IAAIT,KAAK,CAACc,OAAO,EAAE;MACjBP,SAAS,CAACE,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC;IACxC;IACA,IAAIT,KAAK,CAACe,YAAY,EAAE;MACtBR,SAAS,CAACE,YAAY,CAAC,cAAc,EAAET,KAAK,CAACe,YAAY,CAAC;IAC5D;IACA,IAAIf,KAAK,CAACgB,SAAS,EAAE;MACnBT,SAAS,CAACE,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IAC1C;IACAF,SAAS,CAACE,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;IAC1CF,SAAS,CAACU,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;MACvB,MAAMpB,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG;QAC1BU,GAAG,EAAEW,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACZ,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;QAC7CC,GAAG,EAAEU,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACX,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;QAC7CC,KAAK,EACHO,IAAI,CAACG,UAAU,CAACV,KAAK,KAAKW,SAAS,GAC/BA,SAAS,GACTC,UAAU,CAACL,IAAI,CAACG,UAAU,CAACV,KAAK,IAAI,GAAG;MAC/C,CAAE;MACF,IAAIO,IAAI,CAACG,UAAU,CAACjB,KAAK,EAAE;QACzBL,KAAK,CAACE,OAAO,GAAGmB,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACjB,KAAK,EAAE,EAAE,CAAC;MACrD;MACA,IAAIX,KAAK,CAAC+B,YAAY,CAACN,IAAI,CAACG,UAAU,CAACT,MAAM,CAAC,EAAE;QAC9Cb,KAAK,CAACa,MAAM,GAAG,IAAI;MACrB;MACA,IAAInB,KAAK,CAAC+B,YAAY,CAACN,IAAI,CAACG,UAAU,CAACR,OAAO,CAAC,EAAE;QAC/Cd,KAAK,CAACc,OAAO,GAAG,IAAI;MACtB;MACA,IAAIK,IAAI,CAACG,UAAU,CAACP,YAAY,EAAE;QAChCf,KAAK,CAACe,YAAY,GAAGM,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACP,YAAY,EAAE,EAAE,CAAC;MACjE;MACA,IAAIrB,KAAK,CAAC+B,YAAY,CAACN,IAAI,CAACG,UAAU,CAACN,SAAS,CAAC,EAAE;QACjDhB,KAAK,CAACgB,SAAS,GAAG,IAAI;MACxB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;EAEAC,SAASA,CAAC5B,KAAK,EAAEC,OAAO,EAAE;IACxB;IACA,IAAID,KAAK,CAACE,OAAO,EAAE;MACjBF,KAAK,CAACK,KAAK,GAAGJ,OAAO,CAACE,MAAM,CAAC0B,aAAa,CAAC7B,KAAK,CAACE,OAAO,CAAC;IAC3D;EACF;AACF;AAEA4B,MAAM,CAACC,OAAO,GAAGlC,QAAQ"}