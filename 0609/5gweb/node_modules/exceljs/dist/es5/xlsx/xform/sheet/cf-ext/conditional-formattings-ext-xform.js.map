{"version": 3, "file": "conditional-formattings-ext-xform.js", "names": ["CompositeXform", "require", "CfRuleExtXform", "ConditionalFormattingExtXform", "ConditionalFormattingsExtXform", "constructor", "map", "cfXform", "tag", "<PERSON><PERSON><PERSON><PERSON>", "model", "hasExtContent", "undefined", "some", "cf", "rules", "isExt", "prepare", "options", "for<PERSON>ach", "render", "xmlStream", "openNode", "closeNode", "createNewModel", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/conditional-formattings-ext-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst CfRuleExtXform = require('./cf-rule-ext-xform');\nconst ConditionalFormattingExtXform = require('./conditional-formatting-ext-xform');\n\nclass ConditionalFormattingsExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'x14:conditionalFormatting': (this.cfXform = new ConditionalFormattingExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'x14:conditionalFormattings';\n  }\n\n  hasContent(model) {\n    if (model.hasExtContent === undefined) {\n      model.hasExtContent = model.some(cf => cf.rules.some(CfRuleExtXform.isExt));\n    }\n    return model.hasExtContent;\n  }\n\n  prepare(model, options) {\n    model.forEach(cf => {\n      this.cfXform.prepare(cf, options);\n    });\n  }\n\n  render(xmlStream, model) {\n    if (this.hasContent(model)) {\n      xmlStream.openNode(this.tag);\n      model.forEach(cf => this.cfXform.render(xmlStream, cf));\n      xmlStream.closeNode();\n    }\n  }\n\n  createNewModel() {\n    return [];\n  }\n\n  onParserClose(name, parser) {\n    // model is array of conditional formatting objects\n    this.model.push(parser.model);\n  }\n}\n\nmodule.exports = ConditionalFormattingsExtXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,cAAc,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AACrD,MAAME,6BAA6B,GAAGF,OAAO,CAAC,oCAAoC,CAAC;AAEnF,MAAMG,8BAA8B,SAASJ,cAAc,CAAC;EAC1DK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,2BAA2B,EAAG,IAAI,CAACC,OAAO,GAAG,IAAIJ,6BAA6B,CAAC;IACjF,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,4BAA4B;EACrC;EAEAC,UAAUA,CAACC,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACC,aAAa,KAAKC,SAAS,EAAE;MACrCF,KAAK,CAACC,aAAa,GAAGD,KAAK,CAACG,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,KAAK,CAACF,IAAI,CAACX,cAAc,CAACc,KAAK,CAAC,CAAC;IAC7E;IACA,OAAON,KAAK,CAACC,aAAa;EAC5B;EAEAM,OAAOA,CAACP,KAAK,EAAEQ,OAAO,EAAE;IACtBR,KAAK,CAACS,OAAO,CAACL,EAAE,IAAI;MAClB,IAAI,CAACP,OAAO,CAACU,OAAO,CAACH,EAAE,EAAEI,OAAO,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAE,MAAMA,CAACC,SAAS,EAAEX,KAAK,EAAE;IACvB,IAAI,IAAI,CAACD,UAAU,CAACC,KAAK,CAAC,EAAE;MAC1BW,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACd,GAAG,CAAC;MAC5BE,KAAK,CAACS,OAAO,CAACL,EAAE,IAAI,IAAI,CAACP,OAAO,CAACa,MAAM,CAACC,SAAS,EAAEP,EAAE,CAAC,CAAC;MACvDO,SAAS,CAACE,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO,EAAE;EACX;EAEAC,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B;IACA,IAAI,CAACjB,KAAK,CAACkB,IAAI,CAACD,MAAM,CAACjB,KAAK,CAAC;EAC/B;AACF;AAEAmB,MAAM,CAACC,OAAO,GAAG1B,8BAA8B"}