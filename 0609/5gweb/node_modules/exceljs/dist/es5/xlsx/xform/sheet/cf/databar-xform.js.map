{"version": 3, "file": "databar-xform.js", "names": ["CompositeXform", "require", "ColorXform", "CfvoXform", "DatabarXform", "constructor", "map", "cfvo", "cfvoXform", "color", "colorXform", "tag", "render", "xmlStream", "model", "openNode", "for<PERSON>ach", "closeNode", "createNewModel", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/databar-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst ColorXform = require('../../style/color-xform');\nconst CfvoXform = require('./cfvo-xform');\n\nclass DatabarXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      cfvo: (this.cfvoXform = new CfvoXform()),\n      color: (this.colorXform = new ColorXform()),\n    };\n  }\n\n  get tag() {\n    return 'dataBar';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    model.cfvo.forEach(cfvo => {\n      this.cfvoXform.render(xmlStream, cfvo);\n    });\n    this.colorXform.render(xmlStream, model.color);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {\n      cfvo: [],\n    };\n  }\n\n  onParserClose(name, parser) {\n    switch (name) {\n      case 'cfvo':\n        this.model.cfvo.push(parser.model);\n        break;\n      case 'color':\n        this.model.color = parser.model;\n        break;\n    }\n  }\n}\n\nmodule.exports = DatabarXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,UAAU,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAMG,YAAY,SAASJ,cAAc,CAAC;EACxCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,IAAI,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIL,SAAS,CAAC,CAAE;MACxCM,KAAK,EAAG,IAAI,CAACC,UAAU,GAAG,IAAIR,UAAU,CAAC;IAC3C,CAAC;EACH;EAEA,IAAIS,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAE5BG,KAAK,CAACP,IAAI,CAACS,OAAO,CAACT,IAAI,IAAI;MACzB,IAAI,CAACC,SAAS,CAACI,MAAM,CAACC,SAAS,EAAEN,IAAI,CAAC;IACxC,CAAC,CAAC;IACF,IAAI,CAACG,UAAU,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACL,KAAK,CAAC;IAE9CI,SAAS,CAACI,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO;MACLX,IAAI,EAAE;IACR,CAAC;EACH;EAEAY,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,QAAQD,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACN,KAAK,CAACP,IAAI,CAACe,IAAI,CAACD,MAAM,CAACP,KAAK,CAAC;QAClC;MACF,KAAK,OAAO;QACV,IAAI,CAACA,KAAK,CAACL,KAAK,GAAGY,MAAM,CAACP,KAAK;QAC/B;IACJ;EACF;AACF;AAEAS,MAAM,CAACC,OAAO,GAAGpB,YAAY"}