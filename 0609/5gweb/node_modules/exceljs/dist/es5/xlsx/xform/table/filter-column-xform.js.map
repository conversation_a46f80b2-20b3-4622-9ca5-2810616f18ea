{"version": 3, "file": "filter-column-xform.js", "names": ["BaseXform", "require", "ListXform", "CustomFilterXform", "FilterXform", "FilterColumnXform", "constructor", "map", "customFilters", "tag", "count", "empty", "childXform", "filters", "prepare", "model", "options", "colId", "index", "toString", "render", "xmlStream", "openNode", "hiddenButton", "filterButton", "closeNode", "leafNode", "parseOpen", "node", "parser", "attributes", "name", "Error", "JSON", "stringify", "parseText", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/filter-column-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst ListXform = require('../list-xform');\n\nconst CustomFilterXform = require('./custom-filter-xform');\nconst FilterXform = require('./filter-xform');\n\nclass FilterColumnXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      customFilters: new ListXform({\n        tag: 'customFilters',\n        count: false,\n        empty: true,\n        childXform: new CustomFilterXform(),\n      }),\n      filters: new ListXform({\n        tag: 'filters',\n        count: false,\n        empty: true,\n        childXform: new FilterXform(),\n      }),\n    };\n  }\n\n  get tag() {\n    return 'filterColumn';\n  }\n\n  prepare(model, options) {\n    model.colId = options.index.toString();\n  }\n\n  render(xmlStream, model) {\n    if (model.customFilters) {\n      xmlStream.openNode(this.tag, {\n        colId: model.colId,\n        hiddenButton: model.filterButton ? '0' : '1',\n      });\n\n      this.map.customFilters.render(xmlStream, model.customFilters);\n\n      xmlStream.closeNode();\n      return true;\n    }\n    xmlStream.leafNode(this.tag, {\n      colId: model.colId,\n      hiddenButton: model.filterButton ? '0' : '1',\n    });\n    return true;\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    const {attributes} = node;\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          filterButton: attributes.hiddenButton === '0',\n        };\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parseOpen(node);\n          return true;\n        }\n        throw new Error(`Unexpected xml node in parseOpen: ${JSON.stringify(node)}`);\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model.customFilters = this.map.customFilters.model;\n        return false;\n      default:\n        // could be some unrecognised tags\n        return true;\n    }\n  }\n}\n\nmodule.exports = FilterColumnXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,iBAAiB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC1D,MAAMG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAE7C,MAAMI,iBAAiB,SAASL,SAAS,CAAC;EACxCM,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,aAAa,EAAE,IAAIN,SAAS,CAAC;QAC3BO,GAAG,EAAE,eAAe;QACpBC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAIT,iBAAiB,CAAC;MACpC,CAAC,CAAC;MACFU,OAAO,EAAE,IAAIX,SAAS,CAAC;QACrBO,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAIR,WAAW,CAAC;MAC9B,CAAC;IACH,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAK,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtBD,KAAK,CAACE,KAAK,GAAGD,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAC,CAAC;EACxC;EAEAC,MAAMA,CAACC,SAAS,EAAEN,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACP,aAAa,EAAE;MACvBa,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACb,GAAG,EAAE;QAC3BQ,KAAK,EAAEF,KAAK,CAACE,KAAK;QAClBM,YAAY,EAAER,KAAK,CAACS,YAAY,GAAG,GAAG,GAAG;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACjB,GAAG,CAACC,aAAa,CAACY,MAAM,CAACC,SAAS,EAAEN,KAAK,CAACP,aAAa,CAAC;MAE7Da,SAAS,CAACI,SAAS,CAAC,CAAC;MACrB,OAAO,IAAI;IACb;IACAJ,SAAS,CAACK,QAAQ,CAAC,IAAI,CAACjB,GAAG,EAAE;MAC3BQ,KAAK,EAAEF,KAAK,CAACE,KAAK;MAClBM,YAAY,EAAER,KAAK,CAACS,YAAY,GAAG,GAAG,GAAG;IAC3C,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,MAAM;MAACE;IAAU,CAAC,GAAGF,IAAI;IACzB,QAAQA,IAAI,CAACG,IAAI;MACf,KAAK,IAAI,CAACtB,GAAG;QACX,IAAI,CAACM,KAAK,GAAG;UACXS,YAAY,EAAEM,UAAU,CAACP,YAAY,KAAK;QAC5C,CAAC;QACD,OAAO,IAAI;MACb;QACE,IAAI,CAACM,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACqB,IAAI,CAACG,IAAI,CAAC;QACjC,IAAI,IAAI,CAACF,MAAM,EAAE;UACf,IAAI,CAACF,SAAS,CAACC,IAAI,CAAC;UACpB,OAAO,IAAI;QACb;QACA,MAAM,IAAII,KAAK,CAAE,qCAAoCC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAE,EAAC,CAAC;IAChF;EACF;EAEAO,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACL,IAAI,EAAE;IACf,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACO,UAAU,CAACL,IAAI,CAAC,EAAE;QACjC,IAAI,CAACF,MAAM,GAAGQ,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQN,IAAI;MACV,KAAK,IAAI,CAACtB,GAAG;QACX,IAAI,CAACM,KAAK,CAACP,aAAa,GAAG,IAAI,CAACD,GAAG,CAACC,aAAa,CAACO,KAAK;QACvD,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAuB,MAAM,CAACC,OAAO,GAAGlC,iBAAiB"}