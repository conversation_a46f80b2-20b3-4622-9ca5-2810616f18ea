{"version": 3, "file": "integer-xform.js", "names": ["BaseXform", "require", "IntegerXform", "constructor", "options", "tag", "attr", "attrs", "zero", "render", "xmlStream", "model", "openNode", "addAttributes", "addAttribute", "writeText", "closeNode", "parseOpen", "node", "name", "parseInt", "attributes", "text", "parseText", "push", "parseClose", "join", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/simple/integer-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass IntegerXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.attr = options.attr;\n    this.attrs = options.attrs;\n\n    // option to render zero\n    this.zero = options.zero;\n  }\n\n  render(xmlStream, model) {\n    // int is different to float in that zero is not rendered\n    if (model || this.zero) {\n      xmlStream.openNode(this.tag);\n      if (this.attrs) {\n        xmlStream.addAttributes(this.attrs);\n      }\n      if (this.attr) {\n        xmlStream.addAttribute(this.attr, model);\n      } else {\n        xmlStream.writeText(model);\n      }\n      xmlStream.closeNode();\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      if (this.attr) {\n        this.model = parseInt(node.attributes[this.attr], 10);\n      } else {\n        this.text = [];\n      }\n      return true;\n    }\n    return false;\n  }\n\n  parseText(text) {\n    if (!this.attr) {\n      this.text.push(text);\n    }\n  }\n\n  parseClose() {\n    if (!this.attr) {\n      this.model = parseInt(this.text.join('') || 0, 10);\n    }\n    return false;\n  }\n}\n\nmodule.exports = IntegerXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,YAAY,SAASF,SAAS,CAAC;EACnCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI;IACxB,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK;;IAE1B;IACA,IAAI,CAACC,IAAI,GAAGJ,OAAO,CAACI,IAAI;EAC1B;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB;IACA,IAAIA,KAAK,IAAI,IAAI,CAACH,IAAI,EAAE;MACtBE,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACP,GAAG,CAAC;MAC5B,IAAI,IAAI,CAACE,KAAK,EAAE;QACdG,SAAS,CAACG,aAAa,CAAC,IAAI,CAACN,KAAK,CAAC;MACrC;MACA,IAAI,IAAI,CAACD,IAAI,EAAE;QACbI,SAAS,CAACI,YAAY,CAAC,IAAI,CAACR,IAAI,EAAEK,KAAK,CAAC;MAC1C,CAAC,MAAM;QACLD,SAAS,CAACK,SAAS,CAACJ,KAAK,CAAC;MAC5B;MACAD,SAAS,CAACM,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACd,GAAG,EAAE;MAC1B,IAAI,IAAI,CAACC,IAAI,EAAE;QACb,IAAI,CAACK,KAAK,GAAGS,QAAQ,CAACF,IAAI,CAACG,UAAU,CAAC,IAAI,CAACf,IAAI,CAAC,EAAE,EAAE,CAAC;MACvD,CAAC,MAAM;QACL,IAAI,CAACgB,IAAI,GAAG,EAAE;MAChB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAAChB,IAAI,EAAE;MACd,IAAI,CAACgB,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACtB;EACF;EAEAG,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACnB,IAAI,EAAE;MACd,IAAI,CAACK,KAAK,GAAGS,QAAQ,CAAC,IAAI,CAACE,IAAI,CAACI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACpD;IACA,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG1B,YAAY"}