{"version": 3, "file": "base-xform.js", "names": ["parseSax", "require", "XmlStream", "BaseXform", "prepare", "render", "parseOpen", "node", "parseText", "text", "parseClose", "name", "reconcile", "model", "options", "reset", "map", "Object", "values", "for<PERSON>ach", "xform", "mergeModel", "obj", "assign", "parse", "sax<PERSON><PERSON><PERSON>", "events", "eventType", "value", "parseStream", "stream", "xml", "toXml", "xmlStream", "toAttribute", "dflt", "always", "arguments", "length", "undefined", "toString", "toStringAttribute", "toStringValue", "attr", "toBoolAttribute", "toBoolValue", "toIntAttribute", "toIntValue", "parseInt", "toFloatAttribute", "toFloatValue", "parseFloat", "module", "exports"], "sources": ["../../../../lib/xlsx/xform/base-xform.js"], "sourcesContent": ["const parseSax = require('../../utils/parse-sax');\nconst XmlStream = require('../../utils/xml-stream');\n\n/* 'virtual' methods used as a form of documentation */\n/* eslint-disable class-methods-use-this */\n\n// Base class for Xforms\nclass BaseXform {\n  // constructor(/* model, name */) {}\n\n  // ============================================================\n  // Virtual Interface\n  prepare(/* model, options */) {\n    // optional preparation (mutation) of model so it is ready for write\n  }\n\n  render(/* xmlStream, model */) {\n    // convert model to xml\n  }\n\n  parseOpen(node) {\n    // XML node opened\n  }\n\n  parseText(text) {\n    // chunk of text encountered for current node\n  }\n\n  parseClose(name) {\n    // XML node closed\n  }\n\n  reconcile(model, options) {\n    // optional post-parse step (opposite to prepare)\n  }\n\n  // ============================================================\n  reset() {\n    // to make sure parses don't bleed to next iteration\n    this.model = null;\n\n    // if we have a map - reset them too\n    if (this.map) {\n      Object.values(this.map).forEach(xform => {\n        if (xform instanceof BaseXform) {\n          xform.reset();\n        } else if (xform.xform) {\n          xform.xform.reset();\n        }\n      });\n    }\n  }\n\n  mergeModel(obj) {\n    // set obj's props to this.model\n    this.model = Object.assign(this.model || {}, obj);\n  }\n\n  async parse(saxParser) {\n    for await (const events of saxParser) {\n      for (const {eventType, value} of events) {\n        if (eventType === 'opentag') {\n          this.parseOpen(value);\n        } else if (eventType === 'text') {\n          this.parseText(value);\n        } else if (eventType === 'closetag') {\n          if (!this.parseClose(value.name)) {\n            return this.model;\n          }\n        }\n      }\n    }\n    return this.model;\n  }\n\n  async parseStream(stream) {\n    return this.parse(parseSax(stream));\n  }\n\n  get xml() {\n    // convenience function to get the xml of this.model\n    // useful for manager types that are built during the prepare phase\n    return this.toXml(this.model);\n  }\n\n  toXml(model) {\n    const xmlStream = new XmlStream();\n    this.render(xmlStream, model);\n    return xmlStream.xml;\n  }\n\n  // ============================================================\n  // Useful Utilities\n  static toAttribute(value, dflt, always = false) {\n    if (value === undefined) {\n      if (always) {\n        return dflt;\n      }\n    } else if (always || value !== dflt) {\n      return value.toString();\n    }\n    return undefined;\n  }\n\n  static toStringAttribute(value, dflt, always = false) {\n    return BaseXform.toAttribute(value, dflt, always);\n  }\n\n  static toStringValue(attr, dflt) {\n    return attr === undefined ? dflt : attr;\n  }\n\n  static toBoolAttribute(value, dflt, always = false) {\n    if (value === undefined) {\n      if (always) {\n        return dflt;\n      }\n    } else if (always || value !== dflt) {\n      return value ? '1' : '0';\n    }\n    return undefined;\n  }\n\n  static toBoolValue(attr, dflt) {\n    return attr === undefined ? dflt : attr === '1';\n  }\n\n  static toIntAttribute(value, dflt, always = false) {\n    return BaseXform.toAttribute(value, dflt, always);\n  }\n\n  static toIntValue(attr, dflt) {\n    return attr === undefined ? dflt : parseInt(attr, 10);\n  }\n\n  static toFloatAttribute(value, dflt, always = false) {\n    return BaseXform.toAttribute(value, dflt, always);\n  }\n\n  static toFloatValue(attr, dflt) {\n    return attr === undefined ? dflt : parseFloat(attr);\n  }\n}\n\nmodule.exports = BaseXform;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAMC,SAAS,GAAGD,OAAO,CAAC,wBAAwB,CAAC;;AAEnD;AACA;;AAEA;AACA,MAAME,SAAS,CAAC;EACd;;EAEA;EACA;EACAC,OAAOA,CAAA,CAAC;EAAA,EAAsB;IAC5B;EAAA;EAGFC,MAAMA,CAAA,CAAC;EAAA,EAAwB;IAC7B;EAAA;EAGFC,SAASA,CAACC,IAAI,EAAE;IACd;EAAA;EAGFC,SAASA,CAACC,IAAI,EAAE;IACd;EAAA;EAGFC,UAAUA,CAACC,IAAI,EAAE;IACf;EAAA;EAGFC,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACxB;EAAA;;EAGF;EACAC,KAAKA,CAAA,EAAG;IACN;IACA,IAAI,CAACF,KAAK,GAAG,IAAI;;IAEjB;IACA,IAAI,IAAI,CAACG,GAAG,EAAE;MACZC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACF,GAAG,CAAC,CAACG,OAAO,CAACC,KAAK,IAAI;QACvC,IAAIA,KAAK,YAAYjB,SAAS,EAAE;UAC9BiB,KAAK,CAACL,KAAK,CAAC,CAAC;QACf,CAAC,MAAM,IAAIK,KAAK,CAACA,KAAK,EAAE;UACtBA,KAAK,CAACA,KAAK,CAACL,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;EACF;EAEAM,UAAUA,CAACC,GAAG,EAAE;IACd;IACA,IAAI,CAACT,KAAK,GAAGI,MAAM,CAACM,MAAM,CAAC,IAAI,CAACV,KAAK,IAAI,CAAC,CAAC,EAAES,GAAG,CAAC;EACnD;EAEA,MAAME,KAAKA,CAACC,SAAS,EAAE;IACrB,WAAW,MAAMC,MAAM,IAAID,SAAS,EAAE;MACpC,KAAK,MAAM;QAACE,SAAS;QAAEC;MAAK,CAAC,IAAIF,MAAM,EAAE;QACvC,IAAIC,SAAS,KAAK,SAAS,EAAE;UAC3B,IAAI,CAACrB,SAAS,CAACsB,KAAK,CAAC;QACvB,CAAC,MAAM,IAAID,SAAS,KAAK,MAAM,EAAE;UAC/B,IAAI,CAACnB,SAAS,CAACoB,KAAK,CAAC;QACvB,CAAC,MAAM,IAAID,SAAS,KAAK,UAAU,EAAE;UACnC,IAAI,CAAC,IAAI,CAACjB,UAAU,CAACkB,KAAK,CAACjB,IAAI,CAAC,EAAE;YAChC,OAAO,IAAI,CAACE,KAAK;UACnB;QACF;MACF;IACF;IACA,OAAO,IAAI,CAACA,KAAK;EACnB;EAEA,MAAMgB,WAAWA,CAACC,MAAM,EAAE;IACxB,OAAO,IAAI,CAACN,KAAK,CAACxB,QAAQ,CAAC8B,MAAM,CAAC,CAAC;EACrC;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR;IACA;IACA,OAAO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnB,KAAK,CAAC;EAC/B;EAEAmB,KAAKA,CAACnB,KAAK,EAAE;IACX,MAAMoB,SAAS,GAAG,IAAI/B,SAAS,CAAC,CAAC;IACjC,IAAI,CAACG,MAAM,CAAC4B,SAAS,EAAEpB,KAAK,CAAC;IAC7B,OAAOoB,SAAS,CAACF,GAAG;EACtB;;EAEA;EACA;EACA,OAAOG,WAAWA,CAACN,KAAK,EAAEO,IAAI,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAC5C,IAAIT,KAAK,KAAKW,SAAS,EAAE;MACvB,IAAIH,MAAM,EAAE;QACV,OAAOD,IAAI;MACb;IACF,CAAC,MAAM,IAAIC,MAAM,IAAIR,KAAK,KAAKO,IAAI,EAAE;MACnC,OAAOP,KAAK,CAACY,QAAQ,CAAC,CAAC;IACzB;IACA,OAAOD,SAAS;EAClB;EAEA,OAAOE,iBAAiBA,CAACb,KAAK,EAAEO,IAAI,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAClD,OAAOlC,SAAS,CAAC+B,WAAW,CAACN,KAAK,EAAEO,IAAI,EAAEC,MAAM,CAAC;EACnD;EAEA,OAAOM,aAAaA,CAACC,IAAI,EAAER,IAAI,EAAE;IAC/B,OAAOQ,IAAI,KAAKJ,SAAS,GAAGJ,IAAI,GAAGQ,IAAI;EACzC;EAEA,OAAOC,eAAeA,CAAChB,KAAK,EAAEO,IAAI,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAChD,IAAIT,KAAK,KAAKW,SAAS,EAAE;MACvB,IAAIH,MAAM,EAAE;QACV,OAAOD,IAAI;MACb;IACF,CAAC,MAAM,IAAIC,MAAM,IAAIR,KAAK,KAAKO,IAAI,EAAE;MACnC,OAAOP,KAAK,GAAG,GAAG,GAAG,GAAG;IAC1B;IACA,OAAOW,SAAS;EAClB;EAEA,OAAOM,WAAWA,CAACF,IAAI,EAAER,IAAI,EAAE;IAC7B,OAAOQ,IAAI,KAAKJ,SAAS,GAAGJ,IAAI,GAAGQ,IAAI,KAAK,GAAG;EACjD;EAEA,OAAOG,cAAcA,CAAClB,KAAK,EAAEO,IAAI,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAC/C,OAAOlC,SAAS,CAAC+B,WAAW,CAACN,KAAK,EAAEO,IAAI,EAAEC,MAAM,CAAC;EACnD;EAEA,OAAOW,UAAUA,CAACJ,IAAI,EAAER,IAAI,EAAE;IAC5B,OAAOQ,IAAI,KAAKJ,SAAS,GAAGJ,IAAI,GAAGa,QAAQ,CAACL,IAAI,EAAE,EAAE,CAAC;EACvD;EAEA,OAAOM,gBAAgBA,CAACrB,KAAK,EAAEO,IAAI,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACjD,OAAOlC,SAAS,CAAC+B,WAAW,CAACN,KAAK,EAAEO,IAAI,EAAEC,MAAM,CAAC;EACnD;EAEA,OAAOc,YAAYA,CAACP,IAAI,EAAER,IAAI,EAAE;IAC9B,OAAOQ,IAAI,KAAKJ,SAAS,GAAGJ,IAAI,GAAGgB,UAAU,CAACR,IAAI,CAAC;EACrD;AACF;AAEAS,MAAM,CAACC,OAAO,GAAGlD,SAAS"}