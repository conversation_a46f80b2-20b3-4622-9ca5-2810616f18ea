{"version": 3, "file": "cell-xform.js", "names": ["utils", "require", "BaseXform", "Range", "Enums", "RichTextXform", "getValueType", "v", "undefined", "ValueType", "<PERSON><PERSON>", "String", "Number", "Boolean", "Date", "text", "hyperlink", "Hyperlink", "formula", "Formula", "error", "Error", "getEffectiveCellType", "cell", "type", "result", "CellXform", "constructor", "richTextXForm", "tag", "prepare", "model", "options", "styleId", "styles", "addStyleModel", "style", "comment", "comments", "push", "ref", "address", "RichText", "sharedStrings", "ssId", "add", "value", "date1904", "hyperlinks", "target", "tooltip", "<PERSON><PERSON>", "merges", "shareType", "si", "siFormulae", "formulae", "sharedFormula", "master", "range", "expandToAddress", "renderFormula", "xmlStream", "attrs", "t", "leafNode", "addAttribute", "dateToExcel", "render", "openNode", "richText", "for<PERSON>ach", "closeNode", "parseOpen", "node", "parser", "name", "attributes", "r", "s", "parseInt", "currentNode", "parseText", "parseClose", "xmlDecode", "parseFloat", "reconcile", "getStyleModel", "getString", "isDateFmt", "numFmt", "excelToDate", "hyperlinkMap", "commentsMap", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/cell-xform.js"], "sourcesContent": ["const utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\nconst Range = require('../../../doc/range');\nconst Enums = require('../../../doc/enums');\n\nconst RichTextXform = require('../strings/rich-text-xform');\n\nfunction getValueType(v) {\n  if (v === null || v === undefined) {\n    return Enums.ValueType.Null;\n  }\n  if (v instanceof String || typeof v === 'string') {\n    return Enums.ValueType.String;\n  }\n  if (typeof v === 'number') {\n    return Enums.ValueType.Number;\n  }\n  if (typeof v === 'boolean') {\n    return Enums.ValueType.Boolean;\n  }\n  if (v instanceof Date) {\n    return Enums.ValueType.Date;\n  }\n  if (v.text && v.hyperlink) {\n    return Enums.ValueType.Hyperlink;\n  }\n  if (v.formula) {\n    return Enums.ValueType.Formula;\n  }\n  if (v.error) {\n    return Enums.ValueType.Error;\n  }\n  throw new Error('I could not understand type of value');\n}\n\nfunction getEffectiveCellType(cell) {\n  switch (cell.type) {\n    case Enums.ValueType.Formula:\n      return getValueType(cell.result);\n    default:\n      return cell.type;\n  }\n}\n\nclass CellXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.richTextXForm = new RichTextXform();\n  }\n\n  get tag() {\n    return 'c';\n  }\n\n  prepare(model, options) {\n    const styleId = options.styles.addStyleModel(model.style || {}, getEffectiveCellType(model));\n    if (styleId) {\n      model.styleId = styleId;\n    }\n\n    if (model.comment) {\n      options.comments.push({...model.comment, ref: model.address});\n    }\n\n    switch (model.type) {\n      case Enums.ValueType.String:\n      case Enums.ValueType.RichText:\n        if (options.sharedStrings) {\n          model.ssId = options.sharedStrings.add(model.value);\n        }\n        break;\n\n      case Enums.ValueType.Date:\n        if (options.date1904) {\n          model.date1904 = true;\n        }\n        break;\n\n      case Enums.ValueType.Hyperlink:\n        if (options.sharedStrings && model.text !== undefined && model.text !== null) {\n          model.ssId = options.sharedStrings.add(model.text);\n        }\n        options.hyperlinks.push({\n          address: model.address,\n          target: model.hyperlink,\n          tooltip: model.tooltip,\n        });\n        break;\n\n      case Enums.ValueType.Merge:\n        options.merges.add(model);\n        break;\n\n      case Enums.ValueType.Formula:\n        if (options.date1904) {\n          // in case valueType is date\n          model.date1904 = true;\n        }\n\n        if (model.shareType === 'shared') {\n          model.si = options.siFormulae++;\n        }\n\n        if (model.formula) {\n          options.formulae[model.address] = model;\n        } else if (model.sharedFormula) {\n          const master = options.formulae[model.sharedFormula];\n          if (!master) {\n            throw new Error(\n              `Shared Formula master must exist above and or left of clone for cell ${model.address}`\n            );\n          }\n          if (master.si === undefined) {\n            master.shareType = 'shared';\n            master.si = options.siFormulae++;\n            master.range = new Range(master.address, model.address);\n          } else if (master.range) {\n            master.range.expandToAddress(model.address);\n          }\n          model.si = master.si;\n        }\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  renderFormula(xmlStream, model) {\n    let attrs = null;\n    switch (model.shareType) {\n      case 'shared':\n        attrs = {\n          t: 'shared',\n          ref: model.ref || model.range.range,\n          si: model.si,\n        };\n        break;\n\n      case 'array':\n        attrs = {\n          t: 'array',\n          ref: model.ref,\n        };\n        break;\n\n      default:\n        if (model.si !== undefined) {\n          attrs = {\n            t: 'shared',\n            si: model.si,\n          };\n        }\n        break;\n    }\n\n    switch (getValueType(model.result)) {\n      case Enums.ValueType.Null: // ?\n        xmlStream.leafNode('f', attrs, model.formula);\n        break;\n\n      case Enums.ValueType.String:\n        // oddly, formula results don't ever use shared strings\n        xmlStream.addAttribute('t', 'str');\n        xmlStream.leafNode('f', attrs, model.formula);\n        xmlStream.leafNode('v', null, model.result);\n        break;\n\n      case Enums.ValueType.Number:\n        xmlStream.leafNode('f', attrs, model.formula);\n        xmlStream.leafNode('v', null, model.result);\n        break;\n\n      case Enums.ValueType.Boolean:\n        xmlStream.addAttribute('t', 'b');\n        xmlStream.leafNode('f', attrs, model.formula);\n        xmlStream.leafNode('v', null, model.result ? 1 : 0);\n        break;\n\n      case Enums.ValueType.Error:\n        xmlStream.addAttribute('t', 'e');\n        xmlStream.leafNode('f', attrs, model.formula);\n        xmlStream.leafNode('v', null, model.result.error);\n        break;\n\n      case Enums.ValueType.Date:\n        xmlStream.leafNode('f', attrs, model.formula);\n        xmlStream.leafNode('v', null, utils.dateToExcel(model.result, model.date1904));\n        break;\n\n      // case Enums.ValueType.Hyperlink: // ??\n      // case Enums.ValueType.Formula:\n      default:\n        throw new Error('I could not understand type of value');\n    }\n  }\n\n  render(xmlStream, model) {\n    if (model.type === Enums.ValueType.Null && !model.styleId) {\n      // if null and no style, exit\n      return;\n    }\n\n    xmlStream.openNode('c');\n    xmlStream.addAttribute('r', model.address);\n\n    if (model.styleId) {\n      xmlStream.addAttribute('s', model.styleId);\n    }\n\n    switch (model.type) {\n      case Enums.ValueType.Null:\n        break;\n\n      case Enums.ValueType.Number:\n        xmlStream.leafNode('v', null, model.value);\n        break;\n\n      case Enums.ValueType.Boolean:\n        xmlStream.addAttribute('t', 'b');\n        xmlStream.leafNode('v', null, model.value ? '1' : '0');\n        break;\n\n      case Enums.ValueType.Error:\n        xmlStream.addAttribute('t', 'e');\n        xmlStream.leafNode('v', null, model.value.error);\n        break;\n\n      case Enums.ValueType.String:\n      case Enums.ValueType.RichText:\n        if (model.ssId !== undefined) {\n          xmlStream.addAttribute('t', 's');\n          xmlStream.leafNode('v', null, model.ssId);\n        } else if (model.value && model.value.richText) {\n          xmlStream.addAttribute('t', 'inlineStr');\n          xmlStream.openNode('is');\n          model.value.richText.forEach(text => {\n            this.richTextXForm.render(xmlStream, text);\n          });\n          xmlStream.closeNode('is');\n        } else {\n          xmlStream.addAttribute('t', 'str');\n          xmlStream.leafNode('v', null, model.value);\n        }\n        break;\n\n      case Enums.ValueType.Date:\n        xmlStream.leafNode('v', null, utils.dateToExcel(model.value, model.date1904));\n        break;\n\n      case Enums.ValueType.Hyperlink:\n        if (model.ssId !== undefined) {\n          xmlStream.addAttribute('t', 's');\n          xmlStream.leafNode('v', null, model.ssId);\n        } else {\n          xmlStream.addAttribute('t', 'str');\n          xmlStream.leafNode('v', null, model.text);\n        }\n        break;\n\n      case Enums.ValueType.Formula:\n        this.renderFormula(xmlStream, model);\n        break;\n\n      case Enums.ValueType.Merge:\n        // nothing to add\n        break;\n\n      default:\n        break;\n    }\n\n    xmlStream.closeNode(); // </c>\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'c':\n        // const address = colCache.decodeAddress(node.attributes.r);\n        this.model = {\n          address: node.attributes.r,\n        };\n        this.t = node.attributes.t;\n        if (node.attributes.s) {\n          this.model.styleId = parseInt(node.attributes.s, 10);\n        }\n        return true;\n\n      case 'f':\n        this.currentNode = 'f';\n        this.model.si = node.attributes.si;\n        this.model.shareType = node.attributes.t;\n        this.model.ref = node.attributes.ref;\n        return true;\n\n      case 'v':\n        this.currentNode = 'v';\n        return true;\n\n      case 't':\n        this.currentNode = 't';\n        return true;\n\n      case 'r':\n        this.parser = this.richTextXForm;\n        this.parser.parseOpen(node);\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n      return;\n    }\n    switch (this.currentNode) {\n      case 'f':\n        this.model.formula = this.model.formula ? this.model.formula + text : text;\n        break;\n      case 'v':\n      case 't':\n        if (this.model.value && this.model.value.richText) {\n          this.model.value.richText.text = this.model.value.richText.text\n            ? this.model.value.richText.text + text\n            : text;\n        } else {\n          this.model.value = this.model.value ? this.model.value + text : text;\n        }\n        break;\n      default:\n        break;\n    }\n  }\n\n  parseClose(name) {\n    switch (name) {\n      case 'c': {\n        const {model} = this;\n\n        // first guess on cell type\n        if (model.formula || model.shareType) {\n          model.type = Enums.ValueType.Formula;\n          if (model.value) {\n            if (this.t === 'str') {\n              model.result = utils.xmlDecode(model.value);\n            } else if (this.t === 'b') {\n              model.result = parseInt(model.value, 10) !== 0;\n            } else if (this.t === 'e') {\n              model.result = {error: model.value};\n            } else {\n              model.result = parseFloat(model.value);\n            }\n            model.value = undefined;\n          }\n        } else if (model.value !== undefined) {\n          switch (this.t) {\n            case 's':\n              model.type = Enums.ValueType.String;\n              model.value = parseInt(model.value, 10);\n              break;\n            case 'str':\n              model.type = Enums.ValueType.String;\n              model.value = utils.xmlDecode(model.value);\n              break;\n            case 'inlineStr':\n              model.type = Enums.ValueType.String;\n              break;\n            case 'b':\n              model.type = Enums.ValueType.Boolean;\n              model.value = parseInt(model.value, 10) !== 0;\n              break;\n            case 'e':\n              model.type = Enums.ValueType.Error;\n              model.value = {error: model.value};\n              break;\n            default:\n              model.type = Enums.ValueType.Number;\n              model.value = parseFloat(model.value);\n              break;\n          }\n        } else if (model.styleId) {\n          model.type = Enums.ValueType.Null;\n        } else {\n          model.type = Enums.ValueType.Merge;\n        }\n        return false;\n      }\n\n      case 'f':\n      case 'v':\n      case 'is':\n        this.currentNode = undefined;\n        return true;\n\n      case 't':\n        if (this.parser) {\n          this.parser.parseClose(name);\n          return true;\n        }\n        this.currentNode = undefined;\n        return true;\n\n      case 'r':\n        this.model.value = this.model.value || {};\n        this.model.value.richText = this.model.value.richText || [];\n        this.model.value.richText.push(this.parser.model);\n        this.parser = undefined;\n        this.currentNode = undefined;\n        return true;\n\n      default:\n        if (this.parser) {\n          this.parser.parseClose(name);\n          return true;\n        }\n        return false;\n    }\n  }\n\n  reconcile(model, options) {\n    const style = model.styleId && options.styles && options.styles.getStyleModel(model.styleId);\n    if (style) {\n      model.style = style;\n    }\n    if (model.styleId !== undefined) {\n      model.styleId = undefined;\n    }\n\n    switch (model.type) {\n      case Enums.ValueType.String:\n        if (typeof model.value === 'number') {\n          if (options.sharedStrings) {\n            model.value = options.sharedStrings.getString(model.value);\n          }\n        }\n        if (model.value.richText) {\n          model.type = Enums.ValueType.RichText;\n        }\n        break;\n\n      case Enums.ValueType.Number:\n        if (style && utils.isDateFmt(style.numFmt)) {\n          model.type = Enums.ValueType.Date;\n          model.value = utils.excelToDate(model.value, options.date1904);\n        }\n        break;\n\n      case Enums.ValueType.Formula:\n        if (model.result !== undefined && style && utils.isDateFmt(style.numFmt)) {\n          model.result = utils.excelToDate(model.result, options.date1904);\n        }\n        if (model.shareType === 'shared') {\n          if (model.ref) {\n            // master\n            options.formulae[model.si] = model.address;\n          } else {\n            // slave\n            model.sharedFormula = options.formulae[model.si];\n            delete model.shareType;\n          }\n          delete model.si;\n        }\n        break;\n\n      default:\n        break;\n    }\n\n    // look for hyperlink\n    const hyperlink = options.hyperlinkMap[model.address];\n    if (hyperlink) {\n      if (model.type === Enums.ValueType.Formula) {\n        model.text = model.result;\n        model.result = undefined;\n      } else {\n        model.text = model.value;\n        model.value = undefined;\n      }\n      model.type = Enums.ValueType.Hyperlink;\n      model.hyperlink = hyperlink;\n    }\n\n    const comment = options.commentsMap && options.commentsMap[model.address];\n    if (comment) {\n      model.comment = comment;\n    }\n  }\n}\n\nmodule.exports = CellXform;\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,KAAK,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAC3C,MAAMG,KAAK,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAE3C,MAAMI,aAAa,GAAGJ,OAAO,CAAC,4BAA4B,CAAC;AAE3D,SAASK,YAAYA,CAACC,CAAC,EAAE;EACvB,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,EAAE;IACjC,OAAOJ,KAAK,CAACK,SAAS,CAACC,IAAI;EAC7B;EACA,IAAIH,CAAC,YAAYI,MAAM,IAAI,OAAOJ,CAAC,KAAK,QAAQ,EAAE;IAChD,OAAOH,KAAK,CAACK,SAAS,CAACE,MAAM;EAC/B;EACA,IAAI,OAAOJ,CAAC,KAAK,QAAQ,EAAE;IACzB,OAAOH,KAAK,CAACK,SAAS,CAACG,MAAM;EAC/B;EACA,IAAI,OAAOL,CAAC,KAAK,SAAS,EAAE;IAC1B,OAAOH,KAAK,CAACK,SAAS,CAACI,OAAO;EAChC;EACA,IAAIN,CAAC,YAAYO,IAAI,EAAE;IACrB,OAAOV,KAAK,CAACK,SAAS,CAACK,IAAI;EAC7B;EACA,IAAIP,CAAC,CAACQ,IAAI,IAAIR,CAAC,CAACS,SAAS,EAAE;IACzB,OAAOZ,KAAK,CAACK,SAAS,CAACQ,SAAS;EAClC;EACA,IAAIV,CAAC,CAACW,OAAO,EAAE;IACb,OAAOd,KAAK,CAACK,SAAS,CAACU,OAAO;EAChC;EACA,IAAIZ,CAAC,CAACa,KAAK,EAAE;IACX,OAAOhB,KAAK,CAACK,SAAS,CAACY,KAAK;EAC9B;EACA,MAAM,IAAIA,KAAK,CAAC,sCAAsC,CAAC;AACzD;AAEA,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,QAAQA,IAAI,CAACC,IAAI;IACf,KAAKpB,KAAK,CAACK,SAAS,CAACU,OAAO;MAC1B,OAAOb,YAAY,CAACiB,IAAI,CAACE,MAAM,CAAC;IAClC;MACE,OAAOF,IAAI,CAACC,IAAI;EACpB;AACF;AAEA,MAAME,SAAS,SAASxB,SAAS,CAAC;EAChCyB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,aAAa,GAAG,IAAIvB,aAAa,CAAC,CAAC;EAC1C;EAEA,IAAIwB,GAAGA,CAAA,EAAG;IACR,OAAO,GAAG;EACZ;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtB,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAACC,aAAa,CAACJ,KAAK,CAACK,KAAK,IAAI,CAAC,CAAC,EAAEd,oBAAoB,CAACS,KAAK,CAAC,CAAC;IAC5F,IAAIE,OAAO,EAAE;MACXF,KAAK,CAACE,OAAO,GAAGA,OAAO;IACzB;IAEA,IAAIF,KAAK,CAACM,OAAO,EAAE;MACjBL,OAAO,CAACM,QAAQ,CAACC,IAAI,CAAC;QAAC,GAAGR,KAAK,CAACM,OAAO;QAAEG,GAAG,EAAET,KAAK,CAACU;MAAO,CAAC,CAAC;IAC/D;IAEA,QAAQV,KAAK,CAACP,IAAI;MAChB,KAAKpB,KAAK,CAACK,SAAS,CAACE,MAAM;MAC3B,KAAKP,KAAK,CAACK,SAAS,CAACiC,QAAQ;QAC3B,IAAIV,OAAO,CAACW,aAAa,EAAE;UACzBZ,KAAK,CAACa,IAAI,GAAGZ,OAAO,CAACW,aAAa,CAACE,GAAG,CAACd,KAAK,CAACe,KAAK,CAAC;QACrD;QACA;MAEF,KAAK1C,KAAK,CAACK,SAAS,CAACK,IAAI;QACvB,IAAIkB,OAAO,CAACe,QAAQ,EAAE;UACpBhB,KAAK,CAACgB,QAAQ,GAAG,IAAI;QACvB;QACA;MAEF,KAAK3C,KAAK,CAACK,SAAS,CAACQ,SAAS;QAC5B,IAAIe,OAAO,CAACW,aAAa,IAAIZ,KAAK,CAAChB,IAAI,KAAKP,SAAS,IAAIuB,KAAK,CAAChB,IAAI,KAAK,IAAI,EAAE;UAC5EgB,KAAK,CAACa,IAAI,GAAGZ,OAAO,CAACW,aAAa,CAACE,GAAG,CAACd,KAAK,CAAChB,IAAI,CAAC;QACpD;QACAiB,OAAO,CAACgB,UAAU,CAACT,IAAI,CAAC;UACtBE,OAAO,EAAEV,KAAK,CAACU,OAAO;UACtBQ,MAAM,EAAElB,KAAK,CAACf,SAAS;UACvBkC,OAAO,EAAEnB,KAAK,CAACmB;QACjB,CAAC,CAAC;QACF;MAEF,KAAK9C,KAAK,CAACK,SAAS,CAAC0C,KAAK;QACxBnB,OAAO,CAACoB,MAAM,CAACP,GAAG,CAACd,KAAK,CAAC;QACzB;MAEF,KAAK3B,KAAK,CAACK,SAAS,CAACU,OAAO;QAC1B,IAAIa,OAAO,CAACe,QAAQ,EAAE;UACpB;UACAhB,KAAK,CAACgB,QAAQ,GAAG,IAAI;QACvB;QAEA,IAAIhB,KAAK,CAACsB,SAAS,KAAK,QAAQ,EAAE;UAChCtB,KAAK,CAACuB,EAAE,GAAGtB,OAAO,CAACuB,UAAU,EAAE;QACjC;QAEA,IAAIxB,KAAK,CAACb,OAAO,EAAE;UACjBc,OAAO,CAACwB,QAAQ,CAACzB,KAAK,CAACU,OAAO,CAAC,GAAGV,KAAK;QACzC,CAAC,MAAM,IAAIA,KAAK,CAAC0B,aAAa,EAAE;UAC9B,MAAMC,MAAM,GAAG1B,OAAO,CAACwB,QAAQ,CAACzB,KAAK,CAAC0B,aAAa,CAAC;UACpD,IAAI,CAACC,MAAM,EAAE;YACX,MAAM,IAAIrC,KAAK,CACZ,wEAAuEU,KAAK,CAACU,OAAQ,EACxF,CAAC;UACH;UACA,IAAIiB,MAAM,CAACJ,EAAE,KAAK9C,SAAS,EAAE;YAC3BkD,MAAM,CAACL,SAAS,GAAG,QAAQ;YAC3BK,MAAM,CAACJ,EAAE,GAAGtB,OAAO,CAACuB,UAAU,EAAE;YAChCG,MAAM,CAACC,KAAK,GAAG,IAAIxD,KAAK,CAACuD,MAAM,CAACjB,OAAO,EAAEV,KAAK,CAACU,OAAO,CAAC;UACzD,CAAC,MAAM,IAAIiB,MAAM,CAACC,KAAK,EAAE;YACvBD,MAAM,CAACC,KAAK,CAACC,eAAe,CAAC7B,KAAK,CAACU,OAAO,CAAC;UAC7C;UACAV,KAAK,CAACuB,EAAE,GAAGI,MAAM,CAACJ,EAAE;QACtB;QACA;MAEF;QACE;IACJ;EACF;EAEAO,aAAaA,CAACC,SAAS,EAAE/B,KAAK,EAAE;IAC9B,IAAIgC,KAAK,GAAG,IAAI;IAChB,QAAQhC,KAAK,CAACsB,SAAS;MACrB,KAAK,QAAQ;QACXU,KAAK,GAAG;UACNC,CAAC,EAAE,QAAQ;UACXxB,GAAG,EAAET,KAAK,CAACS,GAAG,IAAIT,KAAK,CAAC4B,KAAK,CAACA,KAAK;UACnCL,EAAE,EAAEvB,KAAK,CAACuB;QACZ,CAAC;QACD;MAEF,KAAK,OAAO;QACVS,KAAK,GAAG;UACNC,CAAC,EAAE,OAAO;UACVxB,GAAG,EAAET,KAAK,CAACS;QACb,CAAC;QACD;MAEF;QACE,IAAIT,KAAK,CAACuB,EAAE,KAAK9C,SAAS,EAAE;UAC1BuD,KAAK,GAAG;YACNC,CAAC,EAAE,QAAQ;YACXV,EAAE,EAAEvB,KAAK,CAACuB;UACZ,CAAC;QACH;QACA;IACJ;IAEA,QAAQhD,YAAY,CAACyB,KAAK,CAACN,MAAM,CAAC;MAChC,KAAKrB,KAAK,CAACK,SAAS,CAACC,IAAI;QAAE;QACzBoD,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C;MAEF,KAAKd,KAAK,CAACK,SAAS,CAACE,MAAM;QACzB;QACAmD,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;QAClCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C4C,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACN,MAAM,CAAC;QAC3C;MAEF,KAAKrB,KAAK,CAACK,SAAS,CAACG,MAAM;QACzBkD,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C4C,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACN,MAAM,CAAC;QAC3C;MAEF,KAAKrB,KAAK,CAACK,SAAS,CAACI,OAAO;QAC1BiD,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;QAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C4C,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACN,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACnD;MAEF,KAAKrB,KAAK,CAACK,SAAS,CAACY,KAAK;QACxByC,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;QAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C4C,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACN,MAAM,CAACL,KAAK,CAAC;QACjD;MAEF,KAAKhB,KAAK,CAACK,SAAS,CAACK,IAAI;QACvBgD,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAEF,KAAK,EAAEhC,KAAK,CAACb,OAAO,CAAC;QAC7C4C,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAEjE,KAAK,CAACmE,WAAW,CAACpC,KAAK,CAACN,MAAM,EAAEM,KAAK,CAACgB,QAAQ,CAAC,CAAC;QAC9E;;MAEF;MACA;MACA;QACE,MAAM,IAAI1B,KAAK,CAAC,sCAAsC,CAAC;IAC3D;EACF;EAEA+C,MAAMA,CAACN,SAAS,EAAE/B,KAAK,EAAE;IACvB,IAAIA,KAAK,CAACP,IAAI,KAAKpB,KAAK,CAACK,SAAS,CAACC,IAAI,IAAI,CAACqB,KAAK,CAACE,OAAO,EAAE;MACzD;MACA;IACF;IAEA6B,SAAS,CAACO,QAAQ,CAAC,GAAG,CAAC;IACvBP,SAAS,CAACI,YAAY,CAAC,GAAG,EAAEnC,KAAK,CAACU,OAAO,CAAC;IAE1C,IAAIV,KAAK,CAACE,OAAO,EAAE;MACjB6B,SAAS,CAACI,YAAY,CAAC,GAAG,EAAEnC,KAAK,CAACE,OAAO,CAAC;IAC5C;IAEA,QAAQF,KAAK,CAACP,IAAI;MAChB,KAAKpB,KAAK,CAACK,SAAS,CAACC,IAAI;QACvB;MAEF,KAAKN,KAAK,CAACK,SAAS,CAACG,MAAM;QACzBkD,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACe,KAAK,CAAC;QAC1C;MAEF,KAAK1C,KAAK,CAACK,SAAS,CAACI,OAAO;QAC1BiD,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;QAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACe,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QACtD;MAEF,KAAK1C,KAAK,CAACK,SAAS,CAACY,KAAK;QACxByC,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;QAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACe,KAAK,CAAC1B,KAAK,CAAC;QAChD;MAEF,KAAKhB,KAAK,CAACK,SAAS,CAACE,MAAM;MAC3B,KAAKP,KAAK,CAACK,SAAS,CAACiC,QAAQ;QAC3B,IAAIX,KAAK,CAACa,IAAI,KAAKpC,SAAS,EAAE;UAC5BsD,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;UAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACa,IAAI,CAAC;QAC3C,CAAC,MAAM,IAAIb,KAAK,CAACe,KAAK,IAAIf,KAAK,CAACe,KAAK,CAACwB,QAAQ,EAAE;UAC9CR,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC;UACxCJ,SAAS,CAACO,QAAQ,CAAC,IAAI,CAAC;UACxBtC,KAAK,CAACe,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACxD,IAAI,IAAI;YACnC,IAAI,CAACa,aAAa,CAACwC,MAAM,CAACN,SAAS,EAAE/C,IAAI,CAAC;UAC5C,CAAC,CAAC;UACF+C,SAAS,CAACU,SAAS,CAAC,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLV,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;UAClCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACe,KAAK,CAAC;QAC5C;QACA;MAEF,KAAK1C,KAAK,CAACK,SAAS,CAACK,IAAI;QACvBgD,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAEjE,KAAK,CAACmE,WAAW,CAACpC,KAAK,CAACe,KAAK,EAAEf,KAAK,CAACgB,QAAQ,CAAC,CAAC;QAC7E;MAEF,KAAK3C,KAAK,CAACK,SAAS,CAACQ,SAAS;QAC5B,IAAIc,KAAK,CAACa,IAAI,KAAKpC,SAAS,EAAE;UAC5BsD,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;UAChCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAACa,IAAI,CAAC;QAC3C,CAAC,MAAM;UACLkB,SAAS,CAACI,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;UAClCJ,SAAS,CAACG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,KAAK,CAAChB,IAAI,CAAC;QAC3C;QACA;MAEF,KAAKX,KAAK,CAACK,SAAS,CAACU,OAAO;QAC1B,IAAI,CAAC0C,aAAa,CAACC,SAAS,EAAE/B,KAAK,CAAC;QACpC;MAEF,KAAK3B,KAAK,CAACK,SAAS,CAAC0C,KAAK;QACxB;QACA;MAEF;QACE;IACJ;IAEAW,SAAS,CAACU,SAAS,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,GAAG;QACN;QACA,IAAI,CAAC7C,KAAK,GAAG;UACXU,OAAO,EAAEiC,IAAI,CAACG,UAAU,CAACC;QAC3B,CAAC;QACD,IAAI,CAACd,CAAC,GAAGU,IAAI,CAACG,UAAU,CAACb,CAAC;QAC1B,IAAIU,IAAI,CAACG,UAAU,CAACE,CAAC,EAAE;UACrB,IAAI,CAAChD,KAAK,CAACE,OAAO,GAAG+C,QAAQ,CAACN,IAAI,CAACG,UAAU,CAACE,CAAC,EAAE,EAAE,CAAC;QACtD;QACA,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,CAACE,WAAW,GAAG,GAAG;QACtB,IAAI,CAAClD,KAAK,CAACuB,EAAE,GAAGoB,IAAI,CAACG,UAAU,CAACvB,EAAE;QAClC,IAAI,CAACvB,KAAK,CAACsB,SAAS,GAAGqB,IAAI,CAACG,UAAU,CAACb,CAAC;QACxC,IAAI,CAACjC,KAAK,CAACS,GAAG,GAAGkC,IAAI,CAACG,UAAU,CAACrC,GAAG;QACpC,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,CAACyC,WAAW,GAAG,GAAG;QACtB,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,CAACA,WAAW,GAAG,GAAG;QACtB,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,CAACN,MAAM,GAAG,IAAI,CAAC/C,aAAa;QAChC,IAAI,CAAC+C,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;EAEAQ,SAASA,CAACnE,IAAI,EAAE;IACd,IAAI,IAAI,CAAC4D,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACO,SAAS,CAACnE,IAAI,CAAC;MAC3B;IACF;IACA,QAAQ,IAAI,CAACkE,WAAW;MACtB,KAAK,GAAG;QACN,IAAI,CAAClD,KAAK,CAACb,OAAO,GAAG,IAAI,CAACa,KAAK,CAACb,OAAO,GAAG,IAAI,CAACa,KAAK,CAACb,OAAO,GAAGH,IAAI,GAAGA,IAAI;QAC1E;MACF,KAAK,GAAG;MACR,KAAK,GAAG;QACN,IAAI,IAAI,CAACgB,KAAK,CAACe,KAAK,IAAI,IAAI,CAACf,KAAK,CAACe,KAAK,CAACwB,QAAQ,EAAE;UACjD,IAAI,CAACvC,KAAK,CAACe,KAAK,CAACwB,QAAQ,CAACvD,IAAI,GAAG,IAAI,CAACgB,KAAK,CAACe,KAAK,CAACwB,QAAQ,CAACvD,IAAI,GAC3D,IAAI,CAACgB,KAAK,CAACe,KAAK,CAACwB,QAAQ,CAACvD,IAAI,GAAGA,IAAI,GACrCA,IAAI;QACV,CAAC,MAAM;UACL,IAAI,CAACgB,KAAK,CAACe,KAAK,GAAG,IAAI,CAACf,KAAK,CAACe,KAAK,GAAG,IAAI,CAACf,KAAK,CAACe,KAAK,GAAG/B,IAAI,GAAGA,IAAI;QACtE;QACA;MACF;QACE;IACJ;EACF;EAEAoE,UAAUA,CAACP,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,GAAG;QAAE;UACR,MAAM;YAAC7C;UAAK,CAAC,GAAG,IAAI;;UAEpB;UACA,IAAIA,KAAK,CAACb,OAAO,IAAIa,KAAK,CAACsB,SAAS,EAAE;YACpCtB,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACU,OAAO;YACpC,IAAIY,KAAK,CAACe,KAAK,EAAE;cACf,IAAI,IAAI,CAACkB,CAAC,KAAK,KAAK,EAAE;gBACpBjC,KAAK,CAACN,MAAM,GAAGzB,KAAK,CAACoF,SAAS,CAACrD,KAAK,CAACe,KAAK,CAAC;cAC7C,CAAC,MAAM,IAAI,IAAI,CAACkB,CAAC,KAAK,GAAG,EAAE;gBACzBjC,KAAK,CAACN,MAAM,GAAGuD,QAAQ,CAACjD,KAAK,CAACe,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC;cAChD,CAAC,MAAM,IAAI,IAAI,CAACkB,CAAC,KAAK,GAAG,EAAE;gBACzBjC,KAAK,CAACN,MAAM,GAAG;kBAACL,KAAK,EAAEW,KAAK,CAACe;gBAAK,CAAC;cACrC,CAAC,MAAM;gBACLf,KAAK,CAACN,MAAM,GAAG4D,UAAU,CAACtD,KAAK,CAACe,KAAK,CAAC;cACxC;cACAf,KAAK,CAACe,KAAK,GAAGtC,SAAS;YACzB;UACF,CAAC,MAAM,IAAIuB,KAAK,CAACe,KAAK,KAAKtC,SAAS,EAAE;YACpC,QAAQ,IAAI,CAACwD,CAAC;cACZ,KAAK,GAAG;gBACNjC,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACE,MAAM;gBACnCoB,KAAK,CAACe,KAAK,GAAGkC,QAAQ,CAACjD,KAAK,CAACe,KAAK,EAAE,EAAE,CAAC;gBACvC;cACF,KAAK,KAAK;gBACRf,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACE,MAAM;gBACnCoB,KAAK,CAACe,KAAK,GAAG9C,KAAK,CAACoF,SAAS,CAACrD,KAAK,CAACe,KAAK,CAAC;gBAC1C;cACF,KAAK,WAAW;gBACdf,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACE,MAAM;gBACnC;cACF,KAAK,GAAG;gBACNoB,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACI,OAAO;gBACpCkB,KAAK,CAACe,KAAK,GAAGkC,QAAQ,CAACjD,KAAK,CAACe,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC;gBAC7C;cACF,KAAK,GAAG;gBACNf,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACY,KAAK;gBAClCU,KAAK,CAACe,KAAK,GAAG;kBAAC1B,KAAK,EAAEW,KAAK,CAACe;gBAAK,CAAC;gBAClC;cACF;gBACEf,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACG,MAAM;gBACnCmB,KAAK,CAACe,KAAK,GAAGuC,UAAU,CAACtD,KAAK,CAACe,KAAK,CAAC;gBACrC;YACJ;UACF,CAAC,MAAM,IAAIf,KAAK,CAACE,OAAO,EAAE;YACxBF,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACC,IAAI;UACnC,CAAC,MAAM;YACLqB,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAAC0C,KAAK;UACpC;UACA,OAAO,KAAK;QACd;MAEA,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,IAAI;QACP,IAAI,CAAC8B,WAAW,GAAGzE,SAAS;QAC5B,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,IAAI,CAACmE,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACQ,UAAU,CAACP,IAAI,CAAC;UAC5B,OAAO,IAAI;QACb;QACA,IAAI,CAACK,WAAW,GAAGzE,SAAS;QAC5B,OAAO,IAAI;MAEb,KAAK,GAAG;QACN,IAAI,CAACuB,KAAK,CAACe,KAAK,GAAG,IAAI,CAACf,KAAK,CAACe,KAAK,IAAI,CAAC,CAAC;QACzC,IAAI,CAACf,KAAK,CAACe,KAAK,CAACwB,QAAQ,GAAG,IAAI,CAACvC,KAAK,CAACe,KAAK,CAACwB,QAAQ,IAAI,EAAE;QAC3D,IAAI,CAACvC,KAAK,CAACe,KAAK,CAACwB,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAACoC,MAAM,CAAC5C,KAAK,CAAC;QACjD,IAAI,CAAC4C,MAAM,GAAGnE,SAAS;QACvB,IAAI,CAACyE,WAAW,GAAGzE,SAAS;QAC5B,OAAO,IAAI;MAEb;QACE,IAAI,IAAI,CAACmE,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACQ,UAAU,CAACP,IAAI,CAAC;UAC5B,OAAO,IAAI;QACb;QACA,OAAO,KAAK;IAChB;EACF;EAEAU,SAASA,CAACvD,KAAK,EAAEC,OAAO,EAAE;IACxB,MAAMI,KAAK,GAAGL,KAAK,CAACE,OAAO,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAACqD,aAAa,CAACxD,KAAK,CAACE,OAAO,CAAC;IAC5F,IAAIG,KAAK,EAAE;MACTL,KAAK,CAACK,KAAK,GAAGA,KAAK;IACrB;IACA,IAAIL,KAAK,CAACE,OAAO,KAAKzB,SAAS,EAAE;MAC/BuB,KAAK,CAACE,OAAO,GAAGzB,SAAS;IAC3B;IAEA,QAAQuB,KAAK,CAACP,IAAI;MAChB,KAAKpB,KAAK,CAACK,SAAS,CAACE,MAAM;QACzB,IAAI,OAAOoB,KAAK,CAACe,KAAK,KAAK,QAAQ,EAAE;UACnC,IAAId,OAAO,CAACW,aAAa,EAAE;YACzBZ,KAAK,CAACe,KAAK,GAAGd,OAAO,CAACW,aAAa,CAAC6C,SAAS,CAACzD,KAAK,CAACe,KAAK,CAAC;UAC5D;QACF;QACA,IAAIf,KAAK,CAACe,KAAK,CAACwB,QAAQ,EAAE;UACxBvC,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACiC,QAAQ;QACvC;QACA;MAEF,KAAKtC,KAAK,CAACK,SAAS,CAACG,MAAM;QACzB,IAAIwB,KAAK,IAAIpC,KAAK,CAACyF,SAAS,CAACrD,KAAK,CAACsD,MAAM,CAAC,EAAE;UAC1C3D,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACK,IAAI;UACjCiB,KAAK,CAACe,KAAK,GAAG9C,KAAK,CAAC2F,WAAW,CAAC5D,KAAK,CAACe,KAAK,EAAEd,OAAO,CAACe,QAAQ,CAAC;QAChE;QACA;MAEF,KAAK3C,KAAK,CAACK,SAAS,CAACU,OAAO;QAC1B,IAAIY,KAAK,CAACN,MAAM,KAAKjB,SAAS,IAAI4B,KAAK,IAAIpC,KAAK,CAACyF,SAAS,CAACrD,KAAK,CAACsD,MAAM,CAAC,EAAE;UACxE3D,KAAK,CAACN,MAAM,GAAGzB,KAAK,CAAC2F,WAAW,CAAC5D,KAAK,CAACN,MAAM,EAAEO,OAAO,CAACe,QAAQ,CAAC;QAClE;QACA,IAAIhB,KAAK,CAACsB,SAAS,KAAK,QAAQ,EAAE;UAChC,IAAItB,KAAK,CAACS,GAAG,EAAE;YACb;YACAR,OAAO,CAACwB,QAAQ,CAACzB,KAAK,CAACuB,EAAE,CAAC,GAAGvB,KAAK,CAACU,OAAO;UAC5C,CAAC,MAAM;YACL;YACAV,KAAK,CAAC0B,aAAa,GAAGzB,OAAO,CAACwB,QAAQ,CAACzB,KAAK,CAACuB,EAAE,CAAC;YAChD,OAAOvB,KAAK,CAACsB,SAAS;UACxB;UACA,OAAOtB,KAAK,CAACuB,EAAE;QACjB;QACA;MAEF;QACE;IACJ;;IAEA;IACA,MAAMtC,SAAS,GAAGgB,OAAO,CAAC4D,YAAY,CAAC7D,KAAK,CAACU,OAAO,CAAC;IACrD,IAAIzB,SAAS,EAAE;MACb,IAAIe,KAAK,CAACP,IAAI,KAAKpB,KAAK,CAACK,SAAS,CAACU,OAAO,EAAE;QAC1CY,KAAK,CAAChB,IAAI,GAAGgB,KAAK,CAACN,MAAM;QACzBM,KAAK,CAACN,MAAM,GAAGjB,SAAS;MAC1B,CAAC,MAAM;QACLuB,KAAK,CAAChB,IAAI,GAAGgB,KAAK,CAACe,KAAK;QACxBf,KAAK,CAACe,KAAK,GAAGtC,SAAS;MACzB;MACAuB,KAAK,CAACP,IAAI,GAAGpB,KAAK,CAACK,SAAS,CAACQ,SAAS;MACtCc,KAAK,CAACf,SAAS,GAAGA,SAAS;IAC7B;IAEA,MAAMqB,OAAO,GAAGL,OAAO,CAAC6D,WAAW,IAAI7D,OAAO,CAAC6D,WAAW,CAAC9D,KAAK,CAACU,OAAO,CAAC;IACzE,IAAIJ,OAAO,EAAE;MACXN,KAAK,CAACM,OAAO,GAAGA,OAAO;IACzB;EACF;AACF;AAEAyD,MAAM,CAACC,OAAO,GAAGrE,SAAS"}