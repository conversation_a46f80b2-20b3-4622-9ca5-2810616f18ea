{"version": 3, "file": "pic-xform.js", "names": ["BaseXform", "require", "StaticXform", "BlipFillXform", "NvPicPrXform", "spPrJSON", "PicXform", "constructor", "map", "tag", "prepare", "model", "options", "index", "render", "xmlStream", "openNode", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "parseClose", "mergeModel", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/pic-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst StaticXform = require('../static-xform');\n\nconst BlipFillXform = require('./blip-fill-xform');\nconst NvPicPrXform = require('./nv-pic-pr-xform');\n\nconst spPrJSON = require('./sp-pr');\n\nclass PicXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xdr:nvPicPr': new NvPicPrXform(),\n      'xdr:blipFill': new BlipFillXform(),\n      'xdr:spPr': new StaticXform(spPrJSON),\n    };\n  }\n\n  get tag() {\n    return 'xdr:pic';\n  }\n\n  prepare(model, options) {\n    model.index = options.index + 1;\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    this.map['xdr:nvPicPr'].render(xmlStream, model);\n    this.map['xdr:blipFill'].render(xmlStream, model);\n    this.map['xdr:spPr'].render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.mergeModel(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // not quite sure how we get here!\n        return true;\n    }\n  }\n}\n\nmodule.exports = PicXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE9C,MAAME,aAAa,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAClD,MAAMG,YAAY,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAEjD,MAAMI,QAAQ,GAAGJ,OAAO,CAAC,SAAS,CAAC;AAEnC,MAAMK,QAAQ,SAASN,SAAS,CAAC;EAC/BO,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,aAAa,EAAE,IAAIJ,YAAY,CAAC,CAAC;MACjC,cAAc,EAAE,IAAID,aAAa,CAAC,CAAC;MACnC,UAAU,EAAE,IAAID,WAAW,CAACG,QAAQ;IACtC,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtBD,KAAK,CAACE,KAAK,GAAGD,OAAO,CAACC,KAAK,GAAG,CAAC;EACjC;EAEAC,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvBI,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACP,GAAG,CAAC;IAE5B,IAAI,CAACD,GAAG,CAAC,aAAa,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAChD,IAAI,CAACH,GAAG,CAAC,cAAc,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IACjD,IAAI,CAACH,GAAG,CAAC,UAAU,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAE7CI,SAAS,CAACE,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACZ,GAAG;QACX,IAAI,CAACa,KAAK,CAAC,CAAC;QACZ;MACF;QACE,IAAI,CAACF,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACW,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACI,UAAU,CAAC,IAAI,CAACL,MAAM,CAACT,KAAK,CAAC;QAClC,IAAI,CAACS,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQL,IAAI;MACV,KAAK,IAAI,CAACZ,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAkB,MAAM,CAACC,OAAO,GAAGtB,QAAQ"}