{"version": 3, "file": "defaultnumformats.js", "names": ["module", "exports", "f"], "sources": ["../../../lib/xlsx/defaultnumformats.js"], "sourcesContent": ["module.exports = {\n  0: {f: 'General'},\n  1: {f: '0'},\n  2: {f: '0.00'},\n  3: {f: '#,##0'},\n  4: {f: '#,##0.00'},\n  9: {f: '0%'},\n  10: {f: '0.00%'},\n  11: {f: '0.00E+00'},\n  12: {f: '# ?/?'},\n  13: {f: '# ??/??'},\n  14: {f: 'mm-dd-yy'},\n  15: {f: 'd-mmm-yy'},\n  16: {f: 'd-mmm'},\n  17: {f: 'mmm-yy'},\n  18: {f: 'h:mm AM/PM'},\n  19: {f: 'h:mm:ss AM/PM'},\n  20: {f: 'h:mm'},\n  21: {f: 'h:mm:ss'},\n  22: {f: 'm/d/yy \"h\":mm'},\n\n  27: {\n    'zh-tw': '[$-404]e/m/d',\n    'zh-cn': 'yyyy\"年\"m\"月\"',\n    'ja-jp': '[$-411]ge.m.d',\n    'ko-kr': 'yyyy\"年\" mm\"月\" dd\"日\"',\n  },\n  28: {\n    'zh-tw': '[$-404]e\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': '[$-411]ggge\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'mm-dd',\n  },\n  29: {\n    'zh-tw': '[$-404]e\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': '[$-411]ggge\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'mm-dd',\n  },\n  30: {'zh-tw': 'm/d/yy ', 'zh-cn': 'm-d-yy', 'ja-jp': 'm/d/yy', 'ko-kr': 'mm-dd-yy'},\n  31: {\n    'zh-tw': 'yyyy\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'yyyy\"年\"m\"月\"d\"日\"',\n    'ja-jp': 'yyyy\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'yyyy\"년\" mm\"월\" dd\"일\"',\n  },\n  32: {\n    'zh-tw': 'hh\"時\"mm\"分\"',\n    'zh-cn': 'h\"时\"mm\"分\"',\n    'ja-jp': 'h\"時\"mm\"分\"',\n    'ko-kr': 'h\"시\" mm\"분\"',\n  },\n  33: {\n    'zh-tw': 'hh\"時\"mm\"分\"ss\"秒\"',\n    'zh-cn': 'h\"时\"mm\"分\"ss\"秒\"',\n    'ja-jp': 'h\"時\"mm\"分\"ss\"秒\"',\n    'ko-kr': 'h\"시\" mm\"분\" ss\"초\"',\n  },\n  34: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"',\n    'zh-cn': '上午/下午 h\"时\"mm\"分\"',\n    'ja-jp': 'yyyy\"年\"m\"月\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  35: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"ss\"秒\"',\n    'zh-cn': '上午/下午 h\"时\"mm\"分\"ss\"秒\"',\n    'ja-jp': 'm\"月\"d\"日\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  36: {\n    'zh-tw': '[$-404]e/m/d',\n    'zh-cn': 'yyyy\"年\"m\"月\"',\n    'ja-jp': '[$-411]ge.m.d',\n    'ko-kr': 'yyyy\"年\" mm\"月\" dd\"日\"',\n  },\n\n  37: {f: '#,##0 ;(#,##0)'},\n  38: {f: '#,##0 ;[Red](#,##0)'},\n  39: {f: '#,##0.00 ;(#,##0.00)'},\n  40: {f: '#,##0.00 ;[Red](#,##0.00)'},\n  45: {f: 'mm:ss'},\n  46: {f: '[h]:mm:ss'},\n  47: {f: 'mmss.0'},\n  48: {f: '##0.0E+0'},\n  49: {f: '@'},\n\n  50: {\n    'zh-tw': '[$-404]e/m/d',\n    'zh-cn': 'yyyy\"年\"m\"月\"',\n    'ja-jp': '[$-411]ge.m.d',\n    'ko-kr': 'yyyy\"年\" mm\"月\" dd\"日\"',\n  },\n  51: {\n    'zh-tw': '[$-404]e\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': '[$-411]ggge\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'mm-dd',\n  },\n  52: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"',\n    'zh-cn': 'yyyy\"年\"m\"月\"',\n    'ja-jp': 'yyyy\"年\"m\"月\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  53: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"ss\"秒\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': 'm\"月\"d\"日\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  54: {\n    'zh-tw': '[$-404]e\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': '[$-411]ggge\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'mm-dd',\n  },\n  55: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"',\n    'zh-cn': '上午/下午 h\"时\"mm\"分\"',\n    'ja-jp': 'yyyy\"年\"m\"月\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  56: {\n    'zh-tw': '上午/下午 hh\"時\"mm\"分\"ss\"秒\"',\n    'zh-cn': '上午/下午 h\"时\"mm\"分\"ss\"秒\"',\n    'ja-jp': 'm\"月\"d\"日\"',\n    'ko-kr': 'yyyy-mm-dd',\n  },\n  57: {\n    'zh-tw': '[$-404]e/m/d',\n    'zh-cn': 'yyyy\"年\"m\"月\"',\n    'ja-jp': '[$-411]ge.m.d',\n    'ko-kr': 'yyyy\"年\" mm\"月\" dd\"日\"',\n  },\n  58: {\n    'zh-tw': '[$-404]e\"年\"m\"月\"d\"日\"',\n    'zh-cn': 'm\"月\"d\"日\"',\n    'ja-jp': '[$-411]ggge\"年\"m\"月\"d\"日\"',\n    'ko-kr': 'mm-dd',\n  },\n\n  59: {'th-th': 't0'},\n  60: {'th-th': 't0.00'},\n  61: {'th-th': 't#,##0'},\n  62: {'th-th': 't#,##0.00'},\n  67: {'th-th': 't0%'},\n  68: {'th-th': 't0.00%'},\n  69: {'th-th': 't# ?/?'},\n  70: {'th-th': 't# ??/??'},\n\n  81: {'th-th': 'd/m/bb'},\n};\n"], "mappings": ";;AAAAA,MAAM,CAACC,OAAO,GAAG;EACf,CAAC,EAAE;IAACC,CAAC,EAAE;EAAS,CAAC;EACjB,CAAC,EAAE;IAACA,CAAC,EAAE;EAAG,CAAC;EACX,CAAC,EAAE;IAACA,CAAC,EAAE;EAAM,CAAC;EACd,CAAC,EAAE;IAACA,CAAC,EAAE;EAAO,CAAC;EACf,CAAC,EAAE;IAACA,CAAC,EAAE;EAAU,CAAC;EAClB,CAAC,EAAE;IAACA,CAAC,EAAE;EAAI,CAAC;EACZ,EAAE,EAAE;IAACA,CAAC,EAAE;EAAO,CAAC;EAChB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAU,CAAC;EACnB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAO,CAAC;EAChB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAS,CAAC;EAClB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAU,CAAC;EACnB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAU,CAAC;EACnB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAO,CAAC;EAChB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAQ,CAAC;EACjB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAY,CAAC;EACrB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAe,CAAC;EACxB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAM,CAAC;EACf,EAAE,EAAE;IAACA,CAAC,EAAE;EAAS,CAAC;EAClB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAe,CAAC;EAExB,EAAE,EAAE;IACF,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,eAAe;IACxB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,qBAAqB;IAC9B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,wBAAwB;IACjC,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,qBAAqB;IAC9B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,wBAAwB;IACjC,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IAAC,OAAO,EAAE,SAAS;IAAE,OAAO,EAAE,QAAQ;IAAE,OAAO,EAAE,QAAQ;IAAE,OAAO,EAAE;EAAU,CAAC;EACnF,EAAE,EAAE;IACF,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,YAAY;IACrB,OAAO,EAAE,WAAW;IACpB,OAAO,EAAE,WAAW;IACpB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE,gBAAgB;IACzB,OAAO,EAAE,gBAAgB;IACzB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,kBAAkB;IAC3B,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,uBAAuB;IAChC,OAAO,EAAE,sBAAsB;IAC/B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,eAAe;IACxB,OAAO,EAAE;EACX,CAAC;EAED,EAAE,EAAE;IAACA,CAAC,EAAE;EAAgB,CAAC;EACzB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAqB,CAAC;EAC9B,EAAE,EAAE;IAACA,CAAC,EAAE;EAAsB,CAAC;EAC/B,EAAE,EAAE;IAACA,CAAC,EAAE;EAA2B,CAAC;EACpC,EAAE,EAAE;IAACA,CAAC,EAAE;EAAO,CAAC;EAChB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAW,CAAC;EACpB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAQ,CAAC;EACjB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAU,CAAC;EACnB,EAAE,EAAE;IAACA,CAAC,EAAE;EAAG,CAAC;EAEZ,EAAE,EAAE;IACF,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,eAAe;IACxB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,qBAAqB;IAC9B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,wBAAwB;IACjC,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,kBAAkB;IAC3B,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,uBAAuB;IAChC,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,qBAAqB;IAC9B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,wBAAwB;IACjC,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,kBAAkB;IAC3B,OAAO,EAAE,iBAAiB;IAC1B,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,uBAAuB;IAChC,OAAO,EAAE,sBAAsB;IAC/B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,aAAa;IACtB,OAAO,EAAE,eAAe;IACxB,OAAO,EAAE;EACX,CAAC;EACD,EAAE,EAAE;IACF,OAAO,EAAE,qBAAqB;IAC9B,OAAO,EAAE,UAAU;IACnB,OAAO,EAAE,wBAAwB;IACjC,OAAO,EAAE;EACX,CAAC;EAED,EAAE,EAAE;IAAC,OAAO,EAAE;EAAI,CAAC;EACnB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAO,CAAC;EACtB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAQ,CAAC;EACvB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAW,CAAC;EAC1B,EAAE,EAAE;IAAC,OAAO,EAAE;EAAK,CAAC;EACpB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAQ,CAAC;EACvB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAQ,CAAC;EACvB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAU,CAAC;EAEzB,EAAE,EAAE;IAAC,OAAO,EAAE;EAAQ;AACxB,CAAC"}