{"version": 3, "file": "iterate-stream.js", "names": ["module", "exports", "iterateStream", "stream", "contents", "on", "data", "push", "resolveStreamEndedPromise", "streamEndedPromise", "Promise", "resolve", "ended", "error", "err", "length", "resume", "race", "once", "pause", "shift", "eventEmitter", "type", "fired", "handler", "removeListener", "addListener"], "sources": ["../../../lib/utils/iterate-stream.js"], "sourcesContent": ["module.exports = async function* iterateStream(stream) {\n  const contents = [];\n  stream.on('data', data => contents.push(data));\n\n  let resolveStreamEndedPromise;\n  const streamEndedPromise = new Promise(resolve => (resolveStreamEndedPromise = resolve));\n\n  let ended = false;\n  stream.on('end', () => {\n    ended = true;\n    resolveStreamEndedPromise();\n  });\n\n  let error = false;\n  stream.on('error', err => {\n    error = err;\n    resolveStreamEndedPromise();\n  });\n\n  while (!ended || contents.length > 0) {\n    if (contents.length === 0) {\n      stream.resume();\n      // eslint-disable-next-line no-await-in-loop\n      await Promise.race([once(stream, 'data'), streamEndedPromise]);\n    } else {\n      stream.pause();\n      const data = contents.shift();\n      yield data;\n    }\n    if (error) throw error;\n  }\n  resolveStreamEndedPromise();\n};\n\nfunction once(eventEmitter, type) {\n  // TODO: Use require('events').once when node v10 is dropped\n  return new Promise(resolve => {\n    let fired = false;\n    const handler = () => {\n      if (!fired) {\n        fired = true;\n        eventEmitter.removeListener(type, handler);\n        resolve();\n      }\n    };\n    eventEmitter.addListener(type, handler);\n  });\n}\n"], "mappings": ";;AAAAA,MAAM,CAACC,OAAO,GAAG,gBAAgBC,aAAaA,CAACC,MAAM,EAAE;EACrD,MAAMC,QAAQ,GAAG,EAAE;EACnBD,MAAM,CAACE,EAAE,CAAC,MAAM,EAAEC,IAAI,IAAIF,QAAQ,CAACG,IAAI,CAACD,IAAI,CAAC,CAAC;EAE9C,IAAIE,yBAAyB;EAC7B,MAAMC,kBAAkB,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAKH,yBAAyB,GAAGG,OAAQ,CAAC;EAExF,IAAIC,KAAK,GAAG,KAAK;EACjBT,MAAM,CAACE,EAAE,CAAC,KAAK,EAAE,MAAM;IACrBO,KAAK,GAAG,IAAI;IACZJ,yBAAyB,CAAC,CAAC;EAC7B,CAAC,CAAC;EAEF,IAAIK,KAAK,GAAG,KAAK;EACjBV,MAAM,CAACE,EAAE,CAAC,OAAO,EAAES,GAAG,IAAI;IACxBD,KAAK,GAAGC,GAAG;IACXN,yBAAyB,CAAC,CAAC;EAC7B,CAAC,CAAC;EAEF,OAAO,CAACI,KAAK,IAAIR,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;IACpC,IAAIX,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE;MACzBZ,MAAM,CAACa,MAAM,CAAC,CAAC;MACf;MACA,MAAMN,OAAO,CAACO,IAAI,CAAC,CAACC,IAAI,CAACf,MAAM,EAAE,MAAM,CAAC,EAAEM,kBAAkB,CAAC,CAAC;IAChE,CAAC,MAAM;MACLN,MAAM,CAACgB,KAAK,CAAC,CAAC;MACd,MAAMb,IAAI,GAAGF,QAAQ,CAACgB,KAAK,CAAC,CAAC;MAC7B,MAAMd,IAAI;IACZ;IACA,IAAIO,KAAK,EAAE,MAAMA,KAAK;EACxB;EACAL,yBAAyB,CAAC,CAAC;AAC7B,CAAC;AAED,SAASU,IAAIA,CAACG,YAAY,EAAEC,IAAI,EAAE;EAChC;EACA,OAAO,IAAIZ,OAAO,CAACC,OAAO,IAAI;IAC5B,IAAIY,KAAK,GAAG,KAAK;IACjB,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACD,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI;QACZF,YAAY,CAACI,cAAc,CAACH,IAAI,EAAEE,OAAO,CAAC;QAC1Cb,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IACDU,YAAY,CAACK,WAAW,CAACJ,IAAI,EAAEE,OAAO,CAAC;EACzC,CAAC,CAAC;AACJ"}