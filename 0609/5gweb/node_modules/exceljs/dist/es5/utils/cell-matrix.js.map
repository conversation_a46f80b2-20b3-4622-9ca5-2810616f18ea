{"version": 3, "file": "cell-matrix.js", "names": ["_", "require", "co<PERSON><PERSON><PERSON>", "CellMatrix", "constructor", "template", "sheets", "addCell", "addressStr", "addCellEx", "decodeEx", "getCell", "findCellEx", "find<PERSON>ell", "findCellAt", "sheetName", "rowNumber", "colNumber", "sheet", "row", "address", "top", "bottom", "col", "left", "right", "getCellAt", "getCellEx", "create", "findSheet", "findSheetRow", "findRowCell", "cell", "n2l", "removeCellEx", "forEachInSheet", "callback", "for<PERSON>ach", "each", "map", "results", "push", "name", "undefined", "Object", "assign", "JSON", "parse", "stringify", "spliceRows", "start", "numDelete", "numInsert", "inserts", "i", "splice", "spliceColumns", "module", "exports"], "sources": ["../../../lib/utils/cell-matrix.js"], "sourcesContent": ["const _ = require('./under-dash');\nconst colCache = require('./col-cache');\n\nclass CellMatrix {\n  constructor(template) {\n    this.template = template;\n    this.sheets = {};\n  }\n\n  addCell(addressStr) {\n    this.addCellEx(colCache.decodeEx(addressStr));\n  }\n\n  getCell(addressStr) {\n    return this.findCellEx(colCache.decodeEx(addressStr), true);\n  }\n\n  findCell(addressStr) {\n    return this.findCellEx(colCache.decodeEx(addressStr), false);\n  }\n\n  findCellAt(sheetName, rowNumber, colNumber) {\n    const sheet = this.sheets[sheetName];\n    const row = sheet && sheet[rowNumber];\n    return row && row[colNumber];\n  }\n\n  addCellEx(address) {\n    if (address.top) {\n      for (let row = address.top; row <= address.bottom; row++) {\n        for (let col = address.left; col <= address.right; col++) {\n          this.getCellAt(address.sheetName, row, col);\n        }\n      }\n    } else {\n      this.findCellEx(address, true);\n    }\n  }\n\n  getCellEx(address) {\n    return this.findCellEx(address, true);\n  }\n\n  findCellEx(address, create) {\n    const sheet = this.findSheet(address, create);\n    const row = this.findSheetRow(sheet, address, create);\n    return this.findRowCell(row, address, create);\n  }\n\n  getCellAt(sheetName, rowNumber, colNumber) {\n    const sheet = this.sheets[sheetName] || (this.sheets[sheetName] = []);\n    const row = sheet[rowNumber] || (sheet[rowNumber] = []);\n    const cell =\n      row[colNumber] ||\n      (row[colNumber] = {\n        sheetName,\n        address: colCache.n2l(colNumber) + rowNumber,\n        row: rowNumber,\n        col: colNumber,\n      });\n    return cell;\n  }\n\n  removeCellEx(address) {\n    const sheet = this.findSheet(address);\n    if (!sheet) {\n      return;\n    }\n    const row = this.findSheetRow(sheet, address);\n    if (!row) {\n      return;\n    }\n    delete row[address.col];\n  }\n\n  forEachInSheet(sheetName, callback) {\n    const sheet = this.sheets[sheetName];\n    if (sheet) {\n      sheet.forEach((row, rowNumber) => {\n        if (row) {\n          row.forEach((cell, colNumber) => {\n            if (cell) {\n              callback(cell, rowNumber, colNumber);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  forEach(callback) {\n    _.each(this.sheets, (sheet, sheetName) => {\n      this.forEachInSheet(sheetName, callback);\n    });\n  }\n\n  map(callback) {\n    const results = [];\n    this.forEach(cell => {\n      results.push(callback(cell));\n    });\n    return results;\n  }\n\n  findSheet(address, create) {\n    const name = address.sheetName;\n    if (this.sheets[name]) {\n      return this.sheets[name];\n    }\n    if (create) {\n      return (this.sheets[name] = []);\n    }\n    return undefined;\n  }\n\n  findSheetRow(sheet, address, create) {\n    const {row} = address;\n    if (sheet && sheet[row]) {\n      return sheet[row];\n    }\n    if (create) {\n      return (sheet[row] = []);\n    }\n    return undefined;\n  }\n\n  findRowCell(row, address, create) {\n    const {col} = address;\n    if (row && row[col]) {\n      return row[col];\n    }\n    if (create) {\n      return (row[col] = this.template\n        ? Object.assign(address, JSON.parse(JSON.stringify(this.template)))\n        : address);\n    }\n    return undefined;\n  }\n\n  spliceRows(sheetName, start, numDelete, numInsert) {\n    const sheet = this.sheets[sheetName];\n    if (sheet) {\n      const inserts = [];\n      for (let i = 0; i < numInsert; i++) {\n        inserts.push([]);\n      }\n      sheet.splice(start, numDelete, ...inserts);\n    }\n  }\n\n  spliceColumns(sheetName, start, numDelete, numInsert) {\n    const sheet = this.sheets[sheetName];\n    if (sheet) {\n      const inserts = [];\n      for (let i = 0; i < numInsert; i++) {\n        inserts.push(null);\n      }\n      _.each(sheet, row => {\n        row.splice(start, numDelete, ...inserts);\n      });\n    }\n  }\n}\n\nmodule.exports = CellMatrix;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,cAAc,CAAC;AACjC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEvC,MAAME,UAAU,CAAC;EACfC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAClB;EAEAC,OAAOA,CAACC,UAAU,EAAE;IAClB,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACQ,QAAQ,CAACF,UAAU,CAAC,CAAC;EAC/C;EAEAG,OAAOA,CAACH,UAAU,EAAE;IAClB,OAAO,IAAI,CAACI,UAAU,CAACV,QAAQ,CAACQ,QAAQ,CAACF,UAAU,CAAC,EAAE,IAAI,CAAC;EAC7D;EAEAK,QAAQA,CAACL,UAAU,EAAE;IACnB,OAAO,IAAI,CAACI,UAAU,CAACV,QAAQ,CAACQ,QAAQ,CAACF,UAAU,CAAC,EAAE,KAAK,CAAC;EAC9D;EAEAM,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAC1C,MAAMC,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACS,SAAS,CAAC;IACpC,MAAMI,GAAG,GAAGD,KAAK,IAAIA,KAAK,CAACF,SAAS,CAAC;IACrC,OAAOG,GAAG,IAAIA,GAAG,CAACF,SAAS,CAAC;EAC9B;EAEAR,SAASA,CAACW,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,GAAG,EAAE;MACf,KAAK,IAAIF,GAAG,GAAGC,OAAO,CAACC,GAAG,EAAEF,GAAG,IAAIC,OAAO,CAACE,MAAM,EAAEH,GAAG,EAAE,EAAE;QACxD,KAAK,IAAII,GAAG,GAAGH,OAAO,CAACI,IAAI,EAAED,GAAG,IAAIH,OAAO,CAACK,KAAK,EAAEF,GAAG,EAAE,EAAE;UACxD,IAAI,CAACG,SAAS,CAACN,OAAO,CAACL,SAAS,EAAEI,GAAG,EAAEI,GAAG,CAAC;QAC7C;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACX,UAAU,CAACQ,OAAO,EAAE,IAAI,CAAC;IAChC;EACF;EAEAO,SAASA,CAACP,OAAO,EAAE;IACjB,OAAO,IAAI,CAACR,UAAU,CAACQ,OAAO,EAAE,IAAI,CAAC;EACvC;EAEAR,UAAUA,CAACQ,OAAO,EAAEQ,MAAM,EAAE;IAC1B,MAAMV,KAAK,GAAG,IAAI,CAACW,SAAS,CAACT,OAAO,EAAEQ,MAAM,CAAC;IAC7C,MAAMT,GAAG,GAAG,IAAI,CAACW,YAAY,CAACZ,KAAK,EAAEE,OAAO,EAAEQ,MAAM,CAAC;IACrD,OAAO,IAAI,CAACG,WAAW,CAACZ,GAAG,EAAEC,OAAO,EAAEQ,MAAM,CAAC;EAC/C;EAEAF,SAASA,CAACX,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACzC,MAAMC,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACS,SAAS,CAAC,KAAK,IAAI,CAACT,MAAM,CAACS,SAAS,CAAC,GAAG,EAAE,CAAC;IACrE,MAAMI,GAAG,GAAGD,KAAK,CAACF,SAAS,CAAC,KAAKE,KAAK,CAACF,SAAS,CAAC,GAAG,EAAE,CAAC;IACvD,MAAMgB,IAAI,GACRb,GAAG,CAACF,SAAS,CAAC,KACbE,GAAG,CAACF,SAAS,CAAC,GAAG;MAChBF,SAAS;MACTK,OAAO,EAAElB,QAAQ,CAAC+B,GAAG,CAAChB,SAAS,CAAC,GAAGD,SAAS;MAC5CG,GAAG,EAAEH,SAAS;MACdO,GAAG,EAAEN;IACP,CAAC,CAAC;IACJ,OAAOe,IAAI;EACb;EAEAE,YAAYA,CAACd,OAAO,EAAE;IACpB,MAAMF,KAAK,GAAG,IAAI,CAACW,SAAS,CAACT,OAAO,CAAC;IACrC,IAAI,CAACF,KAAK,EAAE;MACV;IACF;IACA,MAAMC,GAAG,GAAG,IAAI,CAACW,YAAY,CAACZ,KAAK,EAAEE,OAAO,CAAC;IAC7C,IAAI,CAACD,GAAG,EAAE;MACR;IACF;IACA,OAAOA,GAAG,CAACC,OAAO,CAACG,GAAG,CAAC;EACzB;EAEAY,cAAcA,CAACpB,SAAS,EAAEqB,QAAQ,EAAE;IAClC,MAAMlB,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACS,SAAS,CAAC;IACpC,IAAIG,KAAK,EAAE;MACTA,KAAK,CAACmB,OAAO,CAAC,CAAClB,GAAG,EAAEH,SAAS,KAAK;QAChC,IAAIG,GAAG,EAAE;UACPA,GAAG,CAACkB,OAAO,CAAC,CAACL,IAAI,EAAEf,SAAS,KAAK;YAC/B,IAAIe,IAAI,EAAE;cACRI,QAAQ,CAACJ,IAAI,EAAEhB,SAAS,EAAEC,SAAS,CAAC;YACtC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EAEAoB,OAAOA,CAACD,QAAQ,EAAE;IAChBpC,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAChC,MAAM,EAAE,CAACY,KAAK,EAAEH,SAAS,KAAK;MACxC,IAAI,CAACoB,cAAc,CAACpB,SAAS,EAAEqB,QAAQ,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEAG,GAAGA,CAACH,QAAQ,EAAE;IACZ,MAAMI,OAAO,GAAG,EAAE;IAClB,IAAI,CAACH,OAAO,CAACL,IAAI,IAAI;MACnBQ,OAAO,CAACC,IAAI,CAACL,QAAQ,CAACJ,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,OAAOQ,OAAO;EAChB;EAEAX,SAASA,CAACT,OAAO,EAAEQ,MAAM,EAAE;IACzB,MAAMc,IAAI,GAAGtB,OAAO,CAACL,SAAS;IAC9B,IAAI,IAAI,CAACT,MAAM,CAACoC,IAAI,CAAC,EAAE;MACrB,OAAO,IAAI,CAACpC,MAAM,CAACoC,IAAI,CAAC;IAC1B;IACA,IAAId,MAAM,EAAE;MACV,OAAQ,IAAI,CAACtB,MAAM,CAACoC,IAAI,CAAC,GAAG,EAAE;IAChC;IACA,OAAOC,SAAS;EAClB;EAEAb,YAAYA,CAACZ,KAAK,EAAEE,OAAO,EAAEQ,MAAM,EAAE;IACnC,MAAM;MAACT;IAAG,CAAC,GAAGC,OAAO;IACrB,IAAIF,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC,EAAE;MACvB,OAAOD,KAAK,CAACC,GAAG,CAAC;IACnB;IACA,IAAIS,MAAM,EAAE;MACV,OAAQV,KAAK,CAACC,GAAG,CAAC,GAAG,EAAE;IACzB;IACA,OAAOwB,SAAS;EAClB;EAEAZ,WAAWA,CAACZ,GAAG,EAAEC,OAAO,EAAEQ,MAAM,EAAE;IAChC,MAAM;MAACL;IAAG,CAAC,GAAGH,OAAO;IACrB,IAAID,GAAG,IAAIA,GAAG,CAACI,GAAG,CAAC,EAAE;MACnB,OAAOJ,GAAG,CAACI,GAAG,CAAC;IACjB;IACA,IAAIK,MAAM,EAAE;MACV,OAAQT,GAAG,CAACI,GAAG,CAAC,GAAG,IAAI,CAAClB,QAAQ,GAC5BuC,MAAM,CAACC,MAAM,CAACzB,OAAO,EAAE0B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC3C,QAAQ,CAAC,CAAC,CAAC,GACjEe,OAAO;IACb;IACA,OAAOuB,SAAS;EAClB;EAEAM,UAAUA,CAAClC,SAAS,EAAEmC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACjD,MAAMlC,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACS,SAAS,CAAC;IACpC,IAAIG,KAAK,EAAE;MACT,MAAMmC,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;QAClCD,OAAO,CAACZ,IAAI,CAAC,EAAE,CAAC;MAClB;MACAvB,KAAK,CAACqC,MAAM,CAACL,KAAK,EAAEC,SAAS,EAAE,GAAGE,OAAO,CAAC;IAC5C;EACF;EAEAG,aAAaA,CAACzC,SAAS,EAAEmC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACpD,MAAMlC,KAAK,GAAG,IAAI,CAACZ,MAAM,CAACS,SAAS,CAAC;IACpC,IAAIG,KAAK,EAAE;MACT,MAAMmC,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;QAClCD,OAAO,CAACZ,IAAI,CAAC,IAAI,CAAC;MACpB;MACAzC,CAAC,CAACsC,IAAI,CAACpB,KAAK,EAAEC,GAAG,IAAI;QACnBA,GAAG,CAACoC,MAAM,CAACL,KAAK,EAAEC,SAAS,EAAE,GAAGE,OAAO,CAAC;MAC1C,CAAC,CAAC;IACJ;EACF;AACF;AAEAI,MAAM,CAACC,OAAO,GAAGvD,UAAU"}