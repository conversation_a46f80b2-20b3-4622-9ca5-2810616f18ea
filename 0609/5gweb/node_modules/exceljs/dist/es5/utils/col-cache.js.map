{"version": 3, "file": "col-cache.js", "names": ["addressRegex", "co<PERSON><PERSON><PERSON>", "_dictionary", "_l2nFill", "_l2n", "_n2l", "_level", "n", "_fill", "level", "c", "v", "l1", "l2", "l3", "Error", "Math", "floor", "l2n", "l", "length", "n2l", "_hash", "validateAddress", "value", "test", "decode<PERSON>ddress", "addr", "hasCol", "col", "colNumber", "hasRow", "row", "rowNumber", "i", "char", "charCodeAt", "undefined", "address", "$col$row", "get<PERSON><PERSON><PERSON>", "r", "decode", "parts", "split", "tl", "br", "result", "top", "min", "left", "bottom", "max", "right", "dimensions", "decodeEx", "groups", "match", "sheetName", "reference", "startsWith", "error", "encodeAddress", "encode", "arguments", "inRange", "range", "module", "exports"], "sources": ["../../../lib/utils/col-cache.js"], "sourcesContent": ["const addressRegex = /^[A-Z]+\\d+$/;\n// =========================================================================\n// Column Letter to Number conversion\nconst colCache = {\n  _dictionary: [\n    'A',\n    'B',\n    'C',\n    'D',\n    'E',\n    'F',\n    'G',\n    'H',\n    'I',\n    'J',\n    'K',\n    'L',\n    'M',\n    'N',\n    'O',\n    'P',\n    'Q',\n    'R',\n    'S',\n    'T',\n    'U',\n    'V',\n    'W',\n    'X',\n    'Y',\n    'Z',\n  ],\n  _l2nFill: 0,\n  _l2n: {},\n  _n2l: [],\n  _level(n) {\n    if (n <= 26) {\n      return 1;\n    }\n    if (n <= 26 * 26) {\n      return 2;\n    }\n    return 3;\n  },\n  _fill(level) {\n    let c;\n    let v;\n    let l1;\n    let l2;\n    let l3;\n    let n = 1;\n    if (level >= 4) {\n      throw new Error('Out of bounds. Excel supports columns from 1 to 16384');\n    }\n    if (this._l2nFill < 1 && level >= 1) {\n      while (n <= 26) {\n        c = this._dictionary[n - 1];\n        this._n2l[n] = c;\n        this._l2n[c] = n;\n        n++;\n      }\n      this._l2nFill = 1;\n    }\n    if (this._l2nFill < 2 && level >= 2) {\n      n = 27;\n      while (n <= 26 + (26 * 26)) {\n        v = n - (26 + 1);\n        l1 = v % 26;\n        l2 = Math.floor(v / 26);\n        c = this._dictionary[l2] + this._dictionary[l1];\n        this._n2l[n] = c;\n        this._l2n[c] = n;\n        n++;\n      }\n      this._l2nFill = 2;\n    }\n    if (this._l2nFill < 3 && level >= 3) {\n      n = 26 + (26 * 26) + 1;\n      while (n <= 16384) {\n        v = n - ((26 * 26) + 26 + 1);\n        l1 = v % 26;\n        l2 = Math.floor(v / 26) % 26;\n        l3 = Math.floor(v / (26 * 26));\n        c = this._dictionary[l3] + this._dictionary[l2] + this._dictionary[l1];\n        this._n2l[n] = c;\n        this._l2n[c] = n;\n        n++;\n      }\n      this._l2nFill = 3;\n    }\n  },\n  l2n(l) {\n    if (!this._l2n[l]) {\n      this._fill(l.length);\n    }\n    if (!this._l2n[l]) {\n      throw new Error(`Out of bounds. Invalid column letter: ${l}`);\n    }\n    return this._l2n[l];\n  },\n  n2l(n) {\n    if (n < 1 || n > 16384) {\n      throw new Error(`${n} is out of bounds. Excel supports columns from 1 to 16384`);\n    }\n    if (!this._n2l[n]) {\n      this._fill(this._level(n));\n    }\n    return this._n2l[n];\n  },\n\n  // =========================================================================\n  // Address processing\n  _hash: {},\n\n  // check if value looks like an address\n  validateAddress(value) {\n    if (!addressRegex.test(value)) {\n      throw new Error(`Invalid Address: ${value}`);\n    }\n    return true;\n  },\n\n  // convert address string into structure\n  decodeAddress(value) {\n    const addr = value.length < 5 && this._hash[value];\n    if (addr) {\n      return addr;\n    }\n    let hasCol = false;\n    let col = '';\n    let colNumber = 0;\n    let hasRow = false;\n    let row = '';\n    let rowNumber = 0;\n    for (let i = 0, char; i < value.length; i++) {\n      char = value.charCodeAt(i);\n      // col should before row\n      if (!hasRow && char >= 65 && char <= 90) {\n        // 65 = 'A'.charCodeAt(0)\n        // 90 = 'Z'.charCodeAt(0)\n        hasCol = true;\n        col += value[i];\n        // colNumber starts from 1\n        colNumber = (colNumber * 26) + char - 64;\n      } else if (char >= 48 && char <= 57) {\n        // 48 = '0'.charCodeAt(0)\n        // 57 = '9'.charCodeAt(0)\n        hasRow = true;\n        row += value[i];\n        // rowNumber starts from 0\n        rowNumber = (rowNumber * 10) + char - 48;\n      } else if (hasRow && hasCol && char !== 36) {\n        // 36 = '$'.charCodeAt(0)\n        break;\n      }\n    }\n    if (!hasCol) {\n      colNumber = undefined;\n    } else if (colNumber > 16384) {\n      throw new Error(`Out of bounds. Invalid column letter: ${col}`);\n    }\n    if (!hasRow) {\n      rowNumber = undefined;\n    }\n\n    // in case $row$col\n    value = col + row;\n\n    const address = {\n      address: value,\n      col: colNumber,\n      row: rowNumber,\n      $col$row: `$${col}$${row}`,\n    };\n\n    // mem fix - cache only the tl 100x100 square\n    if (colNumber <= 100 && rowNumber <= 100) {\n      this._hash[value] = address;\n      this._hash[address.$col$row] = address;\n    }\n\n    return address;\n  },\n\n  // convert r,c into structure (if only 1 arg, assume r is address string)\n  getAddress(r, c) {\n    if (c) {\n      const address = this.n2l(c) + r;\n      return this.decodeAddress(address);\n    }\n    return this.decodeAddress(r);\n  },\n\n  // convert [address], [tl:br] into address structures\n  decode(value) {\n    const parts = value.split(':');\n    if (parts.length === 2) {\n      const tl = this.decodeAddress(parts[0]);\n      const br = this.decodeAddress(parts[1]);\n      const result = {\n        top: Math.min(tl.row, br.row),\n        left: Math.min(tl.col, br.col),\n        bottom: Math.max(tl.row, br.row),\n        right: Math.max(tl.col, br.col),\n      };\n      // reconstruct tl, br and dimensions\n      result.tl = this.n2l(result.left) + result.top;\n      result.br = this.n2l(result.right) + result.bottom;\n      result.dimensions = `${result.tl}:${result.br}`;\n      return result;\n    }\n    return this.decodeAddress(value);\n  },\n\n  // convert [sheetName!][$]col[$]row[[$]col[$]row] into address or range structures\n  decodeEx(value) {\n    const groups = value.match(/(?:(?:(?:'((?:[^']|'')*)')|([^'^ !]*))!)?(.*)/);\n\n    const sheetName = groups[1] || groups[2]; // Qouted and unqouted groups\n    const reference = groups[3]; // Remaining address\n\n    const parts = reference.split(':');\n    if (parts.length > 1) {\n      let tl = this.decodeAddress(parts[0]);\n      let br = this.decodeAddress(parts[1]);\n      const top = Math.min(tl.row, br.row);\n      const left = Math.min(tl.col, br.col);\n      const bottom = Math.max(tl.row, br.row);\n      const right = Math.max(tl.col, br.col);\n\n      tl = this.n2l(left) + top;\n      br = this.n2l(right) + bottom;\n\n      return {\n        top,\n        left,\n        bottom,\n        right,\n        sheetName,\n        tl: {address: tl, col: left, row: top, $col$row: `$${this.n2l(left)}$${top}`, sheetName},\n        br: {\n          address: br,\n          col: right,\n          row: bottom,\n          $col$row: `$${this.n2l(right)}$${bottom}`,\n          sheetName,\n        },\n        dimensions: `${tl}:${br}`,\n      };\n    }\n    if (reference.startsWith('#')) {\n      return sheetName ? {sheetName, error: reference} : {error: reference};\n    }\n\n    const address = this.decodeAddress(reference);\n    return sheetName ? {sheetName, ...address} : address;\n  },\n\n  // convert row,col into address string\n  encodeAddress(row, col) {\n    return colCache.n2l(col) + row;\n  },\n\n  // convert row,col into string address or t,l,b,r into range\n  encode() {\n    switch (arguments.length) {\n      case 2:\n        return colCache.encodeAddress(arguments[0], arguments[1]);\n      case 4:\n        return `${colCache.encodeAddress(arguments[0], arguments[1])}:${colCache.encodeAddress(\n          arguments[2],\n          arguments[3]\n        )}`;\n      default:\n        throw new Error('Can only encode with 2 or 4 arguments');\n    }\n  },\n\n  // return true if address is contained within range\n  inRange(range, address) {\n    const [left, top, , right, bottom] = range;\n    const [col, row] = address;\n    return col >= left && col <= right && row >= top && row <= bottom;\n  },\n};\n\nmodule.exports = colCache;\n"], "mappings": ";;AAAA,MAAMA,YAAY,GAAG,aAAa;AAClC;AACA;AACA,MAAMC,QAAQ,GAAG;EACfC,WAAW,EAAE,CACX,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;EACDC,QAAQ,EAAE,CAAC;EACXC,IAAI,EAAE,CAAC,CAAC;EACRC,IAAI,EAAE,EAAE;EACRC,MAAMA,CAACC,CAAC,EAAE;IACR,IAAIA,CAAC,IAAI,EAAE,EAAE;MACX,OAAO,CAAC;IACV;IACA,IAAIA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;MAChB,OAAO,CAAC;IACV;IACA,OAAO,CAAC;EACV,CAAC;EACDC,KAAKA,CAACC,KAAK,EAAE;IACX,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIP,CAAC,GAAG,CAAC;IACT,IAAIE,KAAK,IAAI,CAAC,EAAE;MACd,MAAM,IAAIM,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACA,IAAI,IAAI,CAACZ,QAAQ,GAAG,CAAC,IAAIM,KAAK,IAAI,CAAC,EAAE;MACnC,OAAOF,CAAC,IAAI,EAAE,EAAE;QACdG,CAAC,GAAG,IAAI,CAACR,WAAW,CAACK,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,GAAGG,CAAC;QAChB,IAAI,CAACN,IAAI,CAACM,CAAC,CAAC,GAAGH,CAAC;QAChBA,CAAC,EAAE;MACL;MACA,IAAI,CAACJ,QAAQ,GAAG,CAAC;IACnB;IACA,IAAI,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAIM,KAAK,IAAI,CAAC,EAAE;MACnCF,CAAC,GAAG,EAAE;MACN,OAAOA,CAAC,IAAI,EAAE,GAAI,EAAE,GAAG,EAAG,EAAE;QAC1BI,CAAC,GAAGJ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAChBK,EAAE,GAAGD,CAAC,GAAG,EAAE;QACXE,EAAE,GAAGG,IAAI,CAACC,KAAK,CAACN,CAAC,GAAG,EAAE,CAAC;QACvBD,CAAC,GAAG,IAAI,CAACR,WAAW,CAACW,EAAE,CAAC,GAAG,IAAI,CAACX,WAAW,CAACU,EAAE,CAAC;QAC/C,IAAI,CAACP,IAAI,CAACE,CAAC,CAAC,GAAGG,CAAC;QAChB,IAAI,CAACN,IAAI,CAACM,CAAC,CAAC,GAAGH,CAAC;QAChBA,CAAC,EAAE;MACL;MACA,IAAI,CAACJ,QAAQ,GAAG,CAAC;IACnB;IACA,IAAI,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAIM,KAAK,IAAI,CAAC,EAAE;MACnCF,CAAC,GAAG,EAAE,GAAI,EAAE,GAAG,EAAG,GAAG,CAAC;MACtB,OAAOA,CAAC,IAAI,KAAK,EAAE;QACjBI,CAAC,GAAGJ,CAAC,IAAK,EAAE,GAAG,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QAC5BK,EAAE,GAAGD,CAAC,GAAG,EAAE;QACXE,EAAE,GAAGG,IAAI,CAACC,KAAK,CAACN,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;QAC5BG,EAAE,GAAGE,IAAI,CAACC,KAAK,CAACN,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9BD,CAAC,GAAG,IAAI,CAACR,WAAW,CAACY,EAAE,CAAC,GAAG,IAAI,CAACZ,WAAW,CAACW,EAAE,CAAC,GAAG,IAAI,CAACX,WAAW,CAACU,EAAE,CAAC;QACtE,IAAI,CAACP,IAAI,CAACE,CAAC,CAAC,GAAGG,CAAC;QAChB,IAAI,CAACN,IAAI,CAACM,CAAC,CAAC,GAAGH,CAAC;QAChBA,CAAC,EAAE;MACL;MACA,IAAI,CAACJ,QAAQ,GAAG,CAAC;IACnB;EACF,CAAC;EACDe,GAAGA,CAACC,CAAC,EAAE;IACL,IAAI,CAAC,IAAI,CAACf,IAAI,CAACe,CAAC,CAAC,EAAE;MACjB,IAAI,CAACX,KAAK,CAACW,CAAC,CAACC,MAAM,CAAC;IACtB;IACA,IAAI,CAAC,IAAI,CAAChB,IAAI,CAACe,CAAC,CAAC,EAAE;MACjB,MAAM,IAAIJ,KAAK,CAAE,yCAAwCI,CAAE,EAAC,CAAC;IAC/D;IACA,OAAO,IAAI,CAACf,IAAI,CAACe,CAAC,CAAC;EACrB,CAAC;EACDE,GAAGA,CAACd,CAAC,EAAE;IACL,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,KAAK,EAAE;MACtB,MAAM,IAAIQ,KAAK,CAAE,GAAER,CAAE,2DAA0D,CAAC;IAClF;IACA,IAAI,CAAC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,EAAE;MACjB,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,MAAM,CAACC,CAAC,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC;EACrB,CAAC;EAED;EACA;EACAe,KAAK,EAAE,CAAC,CAAC;EAET;EACAC,eAAeA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACxB,YAAY,CAACyB,IAAI,CAACD,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIT,KAAK,CAAE,oBAAmBS,KAAM,EAAC,CAAC;IAC9C;IACA,OAAO,IAAI;EACb,CAAC;EAED;EACAE,aAAaA,CAACF,KAAK,EAAE;IACnB,MAAMG,IAAI,GAAGH,KAAK,CAACJ,MAAM,GAAG,CAAC,IAAI,IAAI,CAACE,KAAK,CAACE,KAAK,CAAC;IAClD,IAAIG,IAAI,EAAE;MACR,OAAOA,IAAI;IACb;IACA,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,EAAED,CAAC,GAAGV,KAAK,CAACJ,MAAM,EAAEc,CAAC,EAAE,EAAE;MAC3CC,IAAI,GAAGX,KAAK,CAACY,UAAU,CAACF,CAAC,CAAC;MAC1B;MACA,IAAI,CAACH,MAAM,IAAII,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;QACvC;QACA;QACAP,MAAM,GAAG,IAAI;QACbC,GAAG,IAAIL,KAAK,CAACU,CAAC,CAAC;QACf;QACAJ,SAAS,GAAIA,SAAS,GAAG,EAAE,GAAIK,IAAI,GAAG,EAAE;MAC1C,CAAC,MAAM,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;QACnC;QACA;QACAJ,MAAM,GAAG,IAAI;QACbC,GAAG,IAAIR,KAAK,CAACU,CAAC,CAAC;QACf;QACAD,SAAS,GAAIA,SAAS,GAAG,EAAE,GAAIE,IAAI,GAAG,EAAE;MAC1C,CAAC,MAAM,IAAIJ,MAAM,IAAIH,MAAM,IAAIO,IAAI,KAAK,EAAE,EAAE;QAC1C;QACA;MACF;IACF;IACA,IAAI,CAACP,MAAM,EAAE;MACXE,SAAS,GAAGO,SAAS;IACvB,CAAC,MAAM,IAAIP,SAAS,GAAG,KAAK,EAAE;MAC5B,MAAM,IAAIf,KAAK,CAAE,yCAAwCc,GAAI,EAAC,CAAC;IACjE;IACA,IAAI,CAACE,MAAM,EAAE;MACXE,SAAS,GAAGI,SAAS;IACvB;;IAEA;IACAb,KAAK,GAAGK,GAAG,GAAGG,GAAG;IAEjB,MAAMM,OAAO,GAAG;MACdA,OAAO,EAAEd,KAAK;MACdK,GAAG,EAAEC,SAAS;MACdE,GAAG,EAAEC,SAAS;MACdM,QAAQ,EAAG,IAAGV,GAAI,IAAGG,GAAI;IAC3B,CAAC;;IAED;IACA,IAAIF,SAAS,IAAI,GAAG,IAAIG,SAAS,IAAI,GAAG,EAAE;MACxC,IAAI,CAACX,KAAK,CAACE,KAAK,CAAC,GAAGc,OAAO;MAC3B,IAAI,CAAChB,KAAK,CAACgB,OAAO,CAACC,QAAQ,CAAC,GAAGD,OAAO;IACxC;IAEA,OAAOA,OAAO;EAChB,CAAC;EAED;EACAE,UAAUA,CAACC,CAAC,EAAE/B,CAAC,EAAE;IACf,IAAIA,CAAC,EAAE;MACL,MAAM4B,OAAO,GAAG,IAAI,CAACjB,GAAG,CAACX,CAAC,CAAC,GAAG+B,CAAC;MAC/B,OAAO,IAAI,CAACf,aAAa,CAACY,OAAO,CAAC;IACpC;IACA,OAAO,IAAI,CAACZ,aAAa,CAACe,CAAC,CAAC;EAC9B,CAAC;EAED;EACAC,MAAMA,CAAClB,KAAK,EAAE;IACZ,MAAMmB,KAAK,GAAGnB,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC;IAC9B,IAAID,KAAK,CAACvB,MAAM,KAAK,CAAC,EAAE;MACtB,MAAMyB,EAAE,GAAG,IAAI,CAACnB,aAAa,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC;MACvC,MAAMG,EAAE,GAAG,IAAI,CAACpB,aAAa,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC;MACvC,MAAMI,MAAM,GAAG;QACbC,GAAG,EAAEhC,IAAI,CAACiC,GAAG,CAACJ,EAAE,CAACb,GAAG,EAAEc,EAAE,CAACd,GAAG,CAAC;QAC7BkB,IAAI,EAAElC,IAAI,CAACiC,GAAG,CAACJ,EAAE,CAAChB,GAAG,EAAEiB,EAAE,CAACjB,GAAG,CAAC;QAC9BsB,MAAM,EAAEnC,IAAI,CAACoC,GAAG,CAACP,EAAE,CAACb,GAAG,EAAEc,EAAE,CAACd,GAAG,CAAC;QAChCqB,KAAK,EAAErC,IAAI,CAACoC,GAAG,CAACP,EAAE,CAAChB,GAAG,EAAEiB,EAAE,CAACjB,GAAG;MAChC,CAAC;MACD;MACAkB,MAAM,CAACF,EAAE,GAAG,IAAI,CAACxB,GAAG,CAAC0B,MAAM,CAACG,IAAI,CAAC,GAAGH,MAAM,CAACC,GAAG;MAC9CD,MAAM,CAACD,EAAE,GAAG,IAAI,CAACzB,GAAG,CAAC0B,MAAM,CAACM,KAAK,CAAC,GAAGN,MAAM,CAACI,MAAM;MAClDJ,MAAM,CAACO,UAAU,GAAI,GAAEP,MAAM,CAACF,EAAG,IAAGE,MAAM,CAACD,EAAG,EAAC;MAC/C,OAAOC,MAAM;IACf;IACA,OAAO,IAAI,CAACrB,aAAa,CAACF,KAAK,CAAC;EAClC,CAAC;EAED;EACA+B,QAAQA,CAAC/B,KAAK,EAAE;IACd,MAAMgC,MAAM,GAAGhC,KAAK,CAACiC,KAAK,CAAC,+CAA+C,CAAC;IAE3E,MAAMC,SAAS,GAAGF,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAMG,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,MAAMb,KAAK,GAAGgB,SAAS,CAACf,KAAK,CAAC,GAAG,CAAC;IAClC,IAAID,KAAK,CAACvB,MAAM,GAAG,CAAC,EAAE;MACpB,IAAIyB,EAAE,GAAG,IAAI,CAACnB,aAAa,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC;MACrC,IAAIG,EAAE,GAAG,IAAI,CAACpB,aAAa,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC;MACrC,MAAMK,GAAG,GAAGhC,IAAI,CAACiC,GAAG,CAACJ,EAAE,CAACb,GAAG,EAAEc,EAAE,CAACd,GAAG,CAAC;MACpC,MAAMkB,IAAI,GAAGlC,IAAI,CAACiC,GAAG,CAACJ,EAAE,CAAChB,GAAG,EAAEiB,EAAE,CAACjB,GAAG,CAAC;MACrC,MAAMsB,MAAM,GAAGnC,IAAI,CAACoC,GAAG,CAACP,EAAE,CAACb,GAAG,EAAEc,EAAE,CAACd,GAAG,CAAC;MACvC,MAAMqB,KAAK,GAAGrC,IAAI,CAACoC,GAAG,CAACP,EAAE,CAAChB,GAAG,EAAEiB,EAAE,CAACjB,GAAG,CAAC;MAEtCgB,EAAE,GAAG,IAAI,CAACxB,GAAG,CAAC6B,IAAI,CAAC,GAAGF,GAAG;MACzBF,EAAE,GAAG,IAAI,CAACzB,GAAG,CAACgC,KAAK,CAAC,GAAGF,MAAM;MAE7B,OAAO;QACLH,GAAG;QACHE,IAAI;QACJC,MAAM;QACNE,KAAK;QACLK,SAAS;QACTb,EAAE,EAAE;UAACP,OAAO,EAAEO,EAAE;UAAEhB,GAAG,EAAEqB,IAAI;UAAElB,GAAG,EAAEgB,GAAG;UAAET,QAAQ,EAAG,IAAG,IAAI,CAAClB,GAAG,CAAC6B,IAAI,CAAE,IAAGF,GAAI,EAAC;UAAEU;QAAS,CAAC;QACxFZ,EAAE,EAAE;UACFR,OAAO,EAAEQ,EAAE;UACXjB,GAAG,EAAEwB,KAAK;UACVrB,GAAG,EAAEmB,MAAM;UACXZ,QAAQ,EAAG,IAAG,IAAI,CAAClB,GAAG,CAACgC,KAAK,CAAE,IAAGF,MAAO,EAAC;UACzCO;QACF,CAAC;QACDJ,UAAU,EAAG,GAAET,EAAG,IAAGC,EAAG;MAC1B,CAAC;IACH;IACA,IAAIa,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MAC7B,OAAOF,SAAS,GAAG;QAACA,SAAS;QAAEG,KAAK,EAAEF;MAAS,CAAC,GAAG;QAACE,KAAK,EAAEF;MAAS,CAAC;IACvE;IAEA,MAAMrB,OAAO,GAAG,IAAI,CAACZ,aAAa,CAACiC,SAAS,CAAC;IAC7C,OAAOD,SAAS,GAAG;MAACA,SAAS;MAAE,GAAGpB;IAAO,CAAC,GAAGA,OAAO;EACtD,CAAC;EAED;EACAwB,aAAaA,CAAC9B,GAAG,EAAEH,GAAG,EAAE;IACtB,OAAO5B,QAAQ,CAACoB,GAAG,CAACQ,GAAG,CAAC,GAAGG,GAAG;EAChC,CAAC;EAED;EACA+B,MAAMA,CAAA,EAAG;IACP,QAAQC,SAAS,CAAC5C,MAAM;MACtB,KAAK,CAAC;QACJ,OAAOnB,QAAQ,CAAC6D,aAAa,CAACE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;MAC3D,KAAK,CAAC;QACJ,OAAQ,GAAE/D,QAAQ,CAAC6D,aAAa,CAACE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAE,IAAG/D,QAAQ,CAAC6D,aAAa,CACpFE,SAAS,CAAC,CAAC,CAAC,EACZA,SAAS,CAAC,CAAC,CACb,CAAE,EAAC;MACL;QACE,MAAM,IAAIjD,KAAK,CAAC,uCAAuC,CAAC;IAC5D;EACF,CAAC;EAED;EACAkD,OAAOA,CAACC,KAAK,EAAE5B,OAAO,EAAE;IACtB,MAAM,CAACY,IAAI,EAAEF,GAAG,GAAIK,KAAK,EAAEF,MAAM,CAAC,GAAGe,KAAK;IAC1C,MAAM,CAACrC,GAAG,EAAEG,GAAG,CAAC,GAAGM,OAAO;IAC1B,OAAOT,GAAG,IAAIqB,IAAI,IAAIrB,GAAG,IAAIwB,KAAK,IAAIrB,GAAG,IAAIgB,GAAG,IAAIhB,GAAG,IAAImB,MAAM;EACnE;AACF,CAAC;AAEDgB,MAAM,CAACC,OAAO,GAAGnE,QAAQ"}