{"version": 3, "file": "stream-buf.js", "names": ["Stream", "require", "utils", "StringBuf", "StringChunk", "constructor", "data", "encoding", "_data", "_encoding", "length", "<PERSON><PERSON><PERSON><PERSON>", "copy", "target", "targetOffset", "offset", "_buffer", "<PERSON><PERSON><PERSON>", "from", "StringBufChunk", "_buf", "BufferChunk", "ReadWriteBuf", "size", "buffer", "alloc", "iRead", "iWrite", "buf", "eod", "full", "read", "undefined", "write", "chunk", "Math", "min", "StreamBuf", "options", "bufSize", "buffers", "batch", "corked", "inPos", "outPos", "pipes", "paused", "inherits", "Duplex", "concat", "map", "rwBuf", "_getW<PERSON><PERSON><PERSON>er", "last", "push", "_pipe", "pipe", "Promise", "resolve", "all", "_writeToBuffers", "inLen", "callback", "Function", "nop", "String", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "shift", "process", "nextTick", "emit", "cork", "_flush", "uncork", "end", "writeComplete", "error", "for<PERSON>ach", "first", "filter", "Boolean", "setEncoding", "pause", "resume", "isPaused", "destination", "unpipe", "unshift", "wrap", "module", "exports"], "sources": ["../../../lib/utils/stream-buf.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst Stream = require('readable-stream');\n\nconst utils = require('./utils');\nconst StringBuf = require('./string-buf');\n\n// =============================================================================\n// data chunks - encapsulating incoming data\nclass StringChunk {\n  constructor(data, encoding) {\n    this._data = data;\n    this._encoding = encoding;\n  }\n\n  get length() {\n    return this.toBuffer().length;\n  }\n\n  // copy to target buffer\n  copy(target, targetOffset, offset, length) {\n    return this.toBuffer().copy(target, targetOffset, offset, length);\n  }\n\n  toBuffer() {\n    if (!this._buffer) {\n      this._buffer = Buffer.from(this._data, this._encoding);\n    }\n    return this._buffer;\n  }\n}\n\nclass StringBufChunk {\n  constructor(data) {\n    this._data = data;\n  }\n\n  get length() {\n    return this._data.length;\n  }\n\n  // copy to target buffer\n  copy(target, targetOffset, offset, length) {\n    // eslint-disable-next-line no-underscore-dangle\n    return this._data._buf.copy(target, targetOffset, offset, length);\n  }\n\n  toBuffer() {\n    return this._data.toBuffer();\n  }\n}\n\nclass BufferChunk {\n  constructor(data) {\n    this._data = data;\n  }\n\n  get length() {\n    return this._data.length;\n  }\n\n  // copy to target buffer\n  copy(target, targetOffset, offset, length) {\n    this._data.copy(target, targetOffset, offset, length);\n  }\n\n  toBuffer() {\n    return this._data;\n  }\n}\n\n// =============================================================================\n// ReadWriteBuf - a single buffer supporting simple read-write\nclass ReadWriteBuf {\n  constructor(size) {\n    this.size = size;\n    // the buffer\n    this.buffer = Buffer.alloc(size);\n    // read index\n    this.iRead = 0;\n    // write index\n    this.iWrite = 0;\n  }\n\n  toBuffer() {\n    if (this.iRead === 0 && this.iWrite === this.size) {\n      return this.buffer;\n    }\n\n    const buf = Buffer.alloc(this.iWrite - this.iRead);\n    this.buffer.copy(buf, 0, this.iRead, this.iWrite);\n    return buf;\n  }\n\n  get length() {\n    return this.iWrite - this.iRead;\n  }\n\n  get eod() {\n    return this.iRead === this.iWrite;\n  }\n\n  get full() {\n    return this.iWrite === this.size;\n  }\n\n  read(size) {\n    let buf;\n    // read size bytes from buffer and return buffer\n    if (size === 0) {\n      // special case - return null if no data requested\n      return null;\n    }\n\n    if (size === undefined || size >= this.length) {\n      // if no size specified or size is at least what we have then return all of the bytes\n      buf = this.toBuffer();\n      this.iRead = this.iWrite;\n      return buf;\n    }\n\n    // otherwise return a chunk\n    buf = Buffer.alloc(size);\n    this.buffer.copy(buf, 0, this.iRead, size);\n    this.iRead += size;\n    return buf;\n  }\n\n  write(chunk, offset, length) {\n    // write as many bytes from data from optional source offset\n    // and return number of bytes written\n    const size = Math.min(length, this.size - this.iWrite);\n    chunk.copy(this.buffer, this.iWrite, offset, offset + size);\n    this.iWrite += size;\n    return size;\n  }\n}\n\n// =============================================================================\n// StreamBuf - a multi-purpose read-write stream\n//  As MemBuf - write as much data as you like. Then call toBuffer() to consolidate\n//  As StreamHub - pipe to multiple writables\n//  As readable stream - feed data into the writable part and have some other code read from it.\n\n// Note: Not sure why but StreamBuf does not like JS \"class\" sugar. It fails the\n// integration tests\nconst StreamBuf = function(options) {\n  options = options || {};\n  this.bufSize = options.bufSize || 1024 * 1024;\n  this.buffers = [];\n\n  // batch mode fills a buffer completely before passing the data on\n  // to pipes or 'readable' event listeners\n  this.batch = options.batch || false;\n\n  this.corked = false;\n  // where in the current writable buffer we're up to\n  this.inPos = 0;\n\n  // where in the current readable buffer we've read up to\n  this.outPos = 0;\n\n  // consuming pipe streams go here\n  this.pipes = [];\n\n  // controls emit('data')\n  this.paused = false;\n\n  this.encoding = null;\n};\n\nutils.inherits(StreamBuf, Stream.Duplex, {\n  toBuffer() {\n    switch (this.buffers.length) {\n      case 0:\n        return null;\n      case 1:\n        return this.buffers[0].toBuffer();\n      default:\n        return Buffer.concat(this.buffers.map(rwBuf => rwBuf.toBuffer()));\n    }\n  },\n\n  // writable\n  // event drain - if write returns false (which it won't), indicates when safe to write again.\n  // finish - end() has been called\n  // pipe(src) - pipe() has been called on readable\n  // unpipe(src) - unpipe() has been called on readable\n  // error - duh\n\n  _getWritableBuffer() {\n    if (this.buffers.length) {\n      const last = this.buffers[this.buffers.length - 1];\n      if (!last.full) {\n        return last;\n      }\n    }\n    const buf = new ReadWriteBuf(this.bufSize);\n    this.buffers.push(buf);\n    return buf;\n  },\n\n  async _pipe(chunk) {\n    const write = function(pipe) {\n      return new Promise(resolve => {\n        pipe.write(chunk.toBuffer(), () => {\n          resolve();\n        });\n      });\n    };\n    await Promise.all(this.pipes.map(write));\n  },\n  _writeToBuffers(chunk) {\n    let inPos = 0;\n    const inLen = chunk.length;\n    while (inPos < inLen) {\n      // find writable buffer\n      const buffer = this._getWritableBuffer();\n\n      // write some data\n      inPos += buffer.write(chunk, inPos, inLen - inPos);\n    }\n  },\n  async write(data, encoding, callback) {\n    if (encoding instanceof Function) {\n      callback = encoding;\n      encoding = 'utf8';\n    }\n    callback = callback || utils.nop;\n\n    // encapsulate data into a chunk\n    let chunk;\n    if (data instanceof StringBuf) {\n      chunk = new StringBufChunk(data);\n    } else if (data instanceof Buffer) {\n      chunk = new BufferChunk(data);\n    } else if (typeof data === 'string' || data instanceof String || data instanceof ArrayBuffer) {\n      chunk = new StringChunk(data, encoding);\n    } else {\n      throw new Error('Chunk must be one of type String, Buffer or StringBuf.');\n    }\n\n    // now, do something with the chunk\n    if (this.pipes.length) {\n      if (this.batch) {\n        this._writeToBuffers(chunk);\n        while (!this.corked && this.buffers.length > 1) {\n          this._pipe(this.buffers.shift());\n        }\n      } else if (!this.corked) {\n        await this._pipe(chunk);\n        callback();\n      } else {\n        this._writeToBuffers(chunk);\n        process.nextTick(callback);\n      }\n    } else {\n      if (!this.paused) {\n        this.emit('data', chunk.toBuffer());\n      }\n\n      this._writeToBuffers(chunk);\n      this.emit('readable');\n    }\n\n    return true;\n  },\n  cork() {\n    this.corked = true;\n  },\n  _flush(/* destination */) {\n    // if we have comsumers...\n    if (this.pipes.length) {\n      // and there's stuff not written\n      while (this.buffers.length) {\n        this._pipe(this.buffers.shift());\n      }\n    }\n  },\n  uncork() {\n    this.corked = false;\n    this._flush();\n  },\n  end(chunk, encoding, callback) {\n    const writeComplete = error => {\n      if (error) {\n        callback(error);\n      } else {\n        this._flush();\n        this.pipes.forEach(pipe => {\n          pipe.end();\n        });\n        this.emit('finish');\n      }\n    };\n    if (chunk) {\n      this.write(chunk, encoding, writeComplete);\n    } else {\n      writeComplete();\n    }\n  },\n\n  // readable\n  // event readable - some data is now available\n  // event data - switch to flowing mode - feeds chunks to handler\n  // event end - no more data\n  // event close - optional, indicates upstream close\n  // event error - duh\n  read(size) {\n    let buffers;\n    // read min(buffer, size || infinity)\n    if (size) {\n      buffers = [];\n      while (size && this.buffers.length && !this.buffers[0].eod) {\n        const first = this.buffers[0];\n        const buffer = first.read(size);\n        size -= buffer.length;\n        buffers.push(buffer);\n        if (first.eod && first.full) {\n          this.buffers.shift();\n        }\n      }\n      return Buffer.concat(buffers);\n    }\n\n    buffers = this.buffers.map(buf => buf.toBuffer()).filter(Boolean);\n    this.buffers = [];\n    return Buffer.concat(buffers);\n  },\n  setEncoding(encoding) {\n    // causes stream.read or stream.on('data) to return strings of encoding instead of Buffer objects\n    this.encoding = encoding;\n  },\n  pause() {\n    this.paused = true;\n  },\n  resume() {\n    this.paused = false;\n  },\n  isPaused() {\n    return !!this.paused;\n  },\n  pipe(destination) {\n    // add destination to pipe list & write current buffer\n    this.pipes.push(destination);\n    if (!this.paused && this.buffers.length) {\n      this.end();\n    }\n  },\n  unpipe(destination) {\n    // remove destination from pipe list\n    this.pipes = this.pipes.filter(pipe => pipe !== destination);\n  },\n  unshift(/* chunk */) {\n    // some numpty has read some data that's not for them and they want to put it back!\n    // Might implement this some day\n    throw new Error('Not Implemented');\n  },\n  wrap(/* stream */) {\n    // not implemented\n    throw new Error('Not Implemented');\n  },\n});\n\nmodule.exports = StreamBuf;\n"], "mappings": ";;AAAA;AACA,MAAMA,MAAM,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAEzC,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;;AAEzC;AACA;AACA,MAAMG,WAAW,CAAC;EAChBC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACC,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,SAAS,GAAGF,QAAQ;EAC3B;EAEA,IAAIG,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACD,MAAM;EAC/B;;EAEA;EACAE,IAAIA,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,EAAE;IACzC,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,CAAC;EACnE;EAEAC,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACK,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACV,KAAK,EAAE,IAAI,CAACC,SAAS,CAAC;IACxD;IACA,OAAO,IAAI,CAACO,OAAO;EACrB;AACF;AAEA,MAAMG,cAAc,CAAC;EACnBd,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACE,KAAK,GAAGF,IAAI;EACnB;EAEA,IAAII,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,KAAK,CAACE,MAAM;EAC1B;;EAEA;EACAE,IAAIA,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,EAAE;IACzC;IACA,OAAO,IAAI,CAACF,KAAK,CAACY,IAAI,CAACR,IAAI,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,CAAC;EACnE;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC9B;AACF;AAEA,MAAMU,WAAW,CAAC;EAChBhB,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACE,KAAK,GAAGF,IAAI;EACnB;EAEA,IAAII,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,KAAK,CAACE,MAAM;EAC1B;;EAEA;EACAE,IAAIA,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,EAAE;IACzC,IAAI,CAACF,KAAK,CAACI,IAAI,CAACC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEL,MAAM,CAAC;EACvD;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,KAAK;EACnB;AACF;;AAEA;AACA;AACA,MAAMc,YAAY,CAAC;EACjBjB,WAAWA,CAACkB,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACC,MAAM,GAAGP,MAAM,CAACQ,KAAK,CAACF,IAAI,CAAC;IAChC;IACA,IAAI,CAACG,KAAK,GAAG,CAAC;IACd;IACA,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB;EAEAhB,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACe,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,CAACJ,IAAI,EAAE;MACjD,OAAO,IAAI,CAACC,MAAM;IACpB;IAEA,MAAMI,GAAG,GAAGX,MAAM,CAACQ,KAAK,CAAC,IAAI,CAACE,MAAM,GAAG,IAAI,CAACD,KAAK,CAAC;IAClD,IAAI,CAACF,MAAM,CAACZ,IAAI,CAACgB,GAAG,EAAE,CAAC,EAAE,IAAI,CAACF,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;IACjD,OAAOC,GAAG;EACZ;EAEA,IAAIlB,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACiB,MAAM,GAAG,IAAI,CAACD,KAAK;EACjC;EAEA,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,KAAK,KAAK,IAAI,CAACC,MAAM;EACnC;EAEA,IAAIG,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,MAAM,KAAK,IAAI,CAACJ,IAAI;EAClC;EAEAQ,IAAIA,CAACR,IAAI,EAAE;IACT,IAAIK,GAAG;IACP;IACA,IAAIL,IAAI,KAAK,CAAC,EAAE;MACd;MACA,OAAO,IAAI;IACb;IAEA,IAAIA,IAAI,KAAKS,SAAS,IAAIT,IAAI,IAAI,IAAI,CAACb,MAAM,EAAE;MAC7C;MACAkB,GAAG,GAAG,IAAI,CAACjB,QAAQ,CAAC,CAAC;MACrB,IAAI,CAACe,KAAK,GAAG,IAAI,CAACC,MAAM;MACxB,OAAOC,GAAG;IACZ;;IAEA;IACAA,GAAG,GAAGX,MAAM,CAACQ,KAAK,CAACF,IAAI,CAAC;IACxB,IAAI,CAACC,MAAM,CAACZ,IAAI,CAACgB,GAAG,EAAE,CAAC,EAAE,IAAI,CAACF,KAAK,EAAEH,IAAI,CAAC;IAC1C,IAAI,CAACG,KAAK,IAAIH,IAAI;IAClB,OAAOK,GAAG;EACZ;EAEAK,KAAKA,CAACC,KAAK,EAAEnB,MAAM,EAAEL,MAAM,EAAE;IAC3B;IACA;IACA,MAAMa,IAAI,GAAGY,IAAI,CAACC,GAAG,CAAC1B,MAAM,EAAE,IAAI,CAACa,IAAI,GAAG,IAAI,CAACI,MAAM,CAAC;IACtDO,KAAK,CAACtB,IAAI,CAAC,IAAI,CAACY,MAAM,EAAE,IAAI,CAACG,MAAM,EAAEZ,MAAM,EAAEA,MAAM,GAAGQ,IAAI,CAAC;IAC3D,IAAI,CAACI,MAAM,IAAIJ,IAAI;IACnB,OAAOA,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAMc,SAAS,GAAG,SAAAA,CAASC,OAAO,EAAE;EAClCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAI,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,IAAI,GAAG,IAAI;EAC7C,IAAI,CAACC,OAAO,GAAG,EAAE;;EAEjB;EACA;EACA,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK,IAAI,KAAK;EAEnC,IAAI,CAACC,MAAM,GAAG,KAAK;EACnB;EACA,IAAI,CAACC,KAAK,GAAG,CAAC;;EAEd;EACA,IAAI,CAACC,MAAM,GAAG,CAAC;;EAEf;EACA,IAAI,CAACC,KAAK,GAAG,EAAE;;EAEf;EACA,IAAI,CAACC,MAAM,GAAG,KAAK;EAEnB,IAAI,CAACvC,QAAQ,GAAG,IAAI;AACtB,CAAC;AAEDL,KAAK,CAAC6C,QAAQ,CAACV,SAAS,EAAErC,MAAM,CAACgD,MAAM,EAAE;EACvCrC,QAAQA,CAAA,EAAG;IACT,QAAQ,IAAI,CAAC6B,OAAO,CAAC9B,MAAM;MACzB,KAAK,CAAC;QACJ,OAAO,IAAI;MACb,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAAC7B,QAAQ,CAAC,CAAC;MACnC;QACE,OAAOM,MAAM,CAACgC,MAAM,CAAC,IAAI,CAACT,OAAO,CAACU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;;EAEAyC,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACZ,OAAO,CAAC9B,MAAM,EAAE;MACvB,MAAM2C,IAAI,GAAG,IAAI,CAACb,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC9B,MAAM,GAAG,CAAC,CAAC;MAClD,IAAI,CAAC2C,IAAI,CAACvB,IAAI,EAAE;QACd,OAAOuB,IAAI;MACb;IACF;IACA,MAAMzB,GAAG,GAAG,IAAIN,YAAY,CAAC,IAAI,CAACiB,OAAO,CAAC;IAC1C,IAAI,CAACC,OAAO,CAACc,IAAI,CAAC1B,GAAG,CAAC;IACtB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAM2B,KAAKA,CAACrB,KAAK,EAAE;IACjB,MAAMD,KAAK,GAAG,SAAAA,CAASuB,IAAI,EAAE;MAC3B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;QAC5BF,IAAI,CAACvB,KAAK,CAACC,KAAK,CAACvB,QAAQ,CAAC,CAAC,EAAE,MAAM;UACjC+C,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,MAAMD,OAAO,CAACE,GAAG,CAAC,IAAI,CAACd,KAAK,CAACK,GAAG,CAACjB,KAAK,CAAC,CAAC;EAC1C,CAAC;EACD2B,eAAeA,CAAC1B,KAAK,EAAE;IACrB,IAAIS,KAAK,GAAG,CAAC;IACb,MAAMkB,KAAK,GAAG3B,KAAK,CAACxB,MAAM;IAC1B,OAAOiC,KAAK,GAAGkB,KAAK,EAAE;MACpB;MACA,MAAMrC,MAAM,GAAG,IAAI,CAAC4B,kBAAkB,CAAC,CAAC;;MAExC;MACAT,KAAK,IAAInB,MAAM,CAACS,KAAK,CAACC,KAAK,EAAES,KAAK,EAAEkB,KAAK,GAAGlB,KAAK,CAAC;IACpD;EACF,CAAC;EACD,MAAMV,KAAKA,CAAC3B,IAAI,EAAEC,QAAQ,EAAEuD,QAAQ,EAAE;IACpC,IAAIvD,QAAQ,YAAYwD,QAAQ,EAAE;MAChCD,QAAQ,GAAGvD,QAAQ;MACnBA,QAAQ,GAAG,MAAM;IACnB;IACAuD,QAAQ,GAAGA,QAAQ,IAAI5D,KAAK,CAAC8D,GAAG;;IAEhC;IACA,IAAI9B,KAAK;IACT,IAAI5B,IAAI,YAAYH,SAAS,EAAE;MAC7B+B,KAAK,GAAG,IAAIf,cAAc,CAACb,IAAI,CAAC;IAClC,CAAC,MAAM,IAAIA,IAAI,YAAYW,MAAM,EAAE;MACjCiB,KAAK,GAAG,IAAIb,WAAW,CAACf,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,YAAY2D,MAAM,IAAI3D,IAAI,YAAY4D,WAAW,EAAE;MAC5FhC,KAAK,GAAG,IAAI9B,WAAW,CAACE,IAAI,EAAEC,QAAQ,CAAC;IACzC,CAAC,MAAM;MACL,MAAM,IAAI4D,KAAK,CAAC,wDAAwD,CAAC;IAC3E;;IAEA;IACA,IAAI,IAAI,CAACtB,KAAK,CAACnC,MAAM,EAAE;MACrB,IAAI,IAAI,CAAC+B,KAAK,EAAE;QACd,IAAI,CAACmB,eAAe,CAAC1B,KAAK,CAAC;QAC3B,OAAO,CAAC,IAAI,CAACQ,MAAM,IAAI,IAAI,CAACF,OAAO,CAAC9B,MAAM,GAAG,CAAC,EAAE;UAC9C,IAAI,CAAC6C,KAAK,CAAC,IAAI,CAACf,OAAO,CAAC4B,KAAK,CAAC,CAAC,CAAC;QAClC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC1B,MAAM,EAAE;QACvB,MAAM,IAAI,CAACa,KAAK,CAACrB,KAAK,CAAC;QACvB4B,QAAQ,CAAC,CAAC;MACZ,CAAC,MAAM;QACL,IAAI,CAACF,eAAe,CAAC1B,KAAK,CAAC;QAC3BmC,OAAO,CAACC,QAAQ,CAACR,QAAQ,CAAC;MAC5B;IACF,CAAC,MAAM;MACL,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;QAChB,IAAI,CAACyB,IAAI,CAAC,MAAM,EAAErC,KAAK,CAACvB,QAAQ,CAAC,CAAC,CAAC;MACrC;MAEA,IAAI,CAACiD,eAAe,CAAC1B,KAAK,CAAC;MAC3B,IAAI,CAACqC,IAAI,CAAC,UAAU,CAAC;IACvB;IAEA,OAAO,IAAI;EACb,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC9B,MAAM,GAAG,IAAI;EACpB,CAAC;EACD+B,MAAMA,CAAA,CAAC;EAAA,EAAmB;IACxB;IACA,IAAI,IAAI,CAAC5B,KAAK,CAACnC,MAAM,EAAE;MACrB;MACA,OAAO,IAAI,CAAC8B,OAAO,CAAC9B,MAAM,EAAE;QAC1B,IAAI,CAAC6C,KAAK,CAAC,IAAI,CAACf,OAAO,CAAC4B,KAAK,CAAC,CAAC,CAAC;MAClC;IACF;EACF,CAAC;EACDM,MAAMA,CAAA,EAAG;IACP,IAAI,CAAChC,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC+B,MAAM,CAAC,CAAC;EACf,CAAC;EACDE,GAAGA,CAACzC,KAAK,EAAE3B,QAAQ,EAAEuD,QAAQ,EAAE;IAC7B,MAAMc,aAAa,GAAGC,KAAK,IAAI;MAC7B,IAAIA,KAAK,EAAE;QACTf,QAAQ,CAACe,KAAK,CAAC;MACjB,CAAC,MAAM;QACL,IAAI,CAACJ,MAAM,CAAC,CAAC;QACb,IAAI,CAAC5B,KAAK,CAACiC,OAAO,CAACtB,IAAI,IAAI;UACzBA,IAAI,CAACmB,GAAG,CAAC,CAAC;QACZ,CAAC,CAAC;QACF,IAAI,CAACJ,IAAI,CAAC,QAAQ,CAAC;MACrB;IACF,CAAC;IACD,IAAIrC,KAAK,EAAE;MACT,IAAI,CAACD,KAAK,CAACC,KAAK,EAAE3B,QAAQ,EAAEqE,aAAa,CAAC;IAC5C,CAAC,MAAM;MACLA,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA7C,IAAIA,CAACR,IAAI,EAAE;IACT,IAAIiB,OAAO;IACX;IACA,IAAIjB,IAAI,EAAE;MACRiB,OAAO,GAAG,EAAE;MACZ,OAAOjB,IAAI,IAAI,IAAI,CAACiB,OAAO,CAAC9B,MAAM,IAAI,CAAC,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACX,GAAG,EAAE;QAC1D,MAAMkD,KAAK,GAAG,IAAI,CAACvC,OAAO,CAAC,CAAC,CAAC;QAC7B,MAAMhB,MAAM,GAAGuD,KAAK,CAAChD,IAAI,CAACR,IAAI,CAAC;QAC/BA,IAAI,IAAIC,MAAM,CAACd,MAAM;QACrB8B,OAAO,CAACc,IAAI,CAAC9B,MAAM,CAAC;QACpB,IAAIuD,KAAK,CAAClD,GAAG,IAAIkD,KAAK,CAACjD,IAAI,EAAE;UAC3B,IAAI,CAACU,OAAO,CAAC4B,KAAK,CAAC,CAAC;QACtB;MACF;MACA,OAAOnD,MAAM,CAACgC,MAAM,CAACT,OAAO,CAAC;IAC/B;IAEAA,OAAO,GAAG,IAAI,CAACA,OAAO,CAACU,GAAG,CAACtB,GAAG,IAAIA,GAAG,CAACjB,QAAQ,CAAC,CAAC,CAAC,CAACqE,MAAM,CAACC,OAAO,CAAC;IACjE,IAAI,CAACzC,OAAO,GAAG,EAAE;IACjB,OAAOvB,MAAM,CAACgC,MAAM,CAACT,OAAO,CAAC;EAC/B,CAAC;EACD0C,WAAWA,CAAC3E,QAAQ,EAAE;IACpB;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B,CAAC;EACD4E,KAAKA,CAAA,EAAG;IACN,IAAI,CAACrC,MAAM,GAAG,IAAI;EACpB,CAAC;EACDsC,MAAMA,CAAA,EAAG;IACP,IAAI,CAACtC,MAAM,GAAG,KAAK;EACrB,CAAC;EACDuC,QAAQA,CAAA,EAAG;IACT,OAAO,CAAC,CAAC,IAAI,CAACvC,MAAM;EACtB,CAAC;EACDU,IAAIA,CAAC8B,WAAW,EAAE;IAChB;IACA,IAAI,CAACzC,KAAK,CAACS,IAAI,CAACgC,WAAW,CAAC;IAC5B,IAAI,CAAC,IAAI,CAACxC,MAAM,IAAI,IAAI,CAACN,OAAO,CAAC9B,MAAM,EAAE;MACvC,IAAI,CAACiE,GAAG,CAAC,CAAC;IACZ;EACF,CAAC;EACDY,MAAMA,CAACD,WAAW,EAAE;IAClB;IACA,IAAI,CAACzC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACmC,MAAM,CAACxB,IAAI,IAAIA,IAAI,KAAK8B,WAAW,CAAC;EAC9D,CAAC;EACDE,OAAOA,CAAA,CAAC;EAAA,EAAa;IACnB;IACA;IACA,MAAM,IAAIrB,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EACDsB,IAAIA,CAAA,CAAC;EAAA,EAAc;IACjB;IACA,MAAM,IAAItB,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC,CAAC;AAEFuB,MAAM,CAACC,OAAO,GAAGtD,SAAS"}