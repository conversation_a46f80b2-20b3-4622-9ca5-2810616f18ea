{"version": 3, "file": "worksheet-writer.js", "names": ["_", "require", "RelType", "co<PERSON><PERSON><PERSON>", "Encryptor", "Dimensions", "StringBuf", "Row", "Column", "SheetRelsWriter", "SheetCommentsWriter", "DataValidations", "xmlBuffer", "ListXform", "DataValidationsXform", "SheetPropertiesXform", "SheetFormatPropertiesXform", "ColXform", "RowXform", "HyperlinkXform", "SheetViewXform", "SheetProtectionXform", "PageMarginsXform", "PageSetupXform", "AutoFilterXform", "PictureXform", "ConditionalFormattingsXform", "HeaderFooterXform", "RowBreaksXform", "xform", "dataValidations", "sheetProperties", "sheetFormatProperties", "columns", "tag", "length", "childXform", "row", "hyperlinks", "sheetViews", "sheetProtection", "<PERSON><PERSON><PERSON><PERSON>", "pageSeteup", "autoFilter", "picture", "conditionalFormattings", "headerFooter", "rowBreaks", "WorksheetWriter", "constructor", "options", "id", "name", "state", "_rows", "_columns", "_keys", "_merges", "add", "_sheetRelsWriter", "_sheetCommentsWriter", "_dimensions", "_rowZero", "committed", "_formulae", "_siFormulae", "conditionalFormatting", "properties", "Object", "assign", "defaultRowHeight", "dyDescent", "outlineLevelCol", "outlineLevelRow", "differentFirst", "differentOddEven", "<PERSON><PERSON><PERSON><PERSON>", "oddFooter", "<PERSON><PERSON><PERSON><PERSON>", "evenFooter", "firstHeader", "firstFooter", "pageSetup", "margins", "left", "right", "top", "bottom", "header", "footer", "orientation", "horizontalDpi", "verticalDpi", "fitToPage", "fitToWidth", "fitToHeight", "scale", "pageOrder", "blackAndWhite", "draft", "cellComments", "errors", "paperSize", "undefined", "showRowColHeaders", "showGridLines", "horizontalCentered", "verticalCentered", "colBreaks", "useSharedStrings", "_workbook", "workbook", "hasComments", "_views", "views", "_media", "_writeOpenWorksheet", "startedData", "stream", "_stream", "_openStream", "pause", "destroy", "Error", "commit", "for<PERSON>ach", "cRow", "_writeRow", "_writeOpenSheetData", "_writeCloseSheetData", "_writeAutoFilter", "_writeMergeCells", "_writeHyperlinks", "_writeConditionalFormatting", "_writeDataValidations", "_writeSheetProtection", "_write<PERSON><PERSON><PERSON><PERSON><PERSON>", "_writePageSetup", "_writeBackground", "_writeHeader<PERSON>ooter", "_writeRowBreaks", "_writeLegacyData", "_writeCloseWorksheet", "end", "dimensions", "value", "_headerRowCount", "reduce", "pv", "cv", "headerCount", "headers", "Math", "max", "count", "defn", "column", "push", "getColumnKey", "key", "setColumnKey", "deleteColumnKey", "eachColumnKey", "f", "each", "getColumn", "c", "col", "l2n", "n", "_nextRow", "eachRow", "iteratee", "includeEmpty", "i", "getRow", "<PERSON><PERSON><PERSON><PERSON>", "number", "_commitRow", "found", "shift", "lastRow", "findRow", "rowNumber", "index", "addRow", "values", "find<PERSON>ell", "r", "address", "get<PERSON><PERSON><PERSON>", "getCell", "getCellEx", "mergeCells", "_len", "arguments", "cells", "Array", "_key", "merge", "intersects", "master", "j", "addConditionalFormatting", "cf", "removeConditionalFormatting", "filter", "splice", "Function", "addBackgroundImage", "imageId", "_background", "getBackgroundImageId", "protect", "password", "Promise", "resolve", "sheet", "spinCount", "Number", "isFinite", "round", "algorithmName", "saltValue", "randomBytes", "toString", "hashValue", "convertPasswordToHash", "unprotect", "_write", "text", "reset", "addText", "write", "_writeSheetProperties", "xmlBuf", "sheetPropertiesModel", "outlineProperties", "tabColor", "toXml", "_writeSheetFormatProperties", "sheetFormatPropertiesModel", "defaultColWidth", "_writeColumns", "cols", "toModel", "prepare", "styles", "height", "model", "sharedStrings", "hyperlinksProxy", "merges", "formulae", "siFormulae", "comments", "addComments", "_hyperlinks", "image", "getImage", "pictureId", "addMedia", "Target", "Type", "Image", "rId", "vmlRelId", "_writeDimensions", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/worksheet-writer.js"], "sourcesContent": ["const _ = require('../../utils/under-dash');\n\nconst RelType = require('../../xlsx/rel-type');\n\nconst colCache = require('../../utils/col-cache');\nconst Encryptor = require('../../utils/encryptor');\nconst Dimensions = require('../../doc/range');\nconst StringBuf = require('../../utils/string-buf');\n\nconst Row = require('../../doc/row');\nconst Column = require('../../doc/column');\n\nconst SheetRelsWriter = require('./sheet-rels-writer');\nconst SheetCommentsWriter = require('./sheet-comments-writer');\nconst DataValidations = require('../../doc/data-validations');\n\nconst xmlBuffer = new StringBuf();\n\n// ============================================================================================\n// Xforms\nconst ListXform = require('../../xlsx/xform/list-xform');\nconst DataValidationsXform = require('../../xlsx/xform/sheet/data-validations-xform');\nconst SheetPropertiesXform = require('../../xlsx/xform/sheet/sheet-properties-xform');\nconst SheetFormatPropertiesXform = require('../../xlsx/xform/sheet/sheet-format-properties-xform');\nconst ColXform = require('../../xlsx/xform/sheet/col-xform');\nconst RowXform = require('../../xlsx/xform/sheet/row-xform');\nconst HyperlinkXform = require('../../xlsx/xform/sheet/hyperlink-xform');\nconst SheetViewXform = require('../../xlsx/xform/sheet/sheet-view-xform');\nconst SheetProtectionXform = require('../../xlsx/xform/sheet/sheet-protection-xform');\nconst PageMarginsXform = require('../../xlsx/xform/sheet/page-margins-xform');\nconst PageSetupXform = require('../../xlsx/xform/sheet/page-setup-xform');\nconst AutoFilterXform = require('../../xlsx/xform/sheet/auto-filter-xform');\nconst PictureXform = require('../../xlsx/xform/sheet/picture-xform');\nconst ConditionalFormattingsXform = require('../../xlsx/xform/sheet/cf/conditional-formattings-xform');\nconst HeaderFooterXform = require('../../xlsx/xform/sheet/header-footer-xform');\nconst RowBreaksXform = require('../../xlsx/xform/sheet/row-breaks-xform');\n\n// since prepare and render are functional, we can use singletons\nconst xform = {\n  dataValidations: new DataValidationsXform(),\n  sheetProperties: new SheetPropertiesXform(),\n  sheetFormatProperties: new SheetFormatPropertiesXform(),\n  columns: new ListXform({tag: 'cols', length: false, childXform: new ColXform()}),\n  row: new RowXform(),\n  hyperlinks: new ListXform({tag: 'hyperlinks', length: false, childXform: new HyperlinkXform()}),\n  sheetViews: new ListXform({tag: 'sheetViews', length: false, childXform: new SheetViewXform()}),\n  sheetProtection: new SheetProtectionXform(),\n  pageMargins: new PageMarginsXform(),\n  pageSeteup: new PageSetupXform(),\n  autoFilter: new AutoFilterXform(),\n  picture: new PictureXform(),\n  conditionalFormattings: new ConditionalFormattingsXform(),\n  headerFooter: new HeaderFooterXform(),\n  rowBreaks: new RowBreaksXform(),\n};\n\n// ============================================================================================\n\nclass WorksheetWriter {\n  constructor(options) {\n    // in a workbook, each sheet will have a number\n    this.id = options.id;\n\n    // and a name\n    this.name = options.name || `Sheet${this.id}`;\n\n    // add a state\n    this.state = options.state || 'visible';\n\n    // rows are stored here while they need to be worked on.\n    // when they are committed, they will be deleted.\n    this._rows = [];\n\n    // column definitions\n    this._columns = null;\n\n    // column keys (addRow convenience): key ==> this._columns index\n    this._keys = {};\n\n    // keep a record of all row and column pageBreaks\n    this._merges = [];\n    this._merges.add = function() {}; // ignore cell instruction\n\n    // keep record of all hyperlinks\n    this._sheetRelsWriter = new SheetRelsWriter(options);\n\n    this._sheetCommentsWriter = new SheetCommentsWriter(this, this._sheetRelsWriter, options);\n\n    // keep a record of dimensions\n    this._dimensions = new Dimensions();\n\n    // first uncommitted row\n    this._rowZero = 1;\n\n    // committed flag\n    this.committed = false;\n\n    // for data validations\n    this.dataValidations = new DataValidations();\n\n    // for sharing formulae\n    this._formulae = {};\n    this._siFormulae = 0;\n\n    // keep a record of conditionalFormattings\n    this.conditionalFormatting = [];\n\n    // keep a record of all row and column pageBreaks\n    this.rowBreaks = [];\n\n    // for default row height, outline levels, etc\n    this.properties = Object.assign(\n      {},\n      {\n        defaultRowHeight: 15,\n        dyDescent: 55,\n        outlineLevelCol: 0,\n        outlineLevelRow: 0,\n      },\n      options.properties\n    );\n\n    this.headerFooter = Object.assign(\n      {},\n      {\n        differentFirst: false,\n        differentOddEven: false,\n        oddHeader: null,\n        oddFooter: null,\n        evenHeader: null,\n        evenFooter: null,\n        firstHeader: null,\n        firstFooter: null,\n      },\n      options.headerFooter\n    );\n\n    // for all things printing\n    this.pageSetup = Object.assign(\n      {},\n      {\n        margins: {left: 0.7, right: 0.7, top: 0.75, bottom: 0.75, header: 0.3, footer: 0.3},\n        orientation: 'portrait',\n        horizontalDpi: 4294967295,\n        verticalDpi: 4294967295,\n        fitToPage: !!(\n          options.pageSetup &&\n          (options.pageSetup.fitToWidth || options.pageSetup.fitToHeight) &&\n          !options.pageSetup.scale\n        ),\n        pageOrder: 'downThenOver',\n        blackAndWhite: false,\n        draft: false,\n        cellComments: 'None',\n        errors: 'displayed',\n        scale: 100,\n        fitToWidth: 1,\n        fitToHeight: 1,\n        paperSize: undefined,\n        showRowColHeaders: false,\n        showGridLines: false,\n        horizontalCentered: false,\n        verticalCentered: false,\n        rowBreaks: null,\n        colBreaks: null,\n      },\n      options.pageSetup\n    );\n\n    // using shared strings creates a smaller xlsx file but may use more memory\n    this.useSharedStrings = options.useSharedStrings || false;\n\n    this._workbook = options.workbook;\n\n    this.hasComments = false;\n\n    // views\n    this._views = options.views || [];\n\n    // auto filter\n    this.autoFilter = options.autoFilter || null;\n\n    this._media = [];\n\n    // worksheet protection\n    this.sheetProtection = null;\n\n    // start writing to stream now\n    this._writeOpenWorksheet();\n\n    this.startedData = false;\n  }\n\n  get workbook() {\n    return this._workbook;\n  }\n\n  get stream() {\n    if (!this._stream) {\n      // eslint-disable-next-line no-underscore-dangle\n      this._stream = this._workbook._openStream(`/xl/worksheets/sheet${this.id}.xml`);\n\n      // pause stream to prevent 'data' events\n      this._stream.pause();\n    }\n    return this._stream;\n  }\n\n  // destroy - not a valid operation for a streaming writer\n  // even though some streamers might be able to, it's a bad idea.\n  destroy() {\n    throw new Error('Invalid Operation: destroy');\n  }\n\n  commit() {\n    if (this.committed) {\n      return;\n    }\n    // commit all rows\n    this._rows.forEach(cRow => {\n      if (cRow) {\n        // write the row to the stream\n        this._writeRow(cRow);\n      }\n    });\n\n    // we _cannot_ accept new rows from now on\n    this._rows = null;\n\n    if (!this.startedData) {\n      this._writeOpenSheetData();\n    }\n    this._writeCloseSheetData();\n    this._writeAutoFilter();\n    this._writeMergeCells();\n\n    // for some reason, Excel can't handle dimensions at the bottom of the file\n    // this._writeDimensions();\n\n    this._writeHyperlinks();\n    this._writeConditionalFormatting();\n    this._writeDataValidations();\n    this._writeSheetProtection();\n    this._writePageMargins();\n    this._writePageSetup();\n    this._writeBackground();\n    this._writeHeaderFooter();\n    this._writeRowBreaks();\n\n    // Legacy Data tag for comments\n    this._writeLegacyData();\n\n    this._writeCloseWorksheet();\n    // signal end of stream to workbook\n    this.stream.end();\n\n    this._sheetCommentsWriter.commit();\n    // also commit the hyperlinks if any\n    this._sheetRelsWriter.commit();\n\n    this.committed = true;\n  }\n\n  // return the current dimensions of the writer\n  get dimensions() {\n    return this._dimensions;\n  }\n\n  get views() {\n    return this._views;\n  }\n\n  // =========================================================================\n  // Columns\n\n  // get the current columns array.\n  get columns() {\n    return this._columns;\n  }\n\n  // set the columns from an array of column definitions.\n  // Note: any headers defined will overwrite existing values.\n  set columns(value) {\n    // calculate max header row count\n    this._headerRowCount = value.reduce((pv, cv) => {\n      const headerCount = (cv.header && 1) || (cv.headers && cv.headers.length) || 0;\n      return Math.max(pv, headerCount);\n    }, 0);\n\n    // construct Column objects\n    let count = 1;\n    const columns = (this._columns = []);\n    value.forEach(defn => {\n      const column = new Column(this, count++, false);\n      columns.push(column);\n      column.defn = defn;\n    });\n  }\n\n  getColumnKey(key) {\n    return this._keys[key];\n  }\n\n  setColumnKey(key, value) {\n    this._keys[key] = value;\n  }\n\n  deleteColumnKey(key) {\n    delete this._keys[key];\n  }\n\n  eachColumnKey(f) {\n    _.each(this._keys, f);\n  }\n\n  // get a single column by col number. If it doesn't exist, it and any gaps before it\n  // are created.\n  getColumn(c) {\n    if (typeof c === 'string') {\n      // if it matches a key'd column, return that\n      const col = this._keys[c];\n      if (col) return col;\n\n      // otherwise, assume letter\n      c = colCache.l2n(c);\n    }\n    if (!this._columns) {\n      this._columns = [];\n    }\n    if (c > this._columns.length) {\n      let n = this._columns.length + 1;\n      while (n <= c) {\n        this._columns.push(new Column(this, n++));\n      }\n    }\n    return this._columns[c - 1];\n  }\n\n  // =========================================================================\n  // Rows\n  get _nextRow() {\n    return this._rowZero + this._rows.length;\n  }\n\n  // iterate over every uncommitted row in the worksheet, including maybe empty rows\n  eachRow(options, iteratee) {\n    if (!iteratee) {\n      iteratee = options;\n      options = undefined;\n    }\n    if (options && options.includeEmpty) {\n      const n = this._nextRow;\n      for (let i = this._rowZero; i < n; i++) {\n        iteratee(this.getRow(i), i);\n      }\n    } else {\n      this._rows.forEach(row => {\n        if (row.hasValues) {\n          iteratee(row, row.number);\n        }\n      });\n    }\n  }\n\n  _commitRow(cRow) {\n    // since rows must be written in order, we commit all rows up till and including cRow\n    let found = false;\n    while (this._rows.length && !found) {\n      const row = this._rows.shift();\n      this._rowZero++;\n      if (row) {\n        this._writeRow(row);\n        found = row.number === cRow.number;\n        this._rowZero = row.number + 1;\n      }\n    }\n  }\n\n  get lastRow() {\n    // returns last uncommitted row\n    if (this._rows.length) {\n      return this._rows[this._rows.length - 1];\n    }\n    return undefined;\n  }\n\n  // find a row (if exists) by row number\n  findRow(rowNumber) {\n    const index = rowNumber - this._rowZero;\n    return this._rows[index];\n  }\n\n  getRow(rowNumber) {\n    const index = rowNumber - this._rowZero;\n\n    // may fail if rows have been comitted\n    if (index < 0) {\n      throw new Error('Out of bounds: this row has been committed');\n    }\n    let row = this._rows[index];\n    if (!row) {\n      this._rows[index] = row = new Row(this, rowNumber);\n    }\n    return row;\n  }\n\n  addRow(value) {\n    const row = new Row(this, this._nextRow);\n    this._rows[row.number - this._rowZero] = row;\n    row.values = value;\n    return row;\n  }\n\n  // ================================================================================\n  // Cells\n\n  // returns the cell at [r,c] or address given by r. If not found, return undefined\n  findCell(r, c) {\n    const address = colCache.getAddress(r, c);\n    const row = this.findRow(address.row);\n    return row ? row.findCell(address.column) : undefined;\n  }\n\n  // return the cell at [r,c] or address given by r. If not found, create a new one.\n  getCell(r, c) {\n    const address = colCache.getAddress(r, c);\n    const row = this.getRow(address.row);\n    return row.getCellEx(address);\n  }\n\n  mergeCells(...cells) {\n    // may fail if rows have been comitted\n    const dimensions = new Dimensions(cells);\n\n    // check cells aren't already merged\n    this._merges.forEach(merge => {\n      if (merge.intersects(dimensions)) {\n        throw new Error('Cannot merge already merged cells');\n      }\n    });\n\n    // apply merge\n    const master = this.getCell(dimensions.top, dimensions.left);\n    for (let i = dimensions.top; i <= dimensions.bottom; i++) {\n      for (let j = dimensions.left; j <= dimensions.right; j++) {\n        if (i > dimensions.top || j > dimensions.left) {\n          this.getCell(i, j).merge(master);\n        }\n      }\n    }\n\n    // index merge\n    this._merges.push(dimensions);\n  }\n\n  // ===========================================================================\n  // Conditional Formatting\n  addConditionalFormatting(cf) {\n    this.conditionalFormatting.push(cf);\n  }\n\n  removeConditionalFormatting(filter) {\n    if (typeof filter === 'number') {\n      this.conditionalFormatting.splice(filter, 1);\n    } else if (filter instanceof Function) {\n      this.conditionalFormatting = this.conditionalFormatting.filter(filter);\n    } else {\n      this.conditionalFormatting = [];\n    }\n  }\n\n  // =========================================================================\n\n  addBackgroundImage(imageId) {\n    this._background = {\n      imageId,\n    };\n  }\n\n  getBackgroundImageId() {\n    return this._background && this._background.imageId;\n  }\n\n  // =========================================================================\n  // Worksheet Protection\n  protect(password, options) {\n    // TODO: make this function truly async\n    // perhaps marshal to worker thread or something\n    return new Promise(resolve => {\n      this.sheetProtection = {\n        sheet: true,\n      };\n      if (options && 'spinCount' in options) {\n        // force spinCount to be integer >= 0\n        options.spinCount = Number.isFinite(options.spinCount) ? Math.round(Math.max(0, options.spinCount)) : 100000;\n      }\n      if (password) {\n        this.sheetProtection.algorithmName = 'SHA-512';\n        this.sheetProtection.saltValue = Encryptor.randomBytes(16).toString('base64');\n        this.sheetProtection.spinCount = options && 'spinCount' in options ? options.spinCount : 100000; // allow user specified spinCount\n        this.sheetProtection.hashValue = Encryptor.convertPasswordToHash(\n          password,\n          'SHA512',\n          this.sheetProtection.saltValue,\n          this.sheetProtection.spinCount\n        );\n      }\n      if (options) {\n        this.sheetProtection = Object.assign(this.sheetProtection, options);\n        if (!password && 'spinCount' in options) {\n          delete this.sheetProtection.spinCount;\n        }\n      }\n      resolve();\n    });\n  }\n\n  unprotect() {\n    this.sheetProtection = null;\n  }\n\n  // ================================================================================\n\n  _write(text) {\n    xmlBuffer.reset();\n    xmlBuffer.addText(text);\n    this.stream.write(xmlBuffer);\n  }\n\n  _writeSheetProperties(xmlBuf, properties, pageSetup) {\n    const sheetPropertiesModel = {\n      outlineProperties: properties && properties.outlineProperties,\n      tabColor: properties && properties.tabColor,\n      pageSetup:\n        pageSetup && pageSetup.fitToPage\n          ? {\n              fitToPage: pageSetup.fitToPage,\n            }\n          : undefined,\n    };\n\n    xmlBuf.addText(xform.sheetProperties.toXml(sheetPropertiesModel));\n  }\n\n  _writeSheetFormatProperties(xmlBuf, properties) {\n    const sheetFormatPropertiesModel = properties\n      ? {\n          defaultRowHeight: properties.defaultRowHeight,\n          dyDescent: properties.dyDescent,\n          outlineLevelCol: properties.outlineLevelCol,\n          outlineLevelRow: properties.outlineLevelRow,\n        }\n      : undefined;\n    if (properties.defaultColWidth) {\n      sheetFormatPropertiesModel.defaultColWidth = properties.defaultColWidth;\n    }\n\n    xmlBuf.addText(xform.sheetFormatProperties.toXml(sheetFormatPropertiesModel));\n  }\n\n  _writeOpenWorksheet() {\n    xmlBuffer.reset();\n\n    xmlBuffer.addText('<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>');\n    xmlBuffer.addText(\n      '<worksheet xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\"' +\n        ' xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\"' +\n        ' xmlns:mc=\"http://schemas.openxmlformats.org/markup-compatibility/2006\"' +\n        ' mc:Ignorable=\"x14ac\"' +\n        ' xmlns:x14ac=\"http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac\">'\n    );\n\n    this._writeSheetProperties(xmlBuffer, this.properties, this.pageSetup);\n\n    xmlBuffer.addText(xform.sheetViews.toXml(this.views));\n\n    this._writeSheetFormatProperties(xmlBuffer, this.properties);\n\n    this.stream.write(xmlBuffer);\n  }\n\n  _writeColumns() {\n    const cols = Column.toModel(this.columns);\n    if (cols) {\n      xform.columns.prepare(cols, {styles: this._workbook.styles});\n      this.stream.write(xform.columns.toXml(cols));\n    }\n  }\n\n  _writeOpenSheetData() {\n    this._write('<sheetData>');\n  }\n\n  _writeRow(row) {\n    if (!this.startedData) {\n      this._writeColumns();\n      this._writeOpenSheetData();\n      this.startedData = true;\n    }\n\n    if (row.hasValues || row.height) {\n      const {model} = row;\n      const options = {\n        styles: this._workbook.styles,\n        sharedStrings: this.useSharedStrings ? this._workbook.sharedStrings : undefined,\n        hyperlinks: this._sheetRelsWriter.hyperlinksProxy,\n        merges: this._merges,\n        formulae: this._formulae,\n        siFormulae: this._siFormulae,\n        comments: [],\n      };\n      xform.row.prepare(model, options);\n      this.stream.write(xform.row.toXml(model));\n\n      if (options.comments.length) {\n        this.hasComments = true;\n        this._sheetCommentsWriter.addComments(options.comments);\n      }\n    }\n  }\n\n  _writeCloseSheetData() {\n    this._write('</sheetData>');\n  }\n\n  _writeMergeCells() {\n    if (this._merges.length) {\n      xmlBuffer.reset();\n      xmlBuffer.addText(`<mergeCells count=\"${this._merges.length}\">`);\n      this._merges.forEach(merge => {\n        xmlBuffer.addText(`<mergeCell ref=\"${merge}\"/>`);\n      });\n      xmlBuffer.addText('</mergeCells>');\n\n      this.stream.write(xmlBuffer);\n    }\n  }\n\n  _writeHyperlinks() {\n    // eslint-disable-next-line no-underscore-dangle\n    this.stream.write(xform.hyperlinks.toXml(this._sheetRelsWriter._hyperlinks));\n  }\n\n  _writeConditionalFormatting() {\n    const options = {\n      styles: this._workbook.styles,\n    };\n    xform.conditionalFormattings.prepare(this.conditionalFormatting, options);\n    this.stream.write(xform.conditionalFormattings.toXml(this.conditionalFormatting));\n  }\n\n  _writeRowBreaks() {\n    this.stream.write(xform.rowBreaks.toXml(this.rowBreaks));\n  }\n\n  _writeDataValidations() {\n    this.stream.write(xform.dataValidations.toXml(this.dataValidations.model));\n  }\n\n  _writeSheetProtection() {\n    this.stream.write(xform.sheetProtection.toXml(this.sheetProtection));\n  }\n\n  _writePageMargins() {\n    this.stream.write(xform.pageMargins.toXml(this.pageSetup.margins));\n  }\n\n  _writePageSetup() {\n    this.stream.write(xform.pageSeteup.toXml(this.pageSetup));\n  }\n\n  _writeHeaderFooter() {\n    this.stream.write(xform.headerFooter.toXml(this.headerFooter));\n  }\n\n  _writeAutoFilter() {\n    this.stream.write(xform.autoFilter.toXml(this.autoFilter));\n  }\n\n  _writeBackground() {\n    if (this._background) {\n      if (this._background.imageId !== undefined) {\n        const image = this._workbook.getImage(this._background.imageId);\n        const pictureId = this._sheetRelsWriter.addMedia({\n          Target: `../media/${image.name}`,\n          Type: RelType.Image,\n        });\n\n        this._background = {\n          ...this._background,\n          rId: pictureId,\n        };\n      }\n      this.stream.write(xform.picture.toXml({rId: this._background.rId}));\n    }\n  }\n\n  _writeLegacyData() {\n    if (this.hasComments) {\n      xmlBuffer.reset();\n      xmlBuffer.addText(`<legacyDrawing r:id=\"${this._sheetCommentsWriter.vmlRelId}\"/>`);\n      this.stream.write(xmlBuffer);\n    }\n  }\n\n  _writeDimensions() {\n    // for some reason, Excel can't handle dimensions at the bottom of the file\n    // and we don't know the dimensions until the commit, so don't write them.\n    // this._write('<dimension ref=\"' + this._dimensions + '\"/>');\n  }\n\n  _writeCloseWorksheet() {\n    this._write('</worksheet>');\n  }\n}\n\nmodule.exports = WorksheetWriter;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE3C,MAAMC,OAAO,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE9C,MAAME,QAAQ,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAMG,SAAS,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAClD,MAAMI,UAAU,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAC7C,MAAMK,SAAS,GAAGL,OAAO,CAAC,wBAAwB,CAAC;AAEnD,MAAMM,GAAG,GAAGN,OAAO,CAAC,eAAe,CAAC;AACpC,MAAMO,MAAM,GAAGP,OAAO,CAAC,kBAAkB,CAAC;AAE1C,MAAMQ,eAAe,GAAGR,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAMS,mBAAmB,GAAGT,OAAO,CAAC,yBAAyB,CAAC;AAC9D,MAAMU,eAAe,GAAGV,OAAO,CAAC,4BAA4B,CAAC;AAE7D,MAAMW,SAAS,GAAG,IAAIN,SAAS,CAAC,CAAC;;AAEjC;AACA;AACA,MAAMO,SAAS,GAAGZ,OAAO,CAAC,6BAA6B,CAAC;AACxD,MAAMa,oBAAoB,GAAGb,OAAO,CAAC,+CAA+C,CAAC;AACrF,MAAMc,oBAAoB,GAAGd,OAAO,CAAC,+CAA+C,CAAC;AACrF,MAAMe,0BAA0B,GAAGf,OAAO,CAAC,sDAAsD,CAAC;AAClG,MAAMgB,QAAQ,GAAGhB,OAAO,CAAC,kCAAkC,CAAC;AAC5D,MAAMiB,QAAQ,GAAGjB,OAAO,CAAC,kCAAkC,CAAC;AAC5D,MAAMkB,cAAc,GAAGlB,OAAO,CAAC,wCAAwC,CAAC;AACxE,MAAMmB,cAAc,GAAGnB,OAAO,CAAC,yCAAyC,CAAC;AACzE,MAAMoB,oBAAoB,GAAGpB,OAAO,CAAC,+CAA+C,CAAC;AACrF,MAAMqB,gBAAgB,GAAGrB,OAAO,CAAC,2CAA2C,CAAC;AAC7E,MAAMsB,cAAc,GAAGtB,OAAO,CAAC,yCAAyC,CAAC;AACzE,MAAMuB,eAAe,GAAGvB,OAAO,CAAC,0CAA0C,CAAC;AAC3E,MAAMwB,YAAY,GAAGxB,OAAO,CAAC,sCAAsC,CAAC;AACpE,MAAMyB,2BAA2B,GAAGzB,OAAO,CAAC,yDAAyD,CAAC;AACtG,MAAM0B,iBAAiB,GAAG1B,OAAO,CAAC,4CAA4C,CAAC;AAC/E,MAAM2B,cAAc,GAAG3B,OAAO,CAAC,yCAAyC,CAAC;;AAEzE;AACA,MAAM4B,KAAK,GAAG;EACZC,eAAe,EAAE,IAAIhB,oBAAoB,CAAC,CAAC;EAC3CiB,eAAe,EAAE,IAAIhB,oBAAoB,CAAC,CAAC;EAC3CiB,qBAAqB,EAAE,IAAIhB,0BAA0B,CAAC,CAAC;EACvDiB,OAAO,EAAE,IAAIpB,SAAS,CAAC;IAACqB,GAAG,EAAE,MAAM;IAAEC,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE,IAAInB,QAAQ,CAAC;EAAC,CAAC,CAAC;EAChFoB,GAAG,EAAE,IAAInB,QAAQ,CAAC,CAAC;EACnBoB,UAAU,EAAE,IAAIzB,SAAS,CAAC;IAACqB,GAAG,EAAE,YAAY;IAAEC,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE,IAAIjB,cAAc,CAAC;EAAC,CAAC,CAAC;EAC/FoB,UAAU,EAAE,IAAI1B,SAAS,CAAC;IAACqB,GAAG,EAAE,YAAY;IAAEC,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE,IAAIhB,cAAc,CAAC;EAAC,CAAC,CAAC;EAC/FoB,eAAe,EAAE,IAAInB,oBAAoB,CAAC,CAAC;EAC3CoB,WAAW,EAAE,IAAInB,gBAAgB,CAAC,CAAC;EACnCoB,UAAU,EAAE,IAAInB,cAAc,CAAC,CAAC;EAChCoB,UAAU,EAAE,IAAInB,eAAe,CAAC,CAAC;EACjCoB,OAAO,EAAE,IAAInB,YAAY,CAAC,CAAC;EAC3BoB,sBAAsB,EAAE,IAAInB,2BAA2B,CAAC,CAAC;EACzDoB,YAAY,EAAE,IAAInB,iBAAiB,CAAC,CAAC;EACrCoB,SAAS,EAAE,IAAInB,cAAc,CAAC;AAChC,CAAC;;AAED;;AAEA,MAAMoB,eAAe,CAAC;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACnB;IACA,IAAI,CAACC,EAAE,GAAGD,OAAO,CAACC,EAAE;;IAEpB;IACA,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI,IAAK,QAAO,IAAI,CAACD,EAAG,EAAC;;IAE7C;IACA,IAAI,CAACE,KAAK,GAAGH,OAAO,CAACG,KAAK,IAAI,SAAS;;IAEvC;IACA;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;;IAEf;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAEpB;IACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;IAEf;IACA,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACA,OAAO,CAACC,GAAG,GAAG,YAAW,CAAC,CAAC,CAAC,CAAC;;IAElC;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIlD,eAAe,CAACyC,OAAO,CAAC;IAEpD,IAAI,CAACU,oBAAoB,GAAG,IAAIlD,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACiD,gBAAgB,EAAET,OAAO,CAAC;;IAEzF;IACA,IAAI,CAACW,WAAW,GAAG,IAAIxD,UAAU,CAAC,CAAC;;IAEnC;IACA,IAAI,CAACyD,QAAQ,GAAG,CAAC;;IAEjB;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;;IAEtB;IACA,IAAI,CAACjC,eAAe,GAAG,IAAInB,eAAe,CAAC,CAAC;;IAE5C;IACA,IAAI,CAACqD,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;;IAEpB;IACA,IAAI,CAACC,qBAAqB,GAAG,EAAE;;IAE/B;IACA,IAAI,CAACnB,SAAS,GAAG,EAAE;;IAEnB;IACA,IAAI,CAACoB,UAAU,GAAGC,MAAM,CAACC,MAAM,CAC7B,CAAC,CAAC,EACF;MACEC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE;IACnB,CAAC,EACDvB,OAAO,CAACiB,UACV,CAAC;IAED,IAAI,CAACrB,YAAY,GAAGsB,MAAM,CAACC,MAAM,CAC/B,CAAC,CAAC,EACF;MACEK,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,CAAC,EACD/B,OAAO,CAACJ,YACV,CAAC;;IAED;IACA,IAAI,CAACoC,SAAS,GAAGd,MAAM,CAACC,MAAM,CAC5B,CAAC,CAAC,EACF;MACEc,OAAO,EAAE;QAACC,IAAI,EAAE,GAAG;QAAEC,KAAK,EAAE,GAAG;QAAEC,GAAG,EAAE,IAAI;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAG,CAAC;MACnFC,WAAW,EAAE,UAAU;MACvBC,aAAa,EAAE,UAAU;MACzBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,CAAC,EACV3C,OAAO,CAACgC,SAAS,KAChBhC,OAAO,CAACgC,SAAS,CAACY,UAAU,IAAI5C,OAAO,CAACgC,SAAS,CAACa,WAAW,CAAC,IAC/D,CAAC7C,OAAO,CAACgC,SAAS,CAACc,KAAK,CACzB;MACDC,SAAS,EAAE,cAAc;MACzBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,KAAK;MACZC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,WAAW;MACnBL,KAAK,EAAE,GAAG;MACVF,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdO,SAAS,EAAEC,SAAS;MACpBC,iBAAiB,EAAE,KAAK;MACxBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE,KAAK;MACvB5D,SAAS,EAAE,IAAI;MACf6D,SAAS,EAAE;IACb,CAAC,EACD1D,OAAO,CAACgC,SACV,CAAC;;IAED;IACA,IAAI,CAAC2B,gBAAgB,GAAG3D,OAAO,CAAC2D,gBAAgB,IAAI,KAAK;IAEzD,IAAI,CAACC,SAAS,GAAG5D,OAAO,CAAC6D,QAAQ;IAEjC,IAAI,CAACC,WAAW,GAAG,KAAK;;IAExB;IACA,IAAI,CAACC,MAAM,GAAG/D,OAAO,CAACgE,KAAK,IAAI,EAAE;;IAEjC;IACA,IAAI,CAACvE,UAAU,GAAGO,OAAO,CAACP,UAAU,IAAI,IAAI;IAE5C,IAAI,CAACwE,MAAM,GAAG,EAAE;;IAEhB;IACA,IAAI,CAAC3E,eAAe,GAAG,IAAI;;IAE3B;IACA,IAAI,CAAC4E,mBAAmB,CAAC,CAAC;IAE1B,IAAI,CAACC,WAAW,GAAG,KAAK;EAC1B;EAEA,IAAIN,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,SAAS;EACvB;EAEA,IAAIQ,MAAMA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACjB;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACT,SAAS,CAACU,WAAW,CAAE,uBAAsB,IAAI,CAACrE,EAAG,MAAK,CAAC;;MAE/E;MACA,IAAI,CAACoE,OAAO,CAACE,KAAK,CAAC,CAAC;IACtB;IACA,OAAO,IAAI,CAACF,OAAO;EACrB;;EAEA;EACA;EACAG,OAAOA,CAAA,EAAG;IACR,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;EAC/C;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC7D,SAAS,EAAE;MAClB;IACF;IACA;IACA,IAAI,CAACT,KAAK,CAACuE,OAAO,CAACC,IAAI,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR;QACA,IAAI,CAACC,SAAS,CAACD,IAAI,CAAC;MACtB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACxE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC,IAAI,CAAC+D,WAAW,EAAE;MACrB,IAAI,CAACW,mBAAmB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAAC,CAAC;;IAEvB;IACA;;IAEA,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B;IACA,IAAI,CAACxB,MAAM,CAACyB,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACnF,oBAAoB,CAACgE,MAAM,CAAC,CAAC;IAClC;IACA,IAAI,CAACjE,gBAAgB,CAACiE,MAAM,CAAC,CAAC;IAE9B,IAAI,CAAC7D,SAAS,GAAG,IAAI;EACvB;;EAEA;EACA,IAAIiF,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnF,WAAW;EACzB;EAEA,IAAIqD,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,MAAM;EACpB;;EAEA;EACA;;EAEA;EACA,IAAIhF,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACsB,QAAQ;EACtB;;EAEA;EACA;EACA,IAAItB,OAAOA,CAACgH,KAAK,EAAE;IACjB;IACA,IAAI,CAACC,eAAe,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAK;MAC9C,MAAMC,WAAW,GAAID,EAAE,CAAC7D,MAAM,IAAI,CAAC,IAAM6D,EAAE,CAACE,OAAO,IAAIF,EAAE,CAACE,OAAO,CAACpH,MAAO,IAAI,CAAC;MAC9E,OAAOqH,IAAI,CAACC,GAAG,CAACL,EAAE,EAAEE,WAAW,CAAC;IAClC,CAAC,EAAE,CAAC,CAAC;;IAEL;IACA,IAAII,KAAK,GAAG,CAAC;IACb,MAAMzH,OAAO,GAAI,IAAI,CAACsB,QAAQ,GAAG,EAAG;IACpC0F,KAAK,CAACpB,OAAO,CAAC8B,IAAI,IAAI;MACpB,MAAMC,MAAM,GAAG,IAAIpJ,MAAM,CAAC,IAAI,EAAEkJ,KAAK,EAAE,EAAE,KAAK,CAAC;MAC/CzH,OAAO,CAAC4H,IAAI,CAACD,MAAM,CAAC;MACpBA,MAAM,CAACD,IAAI,GAAGA,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACC,GAAG,EAAE;IAChB,OAAO,IAAI,CAACvG,KAAK,CAACuG,GAAG,CAAC;EACxB;EAEAC,YAAYA,CAACD,GAAG,EAAEd,KAAK,EAAE;IACvB,IAAI,CAACzF,KAAK,CAACuG,GAAG,CAAC,GAAGd,KAAK;EACzB;EAEAgB,eAAeA,CAACF,GAAG,EAAE;IACnB,OAAO,IAAI,CAACvG,KAAK,CAACuG,GAAG,CAAC;EACxB;EAEAG,aAAaA,CAACC,CAAC,EAAE;IACfnK,CAAC,CAACoK,IAAI,CAAC,IAAI,CAAC5G,KAAK,EAAE2G,CAAC,CAAC;EACvB;;EAEA;EACA;EACAE,SAASA,CAACC,CAAC,EAAE;IACX,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB;MACA,MAAMC,GAAG,GAAG,IAAI,CAAC/G,KAAK,CAAC8G,CAAC,CAAC;MACzB,IAAIC,GAAG,EAAE,OAAOA,GAAG;;MAEnB;MACAD,CAAC,GAAGnK,QAAQ,CAACqK,GAAG,CAACF,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAAC/G,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,EAAE;IACpB;IACA,IAAI+G,CAAC,GAAG,IAAI,CAAC/G,QAAQ,CAACpB,MAAM,EAAE;MAC5B,IAAIsI,CAAC,GAAG,IAAI,CAAClH,QAAQ,CAACpB,MAAM,GAAG,CAAC;MAChC,OAAOsI,CAAC,IAAIH,CAAC,EAAE;QACb,IAAI,CAAC/G,QAAQ,CAACsG,IAAI,CAAC,IAAIrJ,MAAM,CAAC,IAAI,EAAEiK,CAAC,EAAE,CAAC,CAAC;MAC3C;IACF;IACA,OAAO,IAAI,CAAClH,QAAQ,CAAC+G,CAAC,GAAG,CAAC,CAAC;EAC7B;;EAEA;EACA;EACA,IAAII,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC5G,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACnB,MAAM;EAC1C;;EAEA;EACAwI,OAAOA,CAACzH,OAAO,EAAE0H,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAG1H,OAAO;MAClBA,OAAO,GAAGqD,SAAS;IACrB;IACA,IAAIrD,OAAO,IAAIA,OAAO,CAAC2H,YAAY,EAAE;MACnC,MAAMJ,CAAC,GAAG,IAAI,CAACC,QAAQ;MACvB,KAAK,IAAII,CAAC,GAAG,IAAI,CAAChH,QAAQ,EAAEgH,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;QACtCF,QAAQ,CAAC,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC7B;IACF,CAAC,MAAM;MACL,IAAI,CAACxH,KAAK,CAACuE,OAAO,CAACxF,GAAG,IAAI;QACxB,IAAIA,GAAG,CAAC2I,SAAS,EAAE;UACjBJ,QAAQ,CAACvI,GAAG,EAAEA,GAAG,CAAC4I,MAAM,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;EAEAC,UAAUA,CAACpD,IAAI,EAAE;IACf;IACA,IAAIqD,KAAK,GAAG,KAAK;IACjB,OAAO,IAAI,CAAC7H,KAAK,CAACnB,MAAM,IAAI,CAACgJ,KAAK,EAAE;MAClC,MAAM9I,GAAG,GAAG,IAAI,CAACiB,KAAK,CAAC8H,KAAK,CAAC,CAAC;MAC9B,IAAI,CAACtH,QAAQ,EAAE;MACf,IAAIzB,GAAG,EAAE;QACP,IAAI,CAAC0F,SAAS,CAAC1F,GAAG,CAAC;QACnB8I,KAAK,GAAG9I,GAAG,CAAC4I,MAAM,KAAKnD,IAAI,CAACmD,MAAM;QAClC,IAAI,CAACnH,QAAQ,GAAGzB,GAAG,CAAC4I,MAAM,GAAG,CAAC;MAChC;IACF;EACF;EAEA,IAAII,OAAOA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAAC/H,KAAK,CAACnB,MAAM,EAAE;MACrB,OAAO,IAAI,CAACmB,KAAK,CAAC,IAAI,CAACA,KAAK,CAACnB,MAAM,GAAG,CAAC,CAAC;IAC1C;IACA,OAAOoE,SAAS;EAClB;;EAEA;EACA+E,OAAOA,CAACC,SAAS,EAAE;IACjB,MAAMC,KAAK,GAAGD,SAAS,GAAG,IAAI,CAACzH,QAAQ;IACvC,OAAO,IAAI,CAACR,KAAK,CAACkI,KAAK,CAAC;EAC1B;EAEAT,MAAMA,CAACQ,SAAS,EAAE;IAChB,MAAMC,KAAK,GAAGD,SAAS,GAAG,IAAI,CAACzH,QAAQ;;IAEvC;IACA,IAAI0H,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAI7D,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IACA,IAAItF,GAAG,GAAG,IAAI,CAACiB,KAAK,CAACkI,KAAK,CAAC;IAC3B,IAAI,CAACnJ,GAAG,EAAE;MACR,IAAI,CAACiB,KAAK,CAACkI,KAAK,CAAC,GAAGnJ,GAAG,GAAG,IAAI9B,GAAG,CAAC,IAAI,EAAEgL,SAAS,CAAC;IACpD;IACA,OAAOlJ,GAAG;EACZ;EAEAoJ,MAAMA,CAACxC,KAAK,EAAE;IACZ,MAAM5G,GAAG,GAAG,IAAI9B,GAAG,CAAC,IAAI,EAAE,IAAI,CAACmK,QAAQ,CAAC;IACxC,IAAI,CAACpH,KAAK,CAACjB,GAAG,CAAC4I,MAAM,GAAG,IAAI,CAACnH,QAAQ,CAAC,GAAGzB,GAAG;IAC5CA,GAAG,CAACqJ,MAAM,GAAGzC,KAAK;IAClB,OAAO5G,GAAG;EACZ;;EAEA;EACA;;EAEA;EACAsJ,QAAQA,CAACC,CAAC,EAAEtB,CAAC,EAAE;IACb,MAAMuB,OAAO,GAAG1L,QAAQ,CAAC2L,UAAU,CAACF,CAAC,EAAEtB,CAAC,CAAC;IACzC,MAAMjI,GAAG,GAAG,IAAI,CAACiJ,OAAO,CAACO,OAAO,CAACxJ,GAAG,CAAC;IACrC,OAAOA,GAAG,GAAGA,GAAG,CAACsJ,QAAQ,CAACE,OAAO,CAACjC,MAAM,CAAC,GAAGrD,SAAS;EACvD;;EAEA;EACAwF,OAAOA,CAACH,CAAC,EAAEtB,CAAC,EAAE;IACZ,MAAMuB,OAAO,GAAG1L,QAAQ,CAAC2L,UAAU,CAACF,CAAC,EAAEtB,CAAC,CAAC;IACzC,MAAMjI,GAAG,GAAG,IAAI,CAAC0I,MAAM,CAACc,OAAO,CAACxJ,GAAG,CAAC;IACpC,OAAOA,GAAG,CAAC2J,SAAS,CAACH,OAAO,CAAC;EAC/B;EAEAI,UAAUA,CAAA,EAAW;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAhK,MAAA,EAAPiK,KAAK,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAALF,KAAK,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IACjB;IACA,MAAMtD,UAAU,GAAG,IAAI3I,UAAU,CAAC+L,KAAK,CAAC;;IAExC;IACA,IAAI,CAAC3I,OAAO,CAACoE,OAAO,CAAC0E,KAAK,IAAI;MAC5B,IAAIA,KAAK,CAACC,UAAU,CAACxD,UAAU,CAAC,EAAE;QAChC,MAAM,IAAIrB,KAAK,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC;;IAEF;IACA,MAAM8E,MAAM,GAAG,IAAI,CAACV,OAAO,CAAC/C,UAAU,CAAC1D,GAAG,EAAE0D,UAAU,CAAC5D,IAAI,CAAC;IAC5D,KAAK,IAAI0F,CAAC,GAAG9B,UAAU,CAAC1D,GAAG,EAAEwF,CAAC,IAAI9B,UAAU,CAACzD,MAAM,EAAEuF,CAAC,EAAE,EAAE;MACxD,KAAK,IAAI4B,CAAC,GAAG1D,UAAU,CAAC5D,IAAI,EAAEsH,CAAC,IAAI1D,UAAU,CAAC3D,KAAK,EAAEqH,CAAC,EAAE,EAAE;QACxD,IAAI5B,CAAC,GAAG9B,UAAU,CAAC1D,GAAG,IAAIoH,CAAC,GAAG1D,UAAU,CAAC5D,IAAI,EAAE;UAC7C,IAAI,CAAC2G,OAAO,CAACjB,CAAC,EAAE4B,CAAC,CAAC,CAACH,KAAK,CAACE,MAAM,CAAC;QAClC;MACF;IACF;;IAEA;IACA,IAAI,CAAChJ,OAAO,CAACoG,IAAI,CAACb,UAAU,CAAC;EAC/B;;EAEA;EACA;EACA2D,wBAAwBA,CAACC,EAAE,EAAE;IAC3B,IAAI,CAAC1I,qBAAqB,CAAC2F,IAAI,CAAC+C,EAAE,CAAC;EACrC;EAEAC,2BAA2BA,CAACC,MAAM,EAAE;IAClC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAAC5I,qBAAqB,CAAC6I,MAAM,CAACD,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIA,MAAM,YAAYE,QAAQ,EAAE;MACrC,IAAI,CAAC9I,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAAC4I,MAAM,CAACA,MAAM,CAAC;IACxE,CAAC,MAAM;MACL,IAAI,CAAC5I,qBAAqB,GAAG,EAAE;IACjC;EACF;;EAEA;;EAEA+I,kBAAkBA,CAACC,OAAO,EAAE;IAC1B,IAAI,CAACC,WAAW,GAAG;MACjBD;IACF,CAAC;EACH;EAEAE,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACD,WAAW,IAAI,IAAI,CAACA,WAAW,CAACD,OAAO;EACrD;;EAEA;EACA;EACAG,OAAOA,CAACC,QAAQ,EAAEpK,OAAO,EAAE;IACzB;IACA;IACA,OAAO,IAAIqK,OAAO,CAACC,OAAO,IAAI;MAC5B,IAAI,CAAChL,eAAe,GAAG;QACrBiL,KAAK,EAAE;MACT,CAAC;MACD,IAAIvK,OAAO,IAAI,WAAW,IAAIA,OAAO,EAAE;QACrC;QACAA,OAAO,CAACwK,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAAC1K,OAAO,CAACwK,SAAS,CAAC,GAAGlE,IAAI,CAACqE,KAAK,CAACrE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEvG,OAAO,CAACwK,SAAS,CAAC,CAAC,GAAG,MAAM;MAC9G;MACA,IAAIJ,QAAQ,EAAE;QACZ,IAAI,CAAC9K,eAAe,CAACsL,aAAa,GAAG,SAAS;QAC9C,IAAI,CAACtL,eAAe,CAACuL,SAAS,GAAG3N,SAAS,CAAC4N,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC;QAC7E,IAAI,CAACzL,eAAe,CAACkL,SAAS,GAAGxK,OAAO,IAAI,WAAW,IAAIA,OAAO,GAAGA,OAAO,CAACwK,SAAS,GAAG,MAAM,CAAC,CAAC;QACjG,IAAI,CAAClL,eAAe,CAAC0L,SAAS,GAAG9N,SAAS,CAAC+N,qBAAqB,CAC9Db,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC9K,eAAe,CAACuL,SAAS,EAC9B,IAAI,CAACvL,eAAe,CAACkL,SACvB,CAAC;MACH;MACA,IAAIxK,OAAO,EAAE;QACX,IAAI,CAACV,eAAe,GAAG4B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7B,eAAe,EAAEU,OAAO,CAAC;QACnE,IAAI,CAACoK,QAAQ,IAAI,WAAW,IAAIpK,OAAO,EAAE;UACvC,OAAO,IAAI,CAACV,eAAe,CAACkL,SAAS;QACvC;MACF;MACAF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAY,SAASA,CAAA,EAAG;IACV,IAAI,CAAC5L,eAAe,GAAG,IAAI;EAC7B;;EAEA;;EAEA6L,MAAMA,CAACC,IAAI,EAAE;IACX1N,SAAS,CAAC2N,KAAK,CAAC,CAAC;IACjB3N,SAAS,CAAC4N,OAAO,CAACF,IAAI,CAAC;IACvB,IAAI,CAAChH,MAAM,CAACmH,KAAK,CAAC7N,SAAS,CAAC;EAC9B;EAEA8N,qBAAqBA,CAACC,MAAM,EAAExK,UAAU,EAAEe,SAAS,EAAE;IACnD,MAAM0J,oBAAoB,GAAG;MAC3BC,iBAAiB,EAAE1K,UAAU,IAAIA,UAAU,CAAC0K,iBAAiB;MAC7DC,QAAQ,EAAE3K,UAAU,IAAIA,UAAU,CAAC2K,QAAQ;MAC3C5J,SAAS,EACPA,SAAS,IAAIA,SAAS,CAACW,SAAS,GAC5B;QACEA,SAAS,EAAEX,SAAS,CAACW;MACvB,CAAC,GACDU;IACR,CAAC;IAEDoI,MAAM,CAACH,OAAO,CAAC3M,KAAK,CAACE,eAAe,CAACgN,KAAK,CAACH,oBAAoB,CAAC,CAAC;EACnE;EAEAI,2BAA2BA,CAACL,MAAM,EAAExK,UAAU,EAAE;IAC9C,MAAM8K,0BAA0B,GAAG9K,UAAU,GACzC;MACEG,gBAAgB,EAAEH,UAAU,CAACG,gBAAgB;MAC7CC,SAAS,EAAEJ,UAAU,CAACI,SAAS;MAC/BC,eAAe,EAAEL,UAAU,CAACK,eAAe;MAC3CC,eAAe,EAAEN,UAAU,CAACM;IAC9B,CAAC,GACD8B,SAAS;IACb,IAAIpC,UAAU,CAAC+K,eAAe,EAAE;MAC9BD,0BAA0B,CAACC,eAAe,GAAG/K,UAAU,CAAC+K,eAAe;IACzE;IAEAP,MAAM,CAACH,OAAO,CAAC3M,KAAK,CAACG,qBAAqB,CAAC+M,KAAK,CAACE,0BAA0B,CAAC,CAAC;EAC/E;EAEA7H,mBAAmBA,CAAA,EAAG;IACpBxG,SAAS,CAAC2N,KAAK,CAAC,CAAC;IAEjB3N,SAAS,CAAC4N,OAAO,CAAC,yDAAyD,CAAC;IAC5E5N,SAAS,CAAC4N,OAAO,CACf,8EAA8E,GAC5E,gFAAgF,GAChF,yEAAyE,GACzE,uBAAuB,GACvB,6EACJ,CAAC;IAED,IAAI,CAACE,qBAAqB,CAAC9N,SAAS,EAAE,IAAI,CAACuD,UAAU,EAAE,IAAI,CAACe,SAAS,CAAC;IAEtEtE,SAAS,CAAC4N,OAAO,CAAC3M,KAAK,CAACU,UAAU,CAACwM,KAAK,CAAC,IAAI,CAAC7H,KAAK,CAAC,CAAC;IAErD,IAAI,CAAC8H,2BAA2B,CAACpO,SAAS,EAAE,IAAI,CAACuD,UAAU,CAAC;IAE5D,IAAI,CAACmD,MAAM,CAACmH,KAAK,CAAC7N,SAAS,CAAC;EAC9B;EAEAuO,aAAaA,CAAA,EAAG;IACd,MAAMC,IAAI,GAAG5O,MAAM,CAAC6O,OAAO,CAAC,IAAI,CAACpN,OAAO,CAAC;IACzC,IAAImN,IAAI,EAAE;MACRvN,KAAK,CAACI,OAAO,CAACqN,OAAO,CAACF,IAAI,EAAE;QAACG,MAAM,EAAE,IAAI,CAACzI,SAAS,CAACyI;MAAM,CAAC,CAAC;MAC5D,IAAI,CAACjI,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACI,OAAO,CAAC8M,KAAK,CAACK,IAAI,CAAC,CAAC;IAC9C;EACF;EAEApH,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACqG,MAAM,CAAC,aAAa,CAAC;EAC5B;EAEAtG,SAASA,CAAC1F,GAAG,EAAE;IACb,IAAI,CAAC,IAAI,CAACgF,WAAW,EAAE;MACrB,IAAI,CAAC8H,aAAa,CAAC,CAAC;MACpB,IAAI,CAACnH,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACX,WAAW,GAAG,IAAI;IACzB;IAEA,IAAIhF,GAAG,CAAC2I,SAAS,IAAI3I,GAAG,CAACmN,MAAM,EAAE;MAC/B,MAAM;QAACC;MAAK,CAAC,GAAGpN,GAAG;MACnB,MAAMa,OAAO,GAAG;QACdqM,MAAM,EAAE,IAAI,CAACzI,SAAS,CAACyI,MAAM;QAC7BG,aAAa,EAAE,IAAI,CAAC7I,gBAAgB,GAAG,IAAI,CAACC,SAAS,CAAC4I,aAAa,GAAGnJ,SAAS;QAC/EjE,UAAU,EAAE,IAAI,CAACqB,gBAAgB,CAACgM,eAAe;QACjDC,MAAM,EAAE,IAAI,CAACnM,OAAO;QACpBoM,QAAQ,EAAE,IAAI,CAAC7L,SAAS;QACxB8L,UAAU,EAAE,IAAI,CAAC7L,WAAW;QAC5B8L,QAAQ,EAAE;MACZ,CAAC;MACDlO,KAAK,CAACQ,GAAG,CAACiN,OAAO,CAACG,KAAK,EAAEvM,OAAO,CAAC;MACjC,IAAI,CAACoE,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACQ,GAAG,CAAC0M,KAAK,CAACU,KAAK,CAAC,CAAC;MAEzC,IAAIvM,OAAO,CAAC6M,QAAQ,CAAC5N,MAAM,EAAE;QAC3B,IAAI,CAAC6E,WAAW,GAAG,IAAI;QACvB,IAAI,CAACpD,oBAAoB,CAACoM,WAAW,CAAC9M,OAAO,CAAC6M,QAAQ,CAAC;MACzD;IACF;EACF;EAEA9H,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACoG,MAAM,CAAC,cAAc,CAAC;EAC7B;EAEAlG,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1E,OAAO,CAACtB,MAAM,EAAE;MACvBvB,SAAS,CAAC2N,KAAK,CAAC,CAAC;MACjB3N,SAAS,CAAC4N,OAAO,CAAE,sBAAqB,IAAI,CAAC/K,OAAO,CAACtB,MAAO,IAAG,CAAC;MAChE,IAAI,CAACsB,OAAO,CAACoE,OAAO,CAAC0E,KAAK,IAAI;QAC5B3L,SAAS,CAAC4N,OAAO,CAAE,mBAAkBjC,KAAM,KAAI,CAAC;MAClD,CAAC,CAAC;MACF3L,SAAS,CAAC4N,OAAO,CAAC,eAAe,CAAC;MAElC,IAAI,CAAClH,MAAM,CAACmH,KAAK,CAAC7N,SAAS,CAAC;IAC9B;EACF;EAEAwH,gBAAgBA,CAAA,EAAG;IACjB;IACA,IAAI,CAACd,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACS,UAAU,CAACyM,KAAK,CAAC,IAAI,CAACpL,gBAAgB,CAACsM,WAAW,CAAC,CAAC;EAC9E;EAEA5H,2BAA2BA,CAAA,EAAG;IAC5B,MAAMnF,OAAO,GAAG;MACdqM,MAAM,EAAE,IAAI,CAACzI,SAAS,CAACyI;IACzB,CAAC;IACD1N,KAAK,CAACgB,sBAAsB,CAACyM,OAAO,CAAC,IAAI,CAACpL,qBAAqB,EAAEhB,OAAO,CAAC;IACzE,IAAI,CAACoE,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACgB,sBAAsB,CAACkM,KAAK,CAAC,IAAI,CAAC7K,qBAAqB,CAAC,CAAC;EACnF;EAEA0E,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACtB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACkB,SAAS,CAACgM,KAAK,CAAC,IAAI,CAAChM,SAAS,CAAC,CAAC;EAC1D;EAEAuF,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAChB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACC,eAAe,CAACiN,KAAK,CAAC,IAAI,CAACjN,eAAe,CAAC2N,KAAK,CAAC,CAAC;EAC5E;EAEAlH,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAACjB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACW,eAAe,CAACuM,KAAK,CAAC,IAAI,CAACvM,eAAe,CAAC,CAAC;EACtE;EAEAgG,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAClB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACY,WAAW,CAACsM,KAAK,CAAC,IAAI,CAAC7J,SAAS,CAACC,OAAO,CAAC,CAAC;EACpE;EAEAsD,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACnB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACa,UAAU,CAACqM,KAAK,CAAC,IAAI,CAAC7J,SAAS,CAAC,CAAC;EAC3D;EAEAyD,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACrB,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACiB,YAAY,CAACiM,KAAK,CAAC,IAAI,CAACjM,YAAY,CAAC,CAAC;EAChE;EAEAoF,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAACZ,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACc,UAAU,CAACoM,KAAK,CAAC,IAAI,CAACpM,UAAU,CAAC,CAAC;EAC5D;EAEA+F,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACyE,WAAW,EAAE;MACpB,IAAI,IAAI,CAACA,WAAW,CAACD,OAAO,KAAK3G,SAAS,EAAE;QAC1C,MAAM2J,KAAK,GAAG,IAAI,CAACpJ,SAAS,CAACqJ,QAAQ,CAAC,IAAI,CAAChD,WAAW,CAACD,OAAO,CAAC;QAC/D,MAAMkD,SAAS,GAAG,IAAI,CAACzM,gBAAgB,CAAC0M,QAAQ,CAAC;UAC/CC,MAAM,EAAG,YAAWJ,KAAK,CAAC9M,IAAK,EAAC;UAChCmN,IAAI,EAAErQ,OAAO,CAACsQ;QAChB,CAAC,CAAC;QAEF,IAAI,CAACrD,WAAW,GAAG;UACjB,GAAG,IAAI,CAACA,WAAW;UACnBsD,GAAG,EAAEL;QACP,CAAC;MACH;MACA,IAAI,CAAC9I,MAAM,CAACmH,KAAK,CAAC5M,KAAK,CAACe,OAAO,CAACmM,KAAK,CAAC;QAAC0B,GAAG,EAAE,IAAI,CAACtD,WAAW,CAACsD;MAAG,CAAC,CAAC,CAAC;IACrE;EACF;EAEA5H,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC7B,WAAW,EAAE;MACpBpG,SAAS,CAAC2N,KAAK,CAAC,CAAC;MACjB3N,SAAS,CAAC4N,OAAO,CAAE,wBAAuB,IAAI,CAAC5K,oBAAoB,CAAC8M,QAAS,KAAI,CAAC;MAClF,IAAI,CAACpJ,MAAM,CAACmH,KAAK,CAAC7N,SAAS,CAAC;IAC9B;EACF;EAEA+P,gBAAgBA,CAAA,EAAG;IACjB;IACA;IACA;EAAA;EAGF7H,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACuF,MAAM,CAAC,cAAc,CAAC;EAC7B;AACF;AAEAuC,MAAM,CAACC,OAAO,GAAG7N,eAAe"}