{"version": 3, "file": "workbook-reader.js", "names": ["fs", "require", "EventEmitter", "PassThrough", "Readable", "nodeStream", "unzip", "tmp", "iterateStream", "parseSax", "StyleManager", "WorkbookXform", "RelationshipsXform", "WorksheetReader", "HyperlinkReader", "setGracefulCleanup", "WorkbookReader", "constructor", "input", "options", "arguments", "length", "undefined", "worksheets", "sharedStrings", "hyperlinks", "styles", "entries", "init", "_getStream", "createReadStream", "Error", "read", "eventType", "value", "parse", "emit", "error", "Symbol", "asyncIterator", "stream", "zip", "Parse", "forceStream", "pipe", "waitingWorkSheets", "entry", "match", "sheetNo", "path", "_parseRels", "_parseWorkbook", "_parseSharedStrings", "_parseStyles", "workbookRels", "_parseWorksheet", "Promise", "resolve", "reject", "file", "err", "fd", "tempFileCleanupCallback", "push", "tempStream", "createWriteStream", "on", "_parseHyperlinks", "autodrain", "fileStream", "_emitEntry", "payload", "xform", "parseStream", "type", "workbook", "properties", "map", "workbookPr", "model", "text", "richText", "index", "font", "events", "node", "name", "bold", "charset", "parseInt", "attributes", "color", "rgb", "argb", "val", "theme", "family", "italic", "outline", "size", "underline", "vertAlign", "iterator", "id", "worksheetReader", "matchingRel", "find", "rel", "Target", "matchingSheet", "sheets", "sheet", "rId", "Id", "state", "hyperlinksReader", "Options", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/workbook-reader.js"], "sourcesContent": ["const fs = require('fs');\nconst {EventEmitter} = require('events');\nconst {PassThrough, Readable} = require('readable-stream');\nconst nodeStream = require('stream');\nconst unzip = require('unzipper');\nconst tmp = require('tmp');\nconst iterateStream = require('../../utils/iterate-stream');\nconst parseSax = require('../../utils/parse-sax');\n\nconst StyleManager = require('../../xlsx/xform/style/styles-xform');\nconst WorkbookXform = require('../../xlsx/xform/book/workbook-xform');\nconst RelationshipsXform = require('../../xlsx/xform/core/relationships-xform');\n\nconst WorksheetReader = require('./worksheet-reader');\nconst HyperlinkReader = require('./hyperlink-reader');\n\ntmp.setGracefulCleanup();\n\nclass WorkbookReader extends EventEmitter {\n  constructor(input, options = {}) {\n    super();\n\n    this.input = input;\n\n    this.options = {\n      worksheets: 'emit',\n      sharedStrings: 'cache',\n      hyperlinks: 'ignore',\n      styles: 'ignore',\n      entries: 'ignore',\n      ...options,\n    };\n\n    this.styles = new StyleManager();\n    this.styles.init();\n  }\n\n  _getStream(input) {\n    if (input instanceof nodeStream.Readable || input instanceof Readable) {\n      return input;\n    }\n    if (typeof input === 'string') {\n      return fs.createReadStream(input);\n    }\n    throw new Error(`Could not recognise input: ${input}`);\n  }\n\n  async read(input, options) {\n    try {\n      for await (const {eventType, value} of this.parse(input, options)) {\n        switch (eventType) {\n          case 'shared-strings':\n            this.emit(eventType, value);\n            break;\n          case 'worksheet':\n            this.emit(eventType, value);\n            await value.read();\n            break;\n          case 'hyperlinks':\n            this.emit(eventType, value);\n            break;\n        }\n      }\n      this.emit('end');\n      this.emit('finished');\n    } catch (error) {\n      this.emit('error', error);\n    }\n  }\n\n  async *[Symbol.asyncIterator]() {\n    for await (const {eventType, value} of this.parse()) {\n      if (eventType === 'worksheet') {\n        yield value;\n      }\n    }\n  }\n\n  async *parse(input, options) {\n    if (options) this.options = options;\n    const stream = (this.stream = this._getStream(input || this.input));\n    const zip = unzip.Parse({forceStream: true});\n    stream.pipe(zip);\n\n    // worksheets, deferred for parsing after shared strings reading\n    const waitingWorkSheets = [];\n\n    for await (const entry of iterateStream(zip)) {\n      let match;\n      let sheetNo;\n      switch (entry.path) {\n        case '_rels/.rels':\n          break;\n        case 'xl/_rels/workbook.xml.rels':\n          await this._parseRels(entry);\n          break;\n        case 'xl/workbook.xml':\n          await this._parseWorkbook(entry);\n          break;\n        case 'xl/sharedStrings.xml':\n          yield* this._parseSharedStrings(entry);\n          break;\n        case 'xl/styles.xml':\n          await this._parseStyles(entry);\n          break;\n        default:\n          if (entry.path.match(/xl\\/worksheets\\/sheet\\d+[.]xml/)) {\n            match = entry.path.match(/xl\\/worksheets\\/sheet(\\d+)[.]xml/);\n            sheetNo = match[1];\n            if (this.sharedStrings && this.workbookRels) {\n              yield* this._parseWorksheet(iterateStream(entry), sheetNo);\n            } else {\n              // create temp file for each worksheet\n              await new Promise((resolve, reject) => {\n                tmp.file((err, path, fd, tempFileCleanupCallback) => {\n                  if (err) {\n                    return reject(err);\n                  }\n                  waitingWorkSheets.push({sheetNo, path, tempFileCleanupCallback});\n\n                  const tempStream = fs.createWriteStream(path);\n                  tempStream.on('error', reject);\n                  entry.pipe(tempStream);\n                  return tempStream.on('finish', () => {\n                    return resolve();\n                  });\n                });\n              });\n            }\n          } else if (entry.path.match(/xl\\/worksheets\\/_rels\\/sheet\\d+[.]xml.rels/)) {\n            match = entry.path.match(/xl\\/worksheets\\/_rels\\/sheet(\\d+)[.]xml.rels/);\n            sheetNo = match[1];\n            yield* this._parseHyperlinks(iterateStream(entry), sheetNo);\n          }\n          break;\n      }\n      entry.autodrain();\n    }\n\n    for (const {sheetNo, path, tempFileCleanupCallback} of waitingWorkSheets) {\n      let fileStream = fs.createReadStream(path);\n      // TODO: Remove once node v8 is deprecated\n      // Detect and upgrade old fileStreams\n      if (!fileStream[Symbol.asyncIterator]) {\n        fileStream = fileStream.pipe(new PassThrough());\n      }\n      yield* this._parseWorksheet(fileStream, sheetNo);\n      tempFileCleanupCallback();\n    }\n  }\n\n  _emitEntry(payload) {\n    if (this.options.entries === 'emit') {\n      this.emit('entry', payload);\n    }\n  }\n\n  async _parseRels(entry) {\n    const xform = new RelationshipsXform();\n    this.workbookRels = await xform.parseStream(iterateStream(entry));\n  }\n\n  async _parseWorkbook(entry) {\n    this._emitEntry({type: 'workbook'});\n\n    const workbook = new WorkbookXform();\n    await workbook.parseStream(iterateStream(entry));\n\n    this.properties = workbook.map.workbookPr;\n    this.model = workbook.model;\n  }\n\n  async *_parseSharedStrings(entry) {\n    this._emitEntry({type: 'shared-strings'});\n    switch (this.options.sharedStrings) {\n      case 'cache':\n        this.sharedStrings = [];\n        break;\n      case 'emit':\n        break;\n      default:\n        return;\n    }\n\n    let text = null;\n    let richText = [];\n    let index = 0;\n    let font = null;\n    for await (const events of parseSax(iterateStream(entry))) {\n      for (const {eventType, value} of events) {\n        if (eventType === 'opentag') {\n          const node = value;\n          switch (node.name) {\n            case 'b':\n              font = font || {};\n              font.bold = true;\n              break;\n            case 'charset':\n              font = font || {};\n              font.charset = parseInt(node.attributes.charset, 10);\n              break;\n            case 'color':\n              font = font || {};\n              font.color = {};\n              if (node.attributes.rgb) {\n                font.color.argb = node.attributes.argb;\n              }\n              if (node.attributes.val) {\n                font.color.argb = node.attributes.val;\n              }\n              if (node.attributes.theme) {\n                font.color.theme = node.attributes.theme;\n              }\n              break;\n            case 'family':\n              font = font || {};\n              font.family = parseInt(node.attributes.val, 10);\n              break;\n            case 'i':\n              font = font || {};\n              font.italic = true;\n              break;\n            case 'outline':\n              font = font || {};\n              font.outline = true;\n              break;\n            case 'rFont':\n              font = font || {};\n              font.name = node.value;\n              break;\n            case 'si':\n              font = null;\n              richText = [];\n              text = null;\n              break;\n            case 'sz':\n              font = font || {};\n              font.size = parseInt(node.attributes.val, 10);\n              break;\n            case 'strike':\n              break;\n            case 't':\n              text = null;\n              break;\n            case 'u':\n              font = font || {};\n              font.underline = true;\n              break;\n            case 'vertAlign':\n              font = font || {};\n              font.vertAlign = node.attributes.val;\n              break;\n          }\n        } else if (eventType === 'text') {\n          text = text ? text + value : value;\n        } else if (eventType === 'closetag') {\n          const node = value;\n          switch (node.name) {\n            case 'r':\n              richText.push({\n                font,\n                text,\n              });\n\n              font = null;\n              text = null;\n              break;\n            case 'si':\n              if (this.options.sharedStrings === 'cache') {\n                this.sharedStrings.push(richText.length ? {richText} : text);\n              } else if (this.options.sharedStrings === 'emit') {\n                yield {index: index++, text: richText.length ? {richText} : text};\n              }\n\n              richText = [];\n              font = null;\n              text = null;\n              break;\n          }\n        }\n      }\n    }\n  }\n\n  async _parseStyles(entry) {\n    this._emitEntry({type: 'styles'});\n    if (this.options.styles === 'cache') {\n      this.styles = new StyleManager();\n      await this.styles.parseStream(iterateStream(entry));\n    }\n  }\n\n  *_parseWorksheet(iterator, sheetNo) {\n    this._emitEntry({type: 'worksheet', id: sheetNo});\n    const worksheetReader = new WorksheetReader({\n      workbook: this,\n      id: sheetNo,\n      iterator,\n      options: this.options,\n    });\n\n    const matchingRel = (this.workbookRels || []).find(rel => rel.Target === `worksheets/sheet${sheetNo}.xml`);\n    const matchingSheet = matchingRel && (this.model.sheets || []).find(sheet => sheet.rId === matchingRel.Id);\n    if (matchingSheet) {\n      worksheetReader.id = matchingSheet.id;\n      worksheetReader.name = matchingSheet.name;\n      worksheetReader.state = matchingSheet.state;\n    }\n    if (this.options.worksheets === 'emit') {\n      yield {eventType: 'worksheet', value: worksheetReader};\n    }\n  }\n\n  *_parseHyperlinks(iterator, sheetNo) {\n    this._emitEntry({type: 'hyperlinks', id: sheetNo});\n    const hyperlinksReader = new HyperlinkReader({\n      workbook: this,\n      id: sheetNo,\n      iterator,\n      options: this.options,\n    });\n    if (this.options.hyperlinks === 'emit') {\n      yield {eventType: 'hyperlinks', value: hyperlinksReader};\n    }\n  }\n}\n\n// for reference - these are the valid values for options\nWorkbookReader.Options = {\n  worksheets: ['emit', 'ignore'],\n  sharedStrings: ['cache', 'emit', 'ignore'],\n  hyperlinks: ['cache', 'emit', 'ignore'],\n  styles: ['cache', 'ignore'],\n  entries: ['emit', 'ignore'],\n};\n\nmodule.exports = WorkbookReader;\n"], "mappings": ";;AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AACxB,MAAM;EAACC;AAAY,CAAC,GAAGD,OAAO,CAAC,QAAQ,CAAC;AACxC,MAAM;EAACE,WAAW;EAAEC;AAAQ,CAAC,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAC1D,MAAMI,UAAU,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AACpC,MAAMK,KAAK,GAAGL,OAAO,CAAC,UAAU,CAAC;AACjC,MAAMM,GAAG,GAAGN,OAAO,CAAC,KAAK,CAAC;AAC1B,MAAMO,aAAa,GAAGP,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMQ,QAAQ,GAAGR,OAAO,CAAC,uBAAuB,CAAC;AAEjD,MAAMS,YAAY,GAAGT,OAAO,CAAC,qCAAqC,CAAC;AACnE,MAAMU,aAAa,GAAGV,OAAO,CAAC,sCAAsC,CAAC;AACrE,MAAMW,kBAAkB,GAAGX,OAAO,CAAC,2CAA2C,CAAC;AAE/E,MAAMY,eAAe,GAAGZ,OAAO,CAAC,oBAAoB,CAAC;AACrD,MAAMa,eAAe,GAAGb,OAAO,CAAC,oBAAoB,CAAC;AAErDM,GAAG,CAACQ,kBAAkB,CAAC,CAAC;AAExB,MAAMC,cAAc,SAASd,YAAY,CAAC;EACxCe,WAAWA,CAACC,KAAK,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC7B,KAAK,CAAC,CAAC;IAEP,IAAI,CAACF,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACC,OAAO,GAAG;MACbI,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,OAAO;MACtBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,QAAQ;MACjB,GAAGR;IACL,CAAC;IAED,IAAI,CAACO,MAAM,GAAG,IAAIhB,YAAY,CAAC,CAAC;IAChC,IAAI,CAACgB,MAAM,CAACE,IAAI,CAAC,CAAC;EACpB;EAEAC,UAAUA,CAACX,KAAK,EAAE;IAChB,IAAIA,KAAK,YAAYb,UAAU,CAACD,QAAQ,IAAIc,KAAK,YAAYd,QAAQ,EAAE;MACrE,OAAOc,KAAK;IACd;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOlB,EAAE,CAAC8B,gBAAgB,CAACZ,KAAK,CAAC;IACnC;IACA,MAAM,IAAIa,KAAK,CAAE,8BAA6Bb,KAAM,EAAC,CAAC;EACxD;EAEA,MAAMc,IAAIA,CAACd,KAAK,EAAEC,OAAO,EAAE;IACzB,IAAI;MACF,WAAW,MAAM;QAACc,SAAS;QAAEC;MAAK,CAAC,IAAI,IAAI,CAACC,KAAK,CAACjB,KAAK,EAAEC,OAAO,CAAC,EAAE;QACjE,QAAQc,SAAS;UACf,KAAK,gBAAgB;YACnB,IAAI,CAACG,IAAI,CAACH,SAAS,EAAEC,KAAK,CAAC;YAC3B;UACF,KAAK,WAAW;YACd,IAAI,CAACE,IAAI,CAACH,SAAS,EAAEC,KAAK,CAAC;YAC3B,MAAMA,KAAK,CAACF,IAAI,CAAC,CAAC;YAClB;UACF,KAAK,YAAY;YACf,IAAI,CAACI,IAAI,CAACH,SAAS,EAAEC,KAAK,CAAC;YAC3B;QACJ;MACF;MACA,IAAI,CAACE,IAAI,CAAC,KAAK,CAAC;MAChB,IAAI,CAACA,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAEC,KAAK,CAAC;IAC3B;EACF;EAEA,QAAQC,MAAM,CAACC,aAAa,IAAI;IAC9B,WAAW,MAAM;MAACN,SAAS;MAAEC;IAAK,CAAC,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;MACnD,IAAIF,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAMC,KAAK;MACb;IACF;EACF;EAEA,OAAOC,KAAKA,CAACjB,KAAK,EAAEC,OAAO,EAAE;IAC3B,IAAIA,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACnC,MAAMqB,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG,IAAI,CAACX,UAAU,CAACX,KAAK,IAAI,IAAI,CAACA,KAAK,CAAE;IACnE,MAAMuB,GAAG,GAAGnC,KAAK,CAACoC,KAAK,CAAC;MAACC,WAAW,EAAE;IAAI,CAAC,CAAC;IAC5CH,MAAM,CAACI,IAAI,CAACH,GAAG,CAAC;;IAEhB;IACA,MAAMI,iBAAiB,GAAG,EAAE;IAE5B,WAAW,MAAMC,KAAK,IAAItC,aAAa,CAACiC,GAAG,CAAC,EAAE;MAC5C,IAAIM,KAAK;MACT,IAAIC,OAAO;MACX,QAAQF,KAAK,CAACG,IAAI;QAChB,KAAK,aAAa;UAChB;QACF,KAAK,4BAA4B;UAC/B,MAAM,IAAI,CAACC,UAAU,CAACJ,KAAK,CAAC;UAC5B;QACF,KAAK,iBAAiB;UACpB,MAAM,IAAI,CAACK,cAAc,CAACL,KAAK,CAAC;UAChC;QACF,KAAK,sBAAsB;UACzB,OAAO,IAAI,CAACM,mBAAmB,CAACN,KAAK,CAAC;UACtC;QACF,KAAK,eAAe;UAClB,MAAM,IAAI,CAACO,YAAY,CAACP,KAAK,CAAC;UAC9B;QACF;UACE,IAAIA,KAAK,CAACG,IAAI,CAACF,KAAK,CAAC,gCAAgC,CAAC,EAAE;YACtDA,KAAK,GAAGD,KAAK,CAACG,IAAI,CAACF,KAAK,CAAC,kCAAkC,CAAC;YAC5DC,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;YAClB,IAAI,IAAI,CAACvB,aAAa,IAAI,IAAI,CAAC8B,YAAY,EAAE;cAC3C,OAAO,IAAI,CAACC,eAAe,CAAC/C,aAAa,CAACsC,KAAK,CAAC,EAAEE,OAAO,CAAC;YAC5D,CAAC,MAAM;cACL;cACA,MAAM,IAAIQ,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;gBACrCnD,GAAG,CAACoD,IAAI,CAAC,CAACC,GAAG,EAAEX,IAAI,EAAEY,EAAE,EAAEC,uBAAuB,KAAK;kBACnD,IAAIF,GAAG,EAAE;oBACP,OAAOF,MAAM,CAACE,GAAG,CAAC;kBACpB;kBACAf,iBAAiB,CAACkB,IAAI,CAAC;oBAACf,OAAO;oBAAEC,IAAI;oBAAEa;kBAAuB,CAAC,CAAC;kBAEhE,MAAME,UAAU,GAAGhE,EAAE,CAACiE,iBAAiB,CAAChB,IAAI,CAAC;kBAC7Ce,UAAU,CAACE,EAAE,CAAC,OAAO,EAAER,MAAM,CAAC;kBAC9BZ,KAAK,CAACF,IAAI,CAACoB,UAAU,CAAC;kBACtB,OAAOA,UAAU,CAACE,EAAE,CAAC,QAAQ,EAAE,MAAM;oBACnC,OAAOT,OAAO,CAAC,CAAC;kBAClB,CAAC,CAAC;gBACJ,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ;UACF,CAAC,MAAM,IAAIX,KAAK,CAACG,IAAI,CAACF,KAAK,CAAC,4CAA4C,CAAC,EAAE;YACzEA,KAAK,GAAGD,KAAK,CAACG,IAAI,CAACF,KAAK,CAAC,8CAA8C,CAAC;YACxEC,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;YAClB,OAAO,IAAI,CAACoB,gBAAgB,CAAC3D,aAAa,CAACsC,KAAK,CAAC,EAAEE,OAAO,CAAC;UAC7D;UACA;MACJ;MACAF,KAAK,CAACsB,SAAS,CAAC,CAAC;IACnB;IAEA,KAAK,MAAM;MAACpB,OAAO;MAAEC,IAAI;MAAEa;IAAuB,CAAC,IAAIjB,iBAAiB,EAAE;MACxE,IAAIwB,UAAU,GAAGrE,EAAE,CAAC8B,gBAAgB,CAACmB,IAAI,CAAC;MAC1C;MACA;MACA,IAAI,CAACoB,UAAU,CAAC/B,MAAM,CAACC,aAAa,CAAC,EAAE;QACrC8B,UAAU,GAAGA,UAAU,CAACzB,IAAI,CAAC,IAAIzC,WAAW,CAAC,CAAC,CAAC;MACjD;MACA,OAAO,IAAI,CAACoD,eAAe,CAACc,UAAU,EAAErB,OAAO,CAAC;MAChDc,uBAAuB,CAAC,CAAC;IAC3B;EACF;EAEAQ,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI,IAAI,CAACpD,OAAO,CAACQ,OAAO,KAAK,MAAM,EAAE;MACnC,IAAI,CAACS,IAAI,CAAC,OAAO,EAAEmC,OAAO,CAAC;IAC7B;EACF;EAEA,MAAMrB,UAAUA,CAACJ,KAAK,EAAE;IACtB,MAAM0B,KAAK,GAAG,IAAI5D,kBAAkB,CAAC,CAAC;IACtC,IAAI,CAAC0C,YAAY,GAAG,MAAMkB,KAAK,CAACC,WAAW,CAACjE,aAAa,CAACsC,KAAK,CAAC,CAAC;EACnE;EAEA,MAAMK,cAAcA,CAACL,KAAK,EAAE;IAC1B,IAAI,CAACwB,UAAU,CAAC;MAACI,IAAI,EAAE;IAAU,CAAC,CAAC;IAEnC,MAAMC,QAAQ,GAAG,IAAIhE,aAAa,CAAC,CAAC;IACpC,MAAMgE,QAAQ,CAACF,WAAW,CAACjE,aAAa,CAACsC,KAAK,CAAC,CAAC;IAEhD,IAAI,CAAC8B,UAAU,GAAGD,QAAQ,CAACE,GAAG,CAACC,UAAU;IACzC,IAAI,CAACC,KAAK,GAAGJ,QAAQ,CAACI,KAAK;EAC7B;EAEA,OAAO3B,mBAAmBA,CAACN,KAAK,EAAE;IAChC,IAAI,CAACwB,UAAU,CAAC;MAACI,IAAI,EAAE;IAAgB,CAAC,CAAC;IACzC,QAAQ,IAAI,CAACvD,OAAO,CAACK,aAAa;MAChC,KAAK,OAAO;QACV,IAAI,CAACA,aAAa,GAAG,EAAE;QACvB;MACF,KAAK,MAAM;QACT;MACF;QACE;IACJ;IAEA,IAAIwD,IAAI,GAAG,IAAI;IACf,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,IAAI,GAAG,IAAI;IACf,WAAW,MAAMC,MAAM,IAAI3E,QAAQ,CAACD,aAAa,CAACsC,KAAK,CAAC,CAAC,EAAE;MACzD,KAAK,MAAM;QAACb,SAAS;QAAEC;MAAK,CAAC,IAAIkD,MAAM,EAAE;QACvC,IAAInD,SAAS,KAAK,SAAS,EAAE;UAC3B,MAAMoD,IAAI,GAAGnD,KAAK;UAClB,QAAQmD,IAAI,CAACC,IAAI;YACf,KAAK,GAAG;cACNH,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACI,IAAI,GAAG,IAAI;cAChB;YACF,KAAK,SAAS;cACZJ,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACK,OAAO,GAAGC,QAAQ,CAACJ,IAAI,CAACK,UAAU,CAACF,OAAO,EAAE,EAAE,CAAC;cACpD;YACF,KAAK,OAAO;cACVL,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACQ,KAAK,GAAG,CAAC,CAAC;cACf,IAAIN,IAAI,CAACK,UAAU,CAACE,GAAG,EAAE;gBACvBT,IAAI,CAACQ,KAAK,CAACE,IAAI,GAAGR,IAAI,CAACK,UAAU,CAACG,IAAI;cACxC;cACA,IAAIR,IAAI,CAACK,UAAU,CAACI,GAAG,EAAE;gBACvBX,IAAI,CAACQ,KAAK,CAACE,IAAI,GAAGR,IAAI,CAACK,UAAU,CAACI,GAAG;cACvC;cACA,IAAIT,IAAI,CAACK,UAAU,CAACK,KAAK,EAAE;gBACzBZ,IAAI,CAACQ,KAAK,CAACI,KAAK,GAAGV,IAAI,CAACK,UAAU,CAACK,KAAK;cAC1C;cACA;YACF,KAAK,QAAQ;cACXZ,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACa,MAAM,GAAGP,QAAQ,CAACJ,IAAI,CAACK,UAAU,CAACI,GAAG,EAAE,EAAE,CAAC;cAC/C;YACF,KAAK,GAAG;cACNX,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACc,MAAM,GAAG,IAAI;cAClB;YACF,KAAK,SAAS;cACZd,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACe,OAAO,GAAG,IAAI;cACnB;YACF,KAAK,OAAO;cACVf,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACG,IAAI,GAAGD,IAAI,CAACnD,KAAK;cACtB;YACF,KAAK,IAAI;cACPiD,IAAI,GAAG,IAAI;cACXF,QAAQ,GAAG,EAAE;cACbD,IAAI,GAAG,IAAI;cACX;YACF,KAAK,IAAI;cACPG,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACgB,IAAI,GAAGV,QAAQ,CAACJ,IAAI,CAACK,UAAU,CAACI,GAAG,EAAE,EAAE,CAAC;cAC7C;YACF,KAAK,QAAQ;cACX;YACF,KAAK,GAAG;cACNd,IAAI,GAAG,IAAI;cACX;YACF,KAAK,GAAG;cACNG,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACiB,SAAS,GAAG,IAAI;cACrB;YACF,KAAK,WAAW;cACdjB,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;cACjBA,IAAI,CAACkB,SAAS,GAAGhB,IAAI,CAACK,UAAU,CAACI,GAAG;cACpC;UACJ;QACF,CAAC,MAAM,IAAI7D,SAAS,KAAK,MAAM,EAAE;UAC/B+C,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG9C,KAAK,GAAGA,KAAK;QACpC,CAAC,MAAM,IAAID,SAAS,KAAK,UAAU,EAAE;UACnC,MAAMoD,IAAI,GAAGnD,KAAK;UAClB,QAAQmD,IAAI,CAACC,IAAI;YACf,KAAK,GAAG;cACNL,QAAQ,CAAClB,IAAI,CAAC;gBACZoB,IAAI;gBACJH;cACF,CAAC,CAAC;cAEFG,IAAI,GAAG,IAAI;cACXH,IAAI,GAAG,IAAI;cACX;YACF,KAAK,IAAI;cACP,IAAI,IAAI,CAAC7D,OAAO,CAACK,aAAa,KAAK,OAAO,EAAE;gBAC1C,IAAI,CAACA,aAAa,CAACuC,IAAI,CAACkB,QAAQ,CAAC5D,MAAM,GAAG;kBAAC4D;gBAAQ,CAAC,GAAGD,IAAI,CAAC;cAC9D,CAAC,MAAM,IAAI,IAAI,CAAC7D,OAAO,CAACK,aAAa,KAAK,MAAM,EAAE;gBAChD,MAAM;kBAAC0D,KAAK,EAAEA,KAAK,EAAE;kBAAEF,IAAI,EAAEC,QAAQ,CAAC5D,MAAM,GAAG;oBAAC4D;kBAAQ,CAAC,GAAGD;gBAAI,CAAC;cACnE;cAEAC,QAAQ,GAAG,EAAE;cACbE,IAAI,GAAG,IAAI;cACXH,IAAI,GAAG,IAAI;cACX;UACJ;QACF;MACF;IACF;EACF;EAEA,MAAM3B,YAAYA,CAACP,KAAK,EAAE;IACxB,IAAI,CAACwB,UAAU,CAAC;MAACI,IAAI,EAAE;IAAQ,CAAC,CAAC;IACjC,IAAI,IAAI,CAACvD,OAAO,CAACO,MAAM,KAAK,OAAO,EAAE;MACnC,IAAI,CAACA,MAAM,GAAG,IAAIhB,YAAY,CAAC,CAAC;MAChC,MAAM,IAAI,CAACgB,MAAM,CAAC+C,WAAW,CAACjE,aAAa,CAACsC,KAAK,CAAC,CAAC;IACrD;EACF;EAEA,CAACS,eAAeA,CAAC+C,QAAQ,EAAEtD,OAAO,EAAE;IAClC,IAAI,CAACsB,UAAU,CAAC;MAACI,IAAI,EAAE,WAAW;MAAE6B,EAAE,EAAEvD;IAAO,CAAC,CAAC;IACjD,MAAMwD,eAAe,GAAG,IAAI3F,eAAe,CAAC;MAC1C8D,QAAQ,EAAE,IAAI;MACd4B,EAAE,EAAEvD,OAAO;MACXsD,QAAQ;MACRnF,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC;IAEF,MAAMsF,WAAW,GAAG,CAAC,IAAI,CAACnD,YAAY,IAAI,EAAE,EAAEoD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAM,mBAAkB5D,OAAQ,MAAK,CAAC;IAC1G,MAAM6D,aAAa,GAAGJ,WAAW,IAAI,CAAC,IAAI,CAAC1B,KAAK,CAAC+B,MAAM,IAAI,EAAE,EAAEJ,IAAI,CAACK,KAAK,IAAIA,KAAK,CAACC,GAAG,KAAKP,WAAW,CAACQ,EAAE,CAAC;IAC1G,IAAIJ,aAAa,EAAE;MACjBL,eAAe,CAACD,EAAE,GAAGM,aAAa,CAACN,EAAE;MACrCC,eAAe,CAAClB,IAAI,GAAGuB,aAAa,CAACvB,IAAI;MACzCkB,eAAe,CAACU,KAAK,GAAGL,aAAa,CAACK,KAAK;IAC7C;IACA,IAAI,IAAI,CAAC/F,OAAO,CAACI,UAAU,KAAK,MAAM,EAAE;MACtC,MAAM;QAACU,SAAS,EAAE,WAAW;QAAEC,KAAK,EAAEsE;MAAe,CAAC;IACxD;EACF;EAEA,CAACrC,gBAAgBA,CAACmC,QAAQ,EAAEtD,OAAO,EAAE;IACnC,IAAI,CAACsB,UAAU,CAAC;MAACI,IAAI,EAAE,YAAY;MAAE6B,EAAE,EAAEvD;IAAO,CAAC,CAAC;IAClD,MAAMmE,gBAAgB,GAAG,IAAIrG,eAAe,CAAC;MAC3C6D,QAAQ,EAAE,IAAI;MACd4B,EAAE,EAAEvD,OAAO;MACXsD,QAAQ;MACRnF,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,OAAO,CAACM,UAAU,KAAK,MAAM,EAAE;MACtC,MAAM;QAACQ,SAAS,EAAE,YAAY;QAAEC,KAAK,EAAEiF;MAAgB,CAAC;IAC1D;EACF;AACF;;AAEA;AACAnG,cAAc,CAACoG,OAAO,GAAG;EACvB7F,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;EAC9BC,aAAa,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;EAC1CC,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;EACvCC,MAAM,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC3BC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;AAC5B,CAAC;AAED0F,MAAM,CAACC,OAAO,GAAGtG,cAAc"}