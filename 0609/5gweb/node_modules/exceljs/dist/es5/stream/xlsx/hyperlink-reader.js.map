{"version": 3, "file": "hyperlink-reader.js", "names": ["EventEmitter", "require", "parseSax", "Enums", "RelType", "HyperlinkReader", "constructor", "_ref", "workbook", "id", "iterator", "options", "count", "hyperlinks", "length", "each", "fn", "for<PERSON>ach", "read", "emitHyperlinks", "emit", "events", "eventType", "value", "node", "name", "rId", "attributes", "Id", "Type", "Hyperlink", "relationship", "type", "RelationshipType", "Styles", "target", "Target", "targetMode", "TargetMode", "error", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/hyperlink-reader.js"], "sourcesContent": ["const {EventEmitter} = require('events');\nconst parseSax = require('../../utils/parse-sax');\n\nconst Enums = require('../../doc/enums');\nconst RelType = require('../../xlsx/rel-type');\n\nclass HyperlinkReader extends EventEmitter {\n  constructor({workbook, id, iterator, options}) {\n    super();\n\n    this.workbook = workbook;\n    this.id = id;\n    this.iterator = iterator;\n    this.options = options;\n  }\n\n  get count() {\n    return (this.hyperlinks && this.hyperlinks.length) || 0;\n  }\n\n  each(fn) {\n    return this.hyperlinks.forEach(fn);\n  }\n\n  async read() {\n    const {iterator, options} = this;\n    let emitHyperlinks = false;\n    let hyperlinks = null;\n    switch (options.hyperlinks) {\n      case 'emit':\n        emitHyperlinks = true;\n        break;\n      case 'cache':\n        this.hyperlinks = hyperlinks = {};\n        break;\n      default:\n        break;\n    }\n\n    if (!emitHyperlinks && !hyperlinks) {\n      this.emit('finished');\n      return;\n    }\n\n    try {\n      for await (const events of parseSax(iterator)) {\n        for (const {eventType, value} of events) {\n          if (eventType === 'opentag') {\n            const node = value;\n            if (node.name === 'Relationship') {\n              const rId = node.attributes.Id;\n              switch (node.attributes.Type) {\n                case RelType.Hyperlink:\n                  {\n                    const relationship = {\n                      type: Enums.RelationshipType.Styles,\n                      rId,\n                      target: node.attributes.Target,\n                      targetMode: node.attributes.TargetMode,\n                    };\n                    if (emitHyperlinks) {\n                      this.emit('hyperlink', relationship);\n                    } else {\n                      hyperlinks[relationship.rId] = relationship;\n                    }\n                  }\n                  break;\n\n                default:\n                  break;\n              }\n            }\n          }\n        }\n      }\n      this.emit('finished');\n    } catch (error) {\n      this.emit('error', error);\n    }\n  }\n}\n\nmodule.exports = HyperlinkReader;\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAY,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;AACxC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEjD,MAAME,KAAK,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AACxC,MAAMG,OAAO,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAE9C,MAAMI,eAAe,SAASL,YAAY,CAAC;EACzCM,WAAWA,CAAAC,IAAA,EAAoC;IAAA,IAAnC;MAACC,QAAQ;MAAEC,EAAE;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAAJ,IAAA;IAC3C,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EAEA,IAAIC,KAAKA,CAAA,EAAG;IACV,OAAQ,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,MAAM,IAAK,CAAC;EACzD;EAEAC,IAAIA,CAACC,EAAE,EAAE;IACP,OAAO,IAAI,CAACH,UAAU,CAACI,OAAO,CAACD,EAAE,CAAC;EACpC;EAEA,MAAME,IAAIA,CAAA,EAAG;IACX,MAAM;MAACR,QAAQ;MAAEC;IAAO,CAAC,GAAG,IAAI;IAChC,IAAIQ,cAAc,GAAG,KAAK;IAC1B,IAAIN,UAAU,GAAG,IAAI;IACrB,QAAQF,OAAO,CAACE,UAAU;MACxB,KAAK,MAAM;QACTM,cAAc,GAAG,IAAI;QACrB;MACF,KAAK,OAAO;QACV,IAAI,CAACN,UAAU,GAAGA,UAAU,GAAG,CAAC,CAAC;QACjC;MACF;QACE;IACJ;IAEA,IAAI,CAACM,cAAc,IAAI,CAACN,UAAU,EAAE;MAClC,IAAI,CAACO,IAAI,CAAC,UAAU,CAAC;MACrB;IACF;IAEA,IAAI;MACF,WAAW,MAAMC,MAAM,IAAInB,QAAQ,CAACQ,QAAQ,CAAC,EAAE;QAC7C,KAAK,MAAM;UAACY,SAAS;UAAEC;QAAK,CAAC,IAAIF,MAAM,EAAE;UACvC,IAAIC,SAAS,KAAK,SAAS,EAAE;YAC3B,MAAME,IAAI,GAAGD,KAAK;YAClB,IAAIC,IAAI,CAACC,IAAI,KAAK,cAAc,EAAE;cAChC,MAAMC,GAAG,GAAGF,IAAI,CAACG,UAAU,CAACC,EAAE;cAC9B,QAAQJ,IAAI,CAACG,UAAU,CAACE,IAAI;gBAC1B,KAAKzB,OAAO,CAAC0B,SAAS;kBACpB;oBACE,MAAMC,YAAY,GAAG;sBACnBC,IAAI,EAAE7B,KAAK,CAAC8B,gBAAgB,CAACC,MAAM;sBACnCR,GAAG;sBACHS,MAAM,EAAEX,IAAI,CAACG,UAAU,CAACS,MAAM;sBAC9BC,UAAU,EAAEb,IAAI,CAACG,UAAU,CAACW;oBAC9B,CAAC;oBACD,IAAInB,cAAc,EAAE;sBAClB,IAAI,CAACC,IAAI,CAAC,WAAW,EAAEW,YAAY,CAAC;oBACtC,CAAC,MAAM;sBACLlB,UAAU,CAACkB,YAAY,CAACL,GAAG,CAAC,GAAGK,YAAY;oBAC7C;kBACF;kBACA;gBAEF;kBACE;cACJ;YACF;UACF;QACF;MACF;MACA,IAAI,CAACX,IAAI,CAAC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACd,IAAI,CAACnB,IAAI,CAAC,OAAO,EAAEmB,KAAK,CAAC;IAC3B;EACF;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGpC,eAAe"}