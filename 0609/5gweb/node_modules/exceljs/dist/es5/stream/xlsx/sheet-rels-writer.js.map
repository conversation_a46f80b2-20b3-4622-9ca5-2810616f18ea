{"version": 3, "file": "sheet-rels-writer.js", "names": ["utils", "require", "RelType", "HyperlinksProxy", "constructor", "sheetRelsWriter", "writer", "push", "hyperlink", "addHyperlink", "SheetRelsWriter", "options", "id", "count", "_hyperlinks", "_workbook", "workbook", "stream", "_stream", "_openStream", "length", "each", "fn", "for<PERSON>ach", "hyperlinksProxy", "_hyperlinksProxy", "relationship", "Target", "target", "Type", "Hyperlink", "TargetMode", "rId", "_writeRelationship", "address", "addMedia", "media", "addRelationship", "rel", "commit", "_writeClose", "end", "_writeOpen", "write", "xmlEncode", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/sheet-rels-writer.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst utils = require('../../utils/utils');\nconst RelType = require('../../xlsx/rel-type');\n\nclass HyperlinksProxy {\n  constructor(sheetRelsWriter) {\n    this.writer = sheetRelsWriter;\n  }\n\n  push(hyperlink) {\n    this.writer.addHyperlink(hyperlink);\n  }\n}\n\nclass SheetRelsWriter {\n  constructor(options) {\n    // in a workbook, each sheet will have a number\n    this.id = options.id;\n\n    // count of all relationships\n    this.count = 0;\n\n    // keep record of all hyperlinks\n    this._hyperlinks = [];\n\n    this._workbook = options.workbook;\n  }\n\n  get stream() {\n    if (!this._stream) {\n      // eslint-disable-next-line no-underscore-dangle\n      this._stream = this._workbook._openStream(`/xl/worksheets/_rels/sheet${this.id}.xml.rels`);\n    }\n    return this._stream;\n  }\n\n  get length() {\n    return this._hyperlinks.length;\n  }\n\n  each(fn) {\n    return this._hyperlinks.forEach(fn);\n  }\n\n  get hyperlinksProxy() {\n    return this._hyperlinksProxy || (this._hyperlinksProxy = new HyperlinksProxy(this));\n  }\n\n  addHyperlink(hyperlink) {\n    // Write to stream\n    const relationship = {\n      Target: hyperlink.target,\n      Type: RelType.Hyperlink,\n      TargetMode: 'External',\n    };\n    const rId = this._writeRelationship(relationship);\n\n    // store sheet stuff for later\n    this._hyperlinks.push({\n      rId,\n      address: hyperlink.address,\n    });\n  }\n\n  addMedia(media) {\n    return this._writeRelationship(media);\n  }\n\n  addRelationship(rel) {\n    return this._writeRelationship(rel);\n  }\n\n  commit() {\n    if (this.count) {\n      // write xml utro\n      this._writeClose();\n      // and close stream\n      this.stream.end();\n    }\n  }\n\n  // ================================================================================\n  _writeOpen() {\n    this.stream.write(\n      `<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n       <Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">`\n    );\n  }\n\n  _writeRelationship(relationship) {\n    if (!this.count) {\n      this._writeOpen();\n    }\n\n    const rId = `rId${++this.count}`;\n\n    if (relationship.TargetMode) {\n      this.stream.write(\n        `<Relationship Id=\"${rId}\"` +\n          ` Type=\"${relationship.Type}\"` +\n          ` Target=\"${utils.xmlEncode(relationship.Target)}\"` +\n          ` TargetMode=\"${relationship.TargetMode}\"` +\n          '/>'\n      );\n    } else {\n      this.stream.write(\n        `<Relationship Id=\"${rId}\" Type=\"${relationship.Type}\" Target=\"${relationship.Target}\"/>`\n      );\n    }\n\n    return rId;\n  }\n\n  _writeClose() {\n    this.stream.write('</Relationships>');\n  }\n}\n\nmodule.exports = SheetRelsWriter;\n"], "mappings": ";;AAAA;AACA,MAAMA,KAAK,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC1C,MAAMC,OAAO,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE9C,MAAME,eAAe,CAAC;EACpBC,WAAWA,CAACC,eAAe,EAAE;IAC3B,IAAI,CAACC,MAAM,GAAGD,eAAe;EAC/B;EAEAE,IAAIA,CAACC,SAAS,EAAE;IACd,IAAI,CAACF,MAAM,CAACG,YAAY,CAACD,SAAS,CAAC;EACrC;AACF;AAEA,MAAME,eAAe,CAAC;EACpBN,WAAWA,CAACO,OAAO,EAAE;IACnB;IACA,IAAI,CAACC,EAAE,GAAGD,OAAO,CAACC,EAAE;;IAEpB;IACA,IAAI,CAACC,KAAK,GAAG,CAAC;;IAEd;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB,IAAI,CAACC,SAAS,GAAGJ,OAAO,CAACK,QAAQ;EACnC;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACjB;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACH,SAAS,CAACI,WAAW,CAAE,6BAA4B,IAAI,CAACP,EAAG,WAAU,CAAC;IAC5F;IACA,OAAO,IAAI,CAACM,OAAO;EACrB;EAEA,IAAIE,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,WAAW,CAACM,MAAM;EAChC;EAEAC,IAAIA,CAACC,EAAE,EAAE;IACP,OAAO,IAAI,CAACR,WAAW,CAACS,OAAO,CAACD,EAAE,CAAC;EACrC;EAEA,IAAIE,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG,IAAItB,eAAe,CAAC,IAAI,CAAC,CAAC;EACrF;EAEAM,YAAYA,CAACD,SAAS,EAAE;IACtB;IACA,MAAMkB,YAAY,GAAG;MACnBC,MAAM,EAAEnB,SAAS,CAACoB,MAAM;MACxBC,IAAI,EAAE3B,OAAO,CAAC4B,SAAS;MACvBC,UAAU,EAAE;IACd,CAAC;IACD,MAAMC,GAAG,GAAG,IAAI,CAACC,kBAAkB,CAACP,YAAY,CAAC;;IAEjD;IACA,IAAI,CAACZ,WAAW,CAACP,IAAI,CAAC;MACpByB,GAAG;MACHE,OAAO,EAAE1B,SAAS,CAAC0B;IACrB,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAACC,KAAK,EAAE;IACd,OAAO,IAAI,CAACH,kBAAkB,CAACG,KAAK,CAAC;EACvC;EAEAC,eAAeA,CAACC,GAAG,EAAE;IACnB,OAAO,IAAI,CAACL,kBAAkB,CAACK,GAAG,CAAC;EACrC;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC1B,KAAK,EAAE;MACd;MACA,IAAI,CAAC2B,WAAW,CAAC,CAAC;MAClB;MACA,IAAI,CAACvB,MAAM,CAACwB,GAAG,CAAC,CAAC;IACnB;EACF;;EAEA;EACAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACzB,MAAM,CAAC0B,KAAK,CACd;AACP,4FACI,CAAC;EACH;EAEAV,kBAAkBA,CAACP,YAAY,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE;MACf,IAAI,CAAC6B,UAAU,CAAC,CAAC;IACnB;IAEA,MAAMV,GAAG,GAAI,MAAK,EAAE,IAAI,CAACnB,KAAM,EAAC;IAEhC,IAAIa,YAAY,CAACK,UAAU,EAAE;MAC3B,IAAI,CAACd,MAAM,CAAC0B,KAAK,CACd,qBAAoBX,GAAI,GAAE,GACxB,UAASN,YAAY,CAACG,IAAK,GAAE,GAC7B,YAAW7B,KAAK,CAAC4C,SAAS,CAAClB,YAAY,CAACC,MAAM,CAAE,GAAE,GAClD,gBAAeD,YAAY,CAACK,UAAW,GAAE,GAC1C,IACJ,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACd,MAAM,CAAC0B,KAAK,CACd,qBAAoBX,GAAI,WAAUN,YAAY,CAACG,IAAK,aAAYH,YAAY,CAACC,MAAO,KACvF,CAAC;IACH;IAEA,OAAOK,GAAG;EACZ;EAEAQ,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACvB,MAAM,CAAC0B,KAAK,CAAC,kBAAkB,CAAC;EACvC;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGpC,eAAe"}