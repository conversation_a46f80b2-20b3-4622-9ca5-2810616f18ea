{"name": "@types/js-cookie", "version": "3.0.6", "description": "TypeScript definitions for js-cookie", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/js-cookie", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/theodorejb"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "alepee", "url": "https://github.com/alepee"}, {"name": "<PERSON><PERSON>", "githubUsername": "yutod", "url": "https://github.com/yutod"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nreynis"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "require": "./index.d.ts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/js-cookie"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "d8a8b39fde98e89be2790dfbecea0ff8e70cd2773a5149120bae93ccf439920c", "typeScriptVersion": "4.5"}