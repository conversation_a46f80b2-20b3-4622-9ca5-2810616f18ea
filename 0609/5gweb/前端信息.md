Thought for 1 second好的，这是当前项目的前端 API 和目录结构（中文版），希望能帮助你进行后端服务对接。

## 前端 API

以下是基于现有代码的前端 API 摘要，这些是客户端函数，用于调用你的后端 API 路由。

```typescript
// lib/api/index.ts (此文件概述了结构，但使用了模拟数据)

// 身份验证
login(username: string, password: string): Promise<ApiResponse<{ token: string; user: User }>>
logout(): Promise<ApiResponse<null>>
getCurrentUser(): Promise<ApiResponse<User>>
resetPassword(email: string): Promise<ApiResponse<null>>

// 客户账户
getCustomers(params: { page: number; pageSize: number; search?: string; status?: string }): Promise<ApiResponse<PaginatedResponse<Customer>>>
getCustomerById(id: string): Promise<ApiResponse<Customer>>
createCustomer(data: Partial<Customer>): Promise<ApiResponse<Customer>>
updateCustomer(id: string, data: Partial<Customer>): Promise<ApiResponse<Customer>>
disableCustomer(id: string, reason: string): Promise<ApiResponse<null>>
rechargeCustomer(id: string, amount: number, paymentMethod: string, remarks?: string): Promise<ApiResponse<{ balance: number }>>
getCustomerRechargeHistory(id: string, params: { page: number; pageSize: number; startDate?: string; endDate?: string; type?: string }): Promise<ApiResponse<PaginatedResponse<RechargeRecord>>>

// 管理员账户
getAdmins(params: { page: number; pageSize: number; search?: string; role?: string }): Promise<ApiResponse<PaginatedResponse<Admin>>>
getAdminById(id: string): Promise<ApiResponse<Admin>>
createAdmin(data: Partial<Admin>): Promise<ApiResponse<Admin>>
updateAdmin(id: string, data: Partial<Admin>): Promise<ApiResponse<Admin>>

// 任务
getTasks(params: { page: number; pageSize: number; search?: string; status?: string; type?: string; startDate?: string; endDate?: string }): Promise<ApiResponse<PaginatedResponse<Task>>>
getTaskById(id: string): Promise<ApiResponse<Task>>
createVideoCallTask(data: { name: string; content: string; callType: string; startTime: string; resource: string; phoneNumber: string; smsType: string; smsTemplate: string }): Promise<ApiResponse<VideoCallTask>>
updateTaskStatus(id: string, status: string): Promise<ApiResponse<null>>
deleteTask(id: string): Promise<ApiResponse<null>>
importTasks(file: File, options?: { type?: string; resource?: string }): Promise<ApiResponse<{ imported: number; failed: number }>>
exportTasks(format: "csv" | "excel" | "pdf", filters?: any): Promise<Blob>>
downloadTaskTemplate(type?: string): Promise<Blob>>

// 用户设置
updateUserProfile(data: Partial<User>): Promise<ApiResponse<User>>
updateUserPassword(oldPassword: string, newPassword: string): Promise<ApiResponse<null>>
getUserLoginHistory(params: { page: number; pageSize: number; startDate?: string; endDate?: string; status?: string }): Promise<ApiResponse<PaginatedResponse<LoginHistory>>>
exportLoginHistory(format: "csv" | "excel" | "pdf", filters?: any): Promise<Blob>>

// 通知
getNotifications(params: { page: number; pageSize: number; read?: boolean; type?: string; startDate?: string; endDate?: string }): Promise<ApiResponse<PaginatedResponse<Notification>>>
getNotificationById(id: string): Promise<ApiResponse<Notification>>
markNotificationAsRead(id: string): Promise<ApiResponse<null>>
markAllNotificationsAsRead(): Promise<ApiResponse<{ count: number }>>
createNotification(data: { title: string; content: string; type: string; recipients?: string[] }): Promise<ApiResponse<Notification>>>
```

## 项目目录结构

以下是 Next.js 项目的目录结构，以及每个文件的说明：

```plaintext
app/
├── (auth)/
│   └── layout.tsx             # 身份验证页面的布局（登录、注册）
├── (dashboard)/
│   ├── accounts/
│   │   ├── admin/
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx    # 管理员账户详情页面
│   │   │   └── page.tsx        # 管理员账户列表页面
│   │   └── customer/
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx    # 客户账户详情页面
│   │   │   └── page.tsx        # 客户账户列表页面
│   │   └── page.tsx            # 账户页面（未完全实现）
│   ├── dashboard/
│   │   └── page.tsx            # 主仪表盘页面
│   ├── layout.tsx               # 仪表盘页面的布局
│   ├── notification-center/
│   │   ├── detail/
│   │   │   └── page.tsx        # 通知详情页面
│   │   ├── page.tsx            # 通知中心页面
│   │   └── publish/
│   │       └── page.tsx        # 发布通知页面
│   ├── settings/
│   │   ├── menu/
│   │   │   └── layout.tsx      # 菜单设置的布局
│   │   │   └── page.tsx        # 菜单设置页面
│   │   ├── page.tsx            # 设置页面
│   │   └── performance/
│   │       └── page.tsx        # 性能设置页面
│   ├── tasks/
│   │   ├── details/
│   │   │   └── page.tsx        # 任务详情页面
│   │   └── page.tsx            # 任务列表页面
│   └── user/
│       └── settings/
│           └── page.tsx        # 用户设置页面
├── api/
│   ├── auth/
│   │   └── login/
│   │       └── route.ts        # 登录 API 路由
│   ├── tasks/
│   │   └── [id]/
│   │       └── route.ts        # 任务 API 路由
│   └── route.ts                # API 路由
├── components/
│   ├── dashboard-header.tsx    # 仪表盘头部组件
│   ├── dashboard-shell.tsx     # 仪表盘外壳组件
│   ├── overview.tsx            # 概览图表组件
│   ├── recent-activity.tsx     # 最近活动组件
│   ├── theme-switcher.tsx      # 主题切换器组件
│   └── ui/                    # shadcn/ui 组件
├── contexts/
│   └── auth-context.tsx       # 身份验证上下文
├── lib/
│   ├── api.ts                  # API 函数
│   └── utils.ts                # 实用函数
├── public/
│   └── logo.svg               # Logo 图片
├── app/globals.css            # 全局 CSS 文件
├── next.config.mjs            # Next.js 配置文件
├── tailwind.config.ts         # Tailwind CSS 配置文件
└── tsconfig.json              # TypeScript 配置文件
```

## 文件说明

- `app/layout.tsx`: 整个应用程序的根布局。
- `app/(auth)/layout.tsx`: 身份验证页面（登录、注册）的布局。
- `app/(dashboard)/layout.tsx`: 仪表盘页面的布局。
- `app/(dashboard)/accounts/admin/[id]/page.tsx`: 管理员账户详情页面。
- `app/(dashboard)/accounts/admin/page.tsx`: 管理员账户列表页面。
- `app/(dashboard)/accounts/customer/[id]/page.tsx`: 客户账户详情页面。
- `app/(dashboard)/accounts/customer/page.tsx`: 客户账户列表页面。
- `app/(dashboard)/dashboard/page.tsx`: 主仪表盘页面。
- `app/(dashboard)/notification-center/page.tsx`: 通知中心页面。
- `app/(dashboard)/notification-center/publish/page.tsx`: 发布通知页面。
- `app/(dashboard)/settings/page.tsx`: 设置页面。
- `app/(dashboard)/settings/menu/layout.tsx`: 菜单设置的布局。
- `app/(dashboard)/settings/menu/page.tsx`: 菜单设置页面。
- `app/(dashboard)/settings/performance/page.tsx`: 性能设置页面。
- `app/(dashboard)/tasks/page.tsx`: 任务列表页面。
- `app/(dashboard)/tasks/details/page.tsx`: 任务详情页面。
- `components/dashboard-header.tsx`: 仪表盘头部组件。
- `components/dashboard-shell.tsx`: 仪表盘外壳组件。
- `components/overview.tsx`: 概览图表组件。
- `components/recent-activity.tsx`: 最近活动组件。
- `components/theme-switcher.tsx`: 主题切换器组件。
- `components/ui/*`: shadcn/ui 组件。
- `contexts/auth-context.tsx`: 身份验证上下文。
- `lib/api.ts`: API 函数。
- `lib/utils.ts`: 实用函数。
- `public/logo.svg`: Logo 图片。
- `app/globals.css`: 全局 CSS 文件。
- `next.config.mjs`: Next.js 配置文件。
- `tailwind.config.ts`: Tailwind CSS 配置文件。
- `tsconfig.json`: TypeScript 配置文件。