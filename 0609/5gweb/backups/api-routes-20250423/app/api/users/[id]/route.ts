import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createAbacMiddleware } from '@/lib/abac/middleware'
import { hash } from 'bcryptjs'

// 创建用户管理的访问控制中间件
const withUserAccess = createAbacMiddleware('users', 'manage')

/**
 * 获取单个用户详情
 * @route GET /api/users/[id]
 */
export const GET = withUserAccess(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: '用户不存在'
        },
        { status: 404 }
      )
    }

    // 处理数据返回格式，移除敏感信息
    const { password, ...userData } = user

    // 由于我们移除了permissions关联，这里使用空数组
    const formattedPermissions: any[] = []

    return NextResponse.json({
      success: true,
      data: {
        ...userData,
        permissions: formattedPermissions
      }
    })
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取用户详情失败'
      },
      { status: 500 }
    )
  }
})

/**
 * 更新用户
 * @route PUT /api/users/[id]
 */
export const PUT = withUserAccess(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const data = await request.json()

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json(
        {
          success: false,
          message: '用户不存在'
        },
        { status: 404 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      status: data.status,
      updatedAt: new Date()
    }

    // 如果提供了新密码，则更新密码
    if (data.password) {
      updateData.password = await hash(data.password, 10)
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData
    })

    // 如果提供了角色，则更新用户角色
    if (data.roleIds && data.roleIds.length > 0) {
      // 直接更新用户的角色
      await prisma.user.update({
        where: { id: params.id },
        data: {
          role: { connect: { id: data.roleIds[0] } } // 只使用第一个角色ID
        }
      })
    }

    // 移除敏感信息
    const { password, ...userData } = user

    return NextResponse.json({
      success: true,
      data: userData
    })
  } catch (error) {
    console.error('更新用户失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新用户失败'
      },
      { status: 500 }
    )
  }
})

/**
 * 删除用户
 * @route DELETE /api/users/[id]
 */
export const DELETE = withUserAccess(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: '用户不存在'
        },
        { status: 404 }
      )
    }

    // 删除用户
    await prisma.user.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: '用户删除成功'
    })
  } catch (error) {
    console.error('删除用户失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除用户失败'
      },
      { status: 500 }
    )
  }
})