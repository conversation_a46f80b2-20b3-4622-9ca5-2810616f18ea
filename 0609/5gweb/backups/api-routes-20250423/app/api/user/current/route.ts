import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import * as jose from 'jose';
import { prisma } from "@/lib/prisma";

// 使用与登录路由相同的密钥
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key");

/**
 * 获取当前登录用户信息
 * 
 * @route GET /api/user/current
 * @access private - 需要认证
 */
export async function GET(req: NextRequest) {
  try {
    // 从 cookie 中获取 token
    const cookieStore = cookies();
    const token = cookieStore.get("token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "未授权访问", code: 401, data: null },
        { status: 401 }
      );
    }

    // 验证 token
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET);
      console.log("解码后的 token 数据:", payload);
      
      const userId = payload.userId as string;
      
      if (!userId) {
        return NextResponse.json(
          { success: false, message: "无效的令牌", code: 401, data: null },
          { status: 401 }
        );
      }

      // 查询用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          role: true
        }
      });

      if (!user) {
        return NextResponse.json(
          { success: false, message: "用户不存在", code: 404, data: null },
          { status: 404 }
        );
      }

      // 返回用户信息（排除敏感字段）
      const userData = {
        id: user.id,
        username: user.username,
        email: user.email,
        roleCode: user.roleCode,
        permissions: user.permissions,
        role: {
          id: user.role.id,
          code: user.role.code,
          name: user.role.name,
          type: user.role.type,
          permissions: user.role.permissions
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };

      console.log("查询到的用户信息:", userData);

      return NextResponse.json({
        success: true,
        message: "获取用户信息成功",
        code: 200,
        data: userData
      });
    } catch (error) {
      console.error("Token验证失败:", error);
      return NextResponse.json(
        { success: false, message: "Token验证失败", code: 401, data: null },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return NextResponse.json(
      { success: false, message: "获取用户信息失败", code: 500, data: null },
      { status: 500 }
    );
  }
} 