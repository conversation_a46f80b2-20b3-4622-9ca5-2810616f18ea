/**
 * 日志轮转定时任务API
 * 提供日志轮转和清理功能
 * 
 * 此API可以通过定时任务调用，例如每天凌晨执行一次
 * 也可以通过管理界面手动触发
 */

import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-service"
import { AuthLogService } from "@/lib/auth-log-service"
import { SystemLogService } from "@/lib/system-log-service"
import { checkPermission } from "@/lib/middleware/check-permission"

// 日志轮转API
export async function POST(request: NextRequest) {
  try {
    // 验证权限
    const user = await AuthService.validateToken(request)
    if (!user) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(user, "logs", "manage")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限执行此操作" }, { status: 403 })
    }

    // 获取请求参数
    const body = await request.json()
    const { type = 'rotate', days } = body

    let result = 0
    let message = ''

    // 根据类型执行不同的操作
    if (type === 'rotate') {
      // 执行日志轮转
      result = await AuthLogService.rotate()
      message = `日志轮转完成，共清理 ${result} 条日志`
    } else if (type === 'cleanup') {
      // 执行日志清理
      const retentionDays = days || 30
      result = await AuthLogService.cleanup(retentionDays)
      message = `日志清理完成，共清理 ${result} 条过期日志（${retentionDays}天前）`
    } else {
      return NextResponse.json({ success: false, message: "不支持的操作类型" }, { status: 400 })
    }

    // 记录操作日志
    await SystemLogService.log({
      userId: user.id,
      action: type === 'rotate' ? 'log_rotate' : 'log_cleanup',
      module: 'logs',
      details: {
        type,
        count: result,
        days: days || 'default'
      },
      status: 'success'
    })

    return NextResponse.json({
      success: true,
      message,
      data: {
        count: result
      }
    })
  } catch (error) {
    console.error("日志轮转/清理失败:", error)
    return NextResponse.json({ success: false, message: "日志轮转/清理失败" }, { status: 500 })
  }
}

// 获取日志统计信息
export async function GET(request: NextRequest) {
  try {
    // 验证权限
    const user = await AuthService.validateToken(request)
    if (!user) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(user, "logs", "view")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限访问" }, { status: 403 })
    }

    // 获取认证日志统计信息
    const authLogStats = await getAuthLogStats()

    return NextResponse.json({
      success: true,
      data: {
        authLogs: authLogStats
      }
    })
  } catch (error) {
    console.error("获取日志统计信息失败:", error)
    return NextResponse.json({ success: false, message: "获取日志统计信息失败" }, { status: 500 })
  }
}

// 获取认证日志统计信息
async function getAuthLogStats() {
  const prisma = (await import("@/lib/prisma")).default

  // 获取总数
  const total = await prisma.system_log.count({
    where: { module: 'auth' }
  })

  // 获取最近30天的数量
  const date30DaysAgo = new Date()
  date30DaysAgo.setDate(date30DaysAgo.getDate() - 30)
  const last30Days = await prisma.system_log.count({
    where: {
      module: 'auth',
      createdAt: {
        gte: date30DaysAgo
      }
    }
  })

  // 获取最近7天的数量
  const date7DaysAgo = new Date()
  date7DaysAgo.setDate(date7DaysAgo.getDate() - 7)
  const last7Days = await prisma.system_log.count({
    where: {
      module: 'auth',
      createdAt: {
        gte: date7DaysAgo
      }
    }
  })

  // 获取最近24小时的数量
  const date24HoursAgo = new Date()
  date24HoursAgo.setHours(date24HoursAgo.getHours() - 24)
  const last24Hours = await prisma.system_log.count({
    where: {
      module: 'auth',
      createdAt: {
        gte: date24HoursAgo
      }
    }
  })

  // 获取各状态的数量
  const successCount = await prisma.system_log.count({
    where: {
      module: 'auth',
      status: 'success'
    }
  })

  const errorCount = await prisma.system_log.count({
    where: {
      module: 'auth',
      status: 'error'
    }
  })

  const warningCount = await prisma.system_log.count({
    where: {
      module: 'auth',
      status: 'warning'
    }
  })

  const infoCount = await prisma.system_log.count({
    where: {
      module: 'auth',
      status: 'info'
    }
  })

  // 获取最早和最新的日志时间
  const oldestLog = await prisma.system_log.findFirst({
    where: { module: 'auth' },
    orderBy: { createdAt: 'asc' },
    select: { createdAt: true }
  })

  const newestLog = await prisma.system_log.findFirst({
    where: { module: 'auth' },
    orderBy: { createdAt: 'desc' },
    select: { createdAt: true }
  })

  return {
    total,
    last30Days,
    last7Days,
    last24Hours,
    byStatus: {
      success: successCount,
      error: errorCount,
      warning: warningCount,
      info: infoCount
    },
    oldestLogDate: oldestLog?.createdAt || null,
    newestLogDate: newestLog?.createdAt || null
  }
}
