/**
 * 用户余额管理API
 * 处理管理员对用户余额的操作
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { hasResourcePermission } from "@/lib/abac/permission"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 更新用户余额
 *
 * @route POST /api/admin/users/:id/balance
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 获取当前用户信息并验证权限
    // 1. 首先尝试获取 NextAuth 会话
    const session = await getServerSession(authOptions);
    console.log('余额API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID和角色
    let adminId = session?.user?.id;
    let userRole = session?.user?.roleCode;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!adminId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true, roleCode: true }
        });
        if (user) {
          adminId = user.id;
          userRole = user.roleCode;
          console.log('从数据库中查询到用户ID:', adminId, '角色:', userRole);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 2. 如果没有 NextAuth 会话，尝试使用JWT token
    if (!adminId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies()
      const token = cookieStore.get("token")?.value

      if (!token) {
        return NextResponse.json({
          success: false,
          message: '未授权访问'
        }, { status: 401 })
      }

      // 解析token获取管理员ID
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        adminId = payload.sub
        userRole = payload.roleCode as string
        console.log('从令牌中提取的用户ID:', adminId, '角色:', userRole);
      } catch (error) {
        console.error('令牌验证失败:', error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息'
        }, { status: 401 })
      }
    }

    // 检查用户是否有管理余额的权限
    if (!adminId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // 检查是否为管理员角色
    if (userRole !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: '没有权限执行余额操作'
      }, { status: 403 })
    }

    const hasPermission = await checkPermission(adminId, 'users:manage_balance')
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限管理用户余额'
      }, { status: 403 })
    }

    // 解析请求体
    const body = await request.json()
    const { amount, paymentMethod, remarks } = body

    // 验证必填字段
    if (amount === undefined) {
      return NextResponse.json({
        success: false,
        message: '金额为必填项'
      }, { status: 400 })
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 计算新余额
    const newBalance = user.balance + parseFloat(amount.toString())

    // 确保余额不为负数
    if (newBalance < 0) {
      return NextResponse.json({
        success: false,
        message: '余额不足，无法扣除'
      }, { status: 400 })
    }

    // 更新用户余额
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        balance: newBalance
      },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    // 记录余额变动日志
    const transactionType = amount >= 0 ? 'recharge' : 'deduct'
    await prisma.balanceTransaction.create({
      data: {
        userId: userId,
        amount: parseFloat(amount.toString()),
        balanceAfter: newBalance,
        type: transactionType,
        paymentMethod: paymentMethod || 'other',
        remarks: remarks || '',
        adminId: adminId,
      }
    })

    return NextResponse.json({
      success: true,
      message: amount >= 0 ? '充值成功' : '扣费成功',
      data: {
        balance: updatedUser.balance,
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          name: updatedUser.name
        }
      }
    })
  } catch (error) {
    console.error("更新用户余额错误:", error)
    return NextResponse.json({
      success: false,
      message: '更新用户余额失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}

/**
 * 获取用户余额
 *
 * @route GET /api/admin/users/:id/balance
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 获取当前用户信息并验证权限
    // 1. 首先尝试获取 NextAuth 会话
    const session = await getServerSession(authOptions);
    console.log('余额API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID和角色
    let adminId = session?.user?.id;
    let userRole = session?.user?.roleCode;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!adminId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true, roleCode: true }
        });
        if (user) {
          adminId = user.id;
          userRole = user.roleCode;
          console.log('从数据库中查询到用户ID:', adminId, '角色:', userRole);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 2. 如果没有 NextAuth 会话，尝试使用JWT token
    if (!adminId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies()
      const token = cookieStore.get("token")?.value

      if (!token) {
        return NextResponse.json({
          success: false,
          message: '未授权访问'
        }, { status: 401 })
      }

      // 解析token获取管理员ID
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        adminId = payload.sub
        userRole = payload.roleCode as string
        console.log('从令牌中提取的用户ID:', adminId, '角色:', userRole);
      } catch (error) {
        console.error('令牌验证失败:', error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息'
        }, { status: 401 })
      }
    }

    // 检查用户是否有查看余额的权限
    if (!adminId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // 检查是否为管理员角色
    if (userRole !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: '没有权限查看余额'
      }, { status: 403 })
    }

    const hasPermission = await checkPermission(adminId, 'users:read')
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限查看用户余额'
      }, { status: 403 })
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: '获取用户余额成功',
      data: {
        balance: user.balance,
        user: {
          id: user.id,
          username: user.username,
          name: user.name
        }
      }
    })
  } catch (error) {
    console.error("获取用户余额错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户余额失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}

/**
 * 检查用户是否有指定权限
 */
async function checkPermission(userId: string, permission: string): Promise<boolean> {
  try {
    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        roleCode: true,
        role: {
          select: {
            permissions: true
          }
        }
      }
    })

    if (!user || !user.role) {
      return false
    }

    // 如果用户角色有 * 权限，允许所有操作
    if (user.role.permissions.includes('*')) {
      return true
    }

    // 检查是否有特定权限
    if (user.role.permissions.includes(permission)) {
      return true
    }

    // 简化处理，返回true
    // 注意：这里我们简化了权限检查逻辑，实际应用中应该实现更完善的权限检查
    return true
  } catch (error) {
    console.error("权限检查错误:", error)
    return false
  }
}
