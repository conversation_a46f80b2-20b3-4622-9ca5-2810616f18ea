import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth'
import { hasPermission } from '@/lib/permission'

/**
 * 管理员更改用户认证类型
 * @route POST /api/admin/user/verification
 */
export async function POST(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: decoded.userId as string },
      include: { role: true }
    })

    if (!admin) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查管理员权限
    const hasAccess = admin.role?.code === 'super' || admin.role?.code === 'admin'
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有权限执行此操作', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取请求数据
    const data = await req.json()
    const { userId, newType, reason } = data

    if (!userId || !newType || !reason) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数', error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        verification: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查新类型是否与当前类型相同
    if (user.verificationType === newType) {
      return NextResponse.json(
        { success: false, message: '新认证类型与当前类型相同', error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    // 计算截止日期（3天后）
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + 3)

    // 记录原始认证状态和类型
    const originalType = user.verificationType
    const originalStatus = user.verificationStatus

    // 更新用户认证类型
    await prisma.user.update({
      where: { id: userId },
      data: {
        verificationType: newType,
        verificationStatus: 'pending'
      }
    })

    // 创建或更新认证类型变更记录
    await prisma.verificationTypeChange.upsert({
      where: { userId },
      update: {
        originalType,
        originalStatus,
        newType,
        reason,
        adminId: admin.id,
        deadline,
        completed: false
      },
      create: {
        userId,
        originalType,
        originalStatus,
        newType,
        reason,
        adminId: admin.id,
        deadline,
        completed: false
      }
    })

    // 如果用户有现有的认证资料，则删除
    if (user.verification) {
      await prisma.userVerification.delete({
        where: { userId }
      })
    }

    // 创建系统通知
    // 注意：由于没有notification模型，我们这里暂时跳过通知创建
    // TODO: 实现系统通知功能

    return NextResponse.json(
      {
        success: true,
        message: '认证类型变更成功',
        data: {
          userId,
          newType,
          deadline
        }
      },
      { status: 200, headers }
    )
  } catch (error: any) {
    console.error('管理员更改用户认证类型错误:', error)
    return NextResponse.json(
      { success: false, message: '服务器错误', error: error.message },
      { status: 500, headers }
    )
  }
}

/**
 * 获取认证类型变更记录
 * @route GET /api/admin/user/verification?userId=xxx
 */
export async function GET(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: decoded.userId as string },
      include: { role: true }
    })

    if (!admin) {
      return NextResponse.json(
        { success: false, message: '用户不存在', error: 'NotFound' },
        { status: 404, headers }
      )
    }

    // 检查管理员权限
    const hasAccess = admin.role?.code === 'super' || admin.role?.code === 'admin'
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有权限执行此操作', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取查询参数
    const url = new URL(req.url)
    const userId = url.searchParams.get('userId')

    // 如果提供了userId，则获取特定用户的记录
    if (userId) {
      const change = await prisma.verificationTypeChange.findUnique({
        where: { userId },
        include: {
          admin: {
            select: {
              id: true,
              username: true,
              name: true
            }
          },
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              verificationType: true,
              verificationStatus: true
            }
          }
        }
      })

      if (!change) {
        return NextResponse.json(
          { success: false, message: '未找到认证类型变更记录', error: 'NotFound' },
          { status: 404, headers }
        )
      }

      return NextResponse.json(
        { success: true, data: change },
        { status: 200, headers }
      )
    }

    // 否则获取所有记录
    const changes = await prisma.verificationTypeChange.findMany({
      include: {
        admin: {
          select: {
            id: true,
            username: true,
            name: true
          }
        },
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            verificationType: true,
            verificationStatus: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(
      { success: true, data: changes },
      { status: 200, headers }
    )
  } catch (error: any) {
    console.error('获取认证类型变更记录错误:', error)
    return NextResponse.json(
      { success: false, message: '服务器错误', error: error.message },
      { status: 500, headers }
    )
  }
}
