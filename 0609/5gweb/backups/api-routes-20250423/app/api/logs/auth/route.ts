/**
 * 认证日志 API 路由
 * 提供认证日志的查询功能
 */

import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-service"
import { AuthLogService } from "@/lib/auth-log-service"
import { SystemLogService } from "@/lib/system-log-service"
import { checkPermission } from "@/lib/middleware/check-permission"

// 获取认证日志列表
export async function GET(request: NextRequest) {
  try {
    // 验证权限
    const user = await AuthService.validateToken(request)
    if (!user) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(user, "logs", "view")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限访问" }, { status: 403 })
    }

    // 记录日志
    await SystemLogService.log({
      userId: user.id,
      action: "view",
      module: "logs",
      details: { message: "查看认证日志" },
    })

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get("page") || "1")
    const pageSize = parseInt(searchParams.get("pageSize") || "10")
    const userId = searchParams.get("userId") || undefined
    const action = searchParams.get("action") || undefined
    const status = searchParams.get("status") || undefined
    const startDateStr = searchParams.get("startDate")
    const endDateStr = searchParams.get("endDate")

    // 转换日期
    const startDate = startDateStr ? new Date(startDateStr) : undefined
    const endDate = endDateStr ? new Date(endDateStr) : undefined

    // 查询日志
    const result = await AuthLogService.query({
      userId,
      action,
      status,
      startDate,
      endDate,
      page,
      pageSize
    })

    return NextResponse.json({
      success: true,
      data: {
        logs: result.logs,
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: result.totalPages
      }
    })
  } catch (error) {
    console.error("获取认证日志失败:", error)
    return NextResponse.json({ 
      success: false, 
      message: "获取认证日志失败",
      error: error instanceof Error ? error.message : "未知错误"
    }, { status: 500 })
  }
}
