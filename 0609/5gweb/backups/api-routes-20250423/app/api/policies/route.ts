import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { PolicyService } from '@/lib/abac/policy-service'
import { createAbacMiddleware } from '@/lib/abac/middleware'
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { Policy } from "@/lib/abac/types"

// 创建策略访问控制中间件
const withPolicyAccess = createAbacMiddleware('policy', 'manage')

/**
 * 获取策略列表
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const policies = await prisma.policy.findMany({
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    })

    return NextResponse.json(policies)
  } catch (error) {
    console.error("获取策略列表错误:", error)
    return NextResponse.json(
      { error: "获取策略列表失败" },
      { status: 500 }
    )
  }
}

/**
 * 创建策略
 */
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const data = await request.json()
    const { resourceCode, operationCode, conditions } = data

    // 验证资源代码
    const resource = await prisma.resource.findUnique({
      where: { code: resourceCode },
    })

    if (!resource) {
      return NextResponse.json(
        { error: "资源不存在" },
        { status: 400 }
      )
    }

    // 验证操作代码
    const operation = await prisma.operation.findUnique({
      where: { code: operationCode },
    })

    if (!operation) {
      return NextResponse.json(
        { error: "操作不存在" },
        { status: 400 }
      )
    }

    // 创建策略
    const policy = await prisma.policy.create({
      data: {
        resourceCode,
        operationCode,
        conditions: {
          create: conditions.map((condition: any) => ({
            attribute: condition.attribute,
            operator: condition.operator,
            value: condition.value,
            description: condition.description,
          })),
        },
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    })

    return NextResponse.json(policy)
  } catch (error) {
    console.error("创建策略错误:", error)
    return NextResponse.json(
      { error: "创建策略失败" },
      { status: 500 }
    )
  }
}

/**
 * 更新策略
 */
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const data = await request.json()
    const { id, resourceCode, operationCode, conditions } = data

    // 验证策略是否存在
    const existingPolicy = await prisma.policy.findUnique({
      where: { id },
    })

    if (!existingPolicy) {
      return NextResponse.json(
        { error: "策略不存在" },
        { status: 404 }
      )
    }

    // 更新策略
    const policy = await prisma.policy.update({
      where: { id },
      data: {
        resourceCode,
        operationCode,
        conditions: {
          deleteMany: {},
          create: conditions.map((condition: any) => ({
            attribute: condition.attribute,
            operator: condition.operator,
            value: condition.value,
            description: condition.description,
          })),
        },
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    })

    return NextResponse.json({ success: true, data: policy })
  } catch (error) {
    console.error("更新策略错误:", error)
    return NextResponse.json(
      { error: "更新策略失败" },
      { status: 500 }
    )
  }
}