import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { PolicyService } from '@/lib/abac/policy-service'
import { createAbacMiddleware } from '@/lib/abac/middleware'

// 创建策略访问控制中间件
const withPolicyAccess = createAbacMiddleware('policy', 'manage')

/**
 * 启用/禁用策略
 * @route PATCH /api/policies/[id]/status
 */
export const PATCH = withPolicyAccess(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const { enabled } = await request.json()
    const policyService = new PolicyService()
    
    const policy = enabled
      ? await policyService.enablePolicy(params.id)
      : await policyService.disablePolicy(params.id)

    return NextResponse.json({
      success: true,
      data: policy
    })
  } catch (error) {
    console.error('更新策略状态失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新策略状态失败'
      },
      { status: 500 }
    )
  }
}) 