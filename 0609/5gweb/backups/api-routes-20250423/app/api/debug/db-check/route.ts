/**
 * 数据库检查API
 * 直接检查数据库中的角色和权限配置，无需用户登录
 * 仅在开发环境可用
 */

import { NextRequest } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    // 安全检查 - 只在非生产环境允许访问
    if (process.env.NODE_ENV === 'production') {
      return Response.json({
        success: false,
        message: "此接口在生产环境不可用"
      }, { status: 403 })
    }

    // 1. 获取所有角色
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        code: true,
        name: true,
        type: true,
        permissions: true,
        createdAt: true,
        updatedAt: true
      }
    })

    // 2. 获取 ADMIN 角色详情
    const adminRole = roles.find(r => r.code === 'ADMIN') || 
                      roles.find(r => r.code.toUpperCase() === 'ADMIN');

    // 3. 获取所有用户
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        roleCode: true,
        permissions: true
      },
      where: {
        OR: [
          { username: 'admin' },
          { roleCode: 'ADMIN' }
        ]
      }
    })

    // 4. 检查权限点设置
    const requiredPermissions = ['role:create', 'role:view', 'role:update', 'role:delete', '*']
    const permissionCheck = requiredPermissions.map(p => ({
      permission: p,
      adminHasIt: adminRole ? adminRole.permissions.includes(p) : false
    }))

    return Response.json({
      success: true,
      roles,
      adminRole,
      users,
      permissionCheck,
      diagnosis: {
        adminRoleExists: !!adminRole,
        adminRoleCode: adminRole?.code,
        adminRolePermissions: adminRole?.permissions || [],
        hasWildcardPermission: adminRole?.permissions.includes('*') || false,
        hasRoleCreatePermission: adminRole?.permissions.includes('role:create') || false,
        adminUserExists: users.length > 0,
        adminUserCount: users.length,
        adminUsernames: users.map(u => u.username)
      }
    })

  } catch (error) {
    console.error('数据库检查失败:', error)
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : '检查过程中发生错误',
      details: error
    }, { status: 500 })
  }
} 