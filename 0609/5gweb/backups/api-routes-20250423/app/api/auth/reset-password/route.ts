import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"

/**
 * 重置密码的 API 路由
 *
 * 功能描述：
 * 1. 验证邮箱和验证码
 * 2. 更新用户密码
 * 3. 删除已使用的验证码
 *
 * 安全考虑：
 * 1. 验证码有效期检查
 * 2. 密码加密存储
 * 3. 错误信息模糊化
 */
export async function POST(request: Request) {
  try {
    const { email, verificationCode, newPassword } = await request.json()

    // 验证必填字段
    if (!email || !verificationCode || !newPassword) {
      return NextResponse.json(
        { success: false, message: "请填写所有必填字段" },
        { status: 400 }
      )
    }

    // 验证密码强度
    const hasLength = newPassword.length >= 8
    const hasUpperCase = /[A-Z]/.test(newPassword)
    const hasLowerCase = /[a-z]/.test(newPassword)
    const hasNumber = /[0-9]/.test(newPassword)

    if (!hasLength || !hasUpperCase || !hasLowerCase || !hasNumber) {
      return NextResponse.json(
        { success: false, message: "密码必须至少8个字符，并包含大小写字母和数字" },
        { status: 400 }
      )
    }

    // 查找验证码记录
    const codeRecord = await prisma.verificationCode.findFirst({
      where: {
        email,
        code: verificationCode,
        type: "RESET_PASSWORD",
        expiresAt: {
          gt: new Date()
        }
      }
    })

    if (!codeRecord) {
      return NextResponse.json(
        { success: false, message: "验证码无效或已过期" },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 更新密码
    const hashedPassword = await hash(newPassword, 12)
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword }
    })

    // 删除已使用的验证码
    await prisma.verificationCode.delete({
      where: { id: codeRecord.id }
    })

    return NextResponse.json({
      success: true,
      message: "密码重置成功"
    })

  } catch (error) {
    console.error("重置密码失败:", error)
    return NextResponse.json(
      { success: false, message: "重置密码失败，请稍后重试" },
      { status: 500 }
    )
  }
}