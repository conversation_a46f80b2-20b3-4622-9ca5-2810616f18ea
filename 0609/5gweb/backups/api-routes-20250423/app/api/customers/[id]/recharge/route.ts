/**
 * 客户充值API
 * 处理管理员对客户账户的充值操作
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 为客户充值
 *
 * @route POST /api/customers/[id]/recharge
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('充值API接收到的客户ID:', customerId)

    // 获取当前用户信息并验证权限
    // 1. 首先尝试获取 NextAuth 会话
    const session = await getServerSession(authOptions);
    console.log('充值API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID和角色
    let adminId = session?.user?.id;
    let userRole = session?.user?.roleCode;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!adminId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true, roleCode: true }
        });
        if (user) {
          adminId = user.id;
          userRole = user.roleCode;
          console.log('从数据库中查询到用户ID:', adminId, '角色:', userRole);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 2. 如果没有 NextAuth 会话，尝试使用JWT token
    if (!adminId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies()
      const token = cookieStore.get("token")?.value

      if (!token) {
        return NextResponse.json({
          success: false,
          message: '未授权访问'
        }, { status: 401 })
      }

      // 解析token获取管理员ID
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        adminId = payload.sub
        userRole = payload.roleCode as string
        console.log('从令牌中提取的用户ID:', adminId, '角色:', userRole);
      } catch (error) {
        console.error('令牌验证失败:', error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息'
        }, { status: 401 })
      }
    }

    // 检查管理员是否有权限
    if (!adminId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // 检查是否为管理员角色
    if (userRole !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: '没有权限执行充值操作'
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const { amount, paymentMethod, remarks } = body

    // 验证必填字段
    if (amount === undefined) {
      return NextResponse.json({
        success: false,
        message: '金额为必填项'
      }, { status: 400 })
    }

    // 查找客户
    const customer = await prisma.user.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return NextResponse.json({
        success: false,
        message: '客户不存在'
      }, { status: 404 })
    }

    // 计算新余额
    const newBalance = customer.balance + parseFloat(amount.toString())

    // 更新客户余额
    const updatedCustomer = await prisma.user.update({
      where: { id: customerId },
      data: {
        balance: newBalance
      },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    // 确定交易类型
    let transactionType = 'recharge'
    if (parseFloat(amount.toString()) < 0) {
      // 如果金额为负，则是扣费或清零操作
      if (newBalance === 0 && customer.balance > 0) {
        // 如果新余额为0且原余额大于0，则是清零操作
        transactionType = 'balance_reset'
      } else {
        transactionType = 'deduct'
      }
    }

    // 记录余额变动日志
    await prisma.balanceTransaction.create({
      data: {
        userId: customerId,
        amount: parseFloat(amount.toString()),
        balanceAfter: newBalance,
        type: transactionType,
        paymentMethod: paymentMethod || 'system',
        remarks: remarks || '',
        adminId: adminId,
      }
    })

    return NextResponse.json({
      success: true,
      message: transactionType === 'balance_reset' ? '余额清零成功' : (transactionType === 'recharge' ? '充值成功' : '扣费成功'),
      data: {
        balance: updatedCustomer.balance,
        customer: {
          id: updatedCustomer.id,
          username: updatedCustomer.username,
          name: updatedCustomer.name
        }
      }
    })
  } catch (error) {
    console.error("客户账户操作错误:", error)
    return NextResponse.json({
      success: false,
      message: '操作失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
