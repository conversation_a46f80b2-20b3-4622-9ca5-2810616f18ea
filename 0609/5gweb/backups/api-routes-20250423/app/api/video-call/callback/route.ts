import { type NextRequest, NextResponse } from "next/server"
import AesUtils from "@/lib/api/crypto"

/**
 * 外呼结果回调处理
 * 接收第三方API的回调请求，解密数据并处理
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json()

    // 获取加密数据
    const { data } = body

    if (!data) {
      return NextResponse.json({ errCode: 1000, errInfo: "缺少必要参数" }, { status: 400 })
    }

    // 解密数据
    const decryptedData = AesUtils.cbcDecrypt(data)

    // 解析JSON
    const callResult = JSON.parse(decryptedData)

    // 这里可以处理回调数据，例如保存到数据库
    console.log("收到外呼结果回调:", callResult)

    // 返回成功响应
    return NextResponse.json({ errCode: 0, errInfo: "" }, { status: 200 })
  } catch (error) {
    console.error("处理外呼结果回调错误:", error)

    // 返回错误响应
    return NextResponse.json({ errCode: 10, errInfo: "处理回调失败" }, { status: 500 })
  }
}

