import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import * as jose from 'jose';
import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";

// 使用与登录路由相同的密钥
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key");

/**
 * 获取单个通知详情
 * 
 * @route GET /api/notifications/[id]
 * @access private - 需要认证
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取通知ID
    const { id } = params;

    // 从 cookie 中获取 token
    const cookieStore = cookies();
    const token = cookieStore.get("token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "未授权访问", code: 401, data: null },
        { status: 401 }
      );
    }

    // 验证 token
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET);
      const userId = payload.sub;
      const roleCode = payload.roleCode;
      
      if (!userId) {
        return NextResponse.json(
          { success: false, message: "无效的令牌", code: 401, data: null },
          { status: 401 }
        );
      }

      // 检查权限
      const hasViewPermission = await hasResourcePermission(
        {sub: userId, role: { code: roleCode }},
        "notifications",
        "notifications:list"
      );

      if (!hasViewPermission) {
        return NextResponse.json(
          { success: false, message: "无权查看此通知", code: 403, data: null },
          { status: 403 }
        );
      }

      // 使用PrismaClient直接访问模型
      // 查询通知详情
      const notification = await prisma.$queryRaw`
        SELECT * FROM notification WHERE id = ${id}
      `;

      if (!notification || Array.isArray(notification) && notification.length === 0) {
        return NextResponse.json(
          { success: false, message: "通知不存在", code: 404, data: null },
          { status: 404 }
        );
      }

      // 转换为单个对象
      const notificationData = Array.isArray(notification) ? notification[0] : notification;

      // 查询用户与该通知的关联
      const userNotification = await prisma.$queryRaw`
        SELECT * FROM "user_notification" 
        WHERE "notificationId" = ${id} AND "userId" = ${userId}
      `;

      const userNotificationData = Array.isArray(userNotification) && userNotification.length > 0 
        ? userNotification[0] 
        : null;

      // 返回通知详情，包含已读状态
      return NextResponse.json({
        success: true,
        message: "获取通知详情成功",
        code: 200,
        data: {
          ...notificationData,
          read: userNotificationData?.read || false,
          readAt: userNotificationData?.readAt || null
        }
      });
    } catch (error) {
      console.error("Token验证失败:", error);
      return NextResponse.json(
        { success: false, message: "Token验证失败", code: 401, data: null },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("获取通知详情失败:", error);
    return NextResponse.json(
      { success: false, message: "获取通知详情失败", code: 500, data: null },
      { status: 500 }
    );
  }
}

/**
 * 更新通知
 * 
 * @route PUT /api/notifications/[id]
 * @access 管理员可访问
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log("更新通知API被调用");
  
  // 获取cookie中的token
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  
  if (!token) {
    console.log("更新通知 - 未找到token");
    return NextResponse.json({
      success: false,
      message: '未授权，请先登录',
    }, { status: 401 });
  }
  
  try {
    // 验证token
    console.log("验证token...");
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    const userId = payload.sub;
    const roleCode = payload.roleCode;
    
    if (!userId) {
      throw new Error('无效的用户ID');
    }
    
    // 检查用户是否有权限编辑通知
    const hasEditPermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:edit"
    );

    if (!hasEditPermission) {
      console.log("用户没有编辑通知的权限");
      return NextResponse.json({
        success: false,
        message: '权限不足，无法编辑通知',
      }, { status: 403 });
    }
    
    // 解析请求体
    const body = await req.json();
    const { title, content, type, priority } = body;
    
    // 验证必填字段
    if (!title || !content || !type) {
      return NextResponse.json({
        success: false,
        message: '标题、内容和通知类型不能为空',
      }, { status: 400 });
    }

    // 设置默认优先级，如果未提供
    const notificationPriority = priority || 'MEDIUM';
    
    // 更新通知
    console.log(`更新通知 ID: ${params.id}, type: ${type}, priority: ${notificationPriority}`);
    await prisma.$executeRaw`
      UPDATE notification 
      SET 
        title = ${title},
        content = ${content},
        "typeId" = ${type},
        priority = ${notificationPriority},
        "updatedAt" = NOW()
      WHERE id = ${params.id}
    `;
    
    return NextResponse.json({
      success: true,
      message: '更新通知成功',
    });
  } catch (error) {
    console.error("更新通知错误:", error);
    return NextResponse.json({
      success: false,
      message: '更新通知失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}

/**
 * 删除通知
 * 
 * @route DELETE /api/notifications/:id
 * @access private - 需要管理员权限
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户身份
    const cookieStore = cookies();
    const token = cookieStore.get("token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "未授权访问", code: 401, data: null },
        { status: 401 }
      );
    }

    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET);
      const userId = payload.sub;
      const roleCode = payload.roleCode;
      
      if (!userId) {
        return NextResponse.json(
          { success: false, message: "无效的令牌", code: 401, data: null },
          { status: 401 }
        );
      }

      // 检查权限
      const hasDeletePermission = await hasResourcePermission(
        { sub: userId, role: { code: roleCode } },
        "notifications",
        "notifications:delete"
      );

      if (!hasDeletePermission) {
        return NextResponse.json(
          { success: false, message: "无权删除通知", code: 403, data: null },
          { status: 403 }
        );
      }

      // 获取通知ID
      const { id } = params;

      // 验证通知是否存在
      const notification = await prisma.$queryRaw`
        SELECT * FROM notification WHERE id = ${id}
      `;

      if (!notification || Array.isArray(notification) && notification.length === 0) {
        return NextResponse.json(
          { success: false, message: "通知不存在", code: 404, data: null },
          { status: 404 }
        );
      }

      // 删除用户通知关联
      await prisma.$executeRaw`
        DELETE FROM "user_notification" WHERE "notificationId" = ${id}
      `;

      // 删除通知
      await prisma.$executeRaw`
        DELETE FROM notification WHERE id = ${id}
      `;

      return NextResponse.json({
        success: true,
        message: "删除通知成功",
        code: 200,
        data: null
      });

    } catch (error) {
      console.error("删除通知时发生错误:", error);
      return NextResponse.json(
        { success: false, message: "删除通知失败", code: 500, data: null },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("删除通知时发生错误:", error);
    return NextResponse.json(
      { success: false, message: "删除通知失败", code: 500, data: null },
      { status: 500 }
    );
  }
} 