import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuthMiddleware } from '@/lib/middleware/auth-middleware';

/**
 * 获取所有费率列表
 * @route GET /api/rates
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "费率管理API");
    if (response) {
      return response;
    }
    const rates = await prisma.rate.findMany({
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: rates
    });
  } catch (error) {
    console.error('获取费率列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取费率列表失败'
      },
      { status: 500 }
    );
  }
}

/**
 * 创建新费率
 * @route POST /api/rates
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "费率管理API");
    if (response) {
      return response;
    }
    const body = await request.json();
    const { amount, period, billingIncrement, customerId, businessType } = body;

    // 验证必填字段
    if (amount === undefined || !businessType) {
      return NextResponse.json(
        {
          success: false,
          message: '费率和业务类型不能为空'
        },
        { status: 400 }
      );
    }

    // 创建费率
    const rate = await prisma.rate.create({
      data: {
        amount: parseFloat(amount),
        period: period ? parseInt(period) : null,
        billingIncrement: billingIncrement || "PER_MINUTE",
        customerId,
        businessType
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: '费率创建成功',
      data: rate
    });
  } catch (error) {
    console.error('创建费率失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '创建费率失败'
      },
      { status: 500 }
    );
  }
}
