export interface BaseFormField {
  name: string
  label: string
  required?: boolean
}

export interface TextFormField extends BaseFormField {
  type: "text" | "textarea"
  value: string
  onChange: (value: string) => void
}

export interface SelectFormField extends BaseFormField {
  type: "select"
  options: { value: string; label: string }[]
  value: string
  onChange: (value: string) => void
}

export interface MultiSelectFormField extends BaseFormField {
  type: "select"
  options: { value: string; label: string }[]
  value: string[]
  onChange: (value: string[]) => void
  multiple: true
}

export interface IconFormField extends BaseFormField {
  type: "icon"
  value: string
  onChange: (value: string) => void
}

export type FormField = TextFormField | SelectFormField | MultiSelectFormField | IconFormField 