export interface SystemSettingsState {
  siteName: string
  logo: string
  footerText?: string
  theme: {
    primaryColor: string
    mode: "light" | "dark" | "system"
  }
  features: {
    enableRegistration: boolean
    enablePasswordReset: boolean
  }
  loginPage: {
    backgroundImage?: string
    title: string
    description: string
    features: Feature[]
  }
  menu: {
    items: MenuItem[]
    settings: {
      enableCustomIcons: boolean
      enableDragAndDrop: boolean
    }
  }
}

export interface Feature {
  name: string
  enabled: boolean
  description?: string
}

export interface MenuItem {
  id: string
  title: string
  path: string
  icon: string
  isVisible: boolean
  children: MenuItem[]
}

export interface AuthContextType {
  user: {
    id: string
    name: string
    email: string
    role: string
  } | null
  status: "loading" | "authenticated" | "unauthenticated"
}
