{
  "extends": ["next/core-web-vitals"],
  "rules": {
    // 完全禁用 console 语句检查
    "no-console": "off",
    // 完全禁用 alert 语句检查
    "no-alert": "off",
    // 完全禁用 debugger 语句检查
    "no-debugger": "off",
    // 完全禁用 React Hook 依赖检查
    "react-hooks/exhaustive-deps": "off",
    // 完全禁用 img 标签检查
    "@next/next/no-img-element": "off",
    // 禁用 module 变量赋值检查
    "@next/next/no-assign-module-variable": "off",
    // 禁用图片 alt 属性检查
    "jsx-a11y/alt-text": "off",
    // 禁用匿名默认导出检查
    "import/no-anonymous-default-export": "off"
  },
  "overrides": [
    {
      "files": ["**/*.dev.js", "**/*.dev.ts", "**/*.dev.tsx"],
      "rules": {
        "no-console": "off"
      }
    },
    {
      "files": ["**/api/**/*.ts", "**/api/**/*.js"],
      "rules": {
        "no-console": "off"
      }
    },
    {
      "files": ["**/*.test.js", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.js", "**/*.spec.ts", "**/*.spec.tsx"],
      "rules": {
        "no-console": "off",
        "no-alert": "off"
      }
    },
    {
      "files": ["app/api/logs/export/route.ts", "app/api/logs/route.ts"],
      "rules": {
        "@next/next/no-assign-module-variable": "off"
      }
    }
  ]
}
