import { NavItem } from "@/types/nav"

/**
 * 导航配置
 *
 * 权限控制说明：
 * 1. requiredRole: 指定需要的角色，如果不指定则所有角色都可以访问
 * 2. requiredPermissions: 指定需要的权限列表，如果不指定则不需要特定权限
 * 3. resource 和 action: 用于 ABAC 策略检查，如果指定则会调用策略检查接口
 */
export const navConfig = {
  main: [
    {
      title: "仪表盘",
      href: "/dashboard",
      icon: "dashboard",
      resource: "dashboard",
      action: "view",
    },
    {
      title: "任务管理",
      href: "/tasks",
      icon: "tasks",
      resource: "tasks",
      action: "view",
      children: [
        {
          title: "我的任务",
          href: "/tasks/my",
          icon: "my-tasks",
          resource: "tasks",
          action: "view",
          requiredRole: "USER",
        },
        {
          title: "任务列表",
          href: "/tasks/list",
          icon: "task-list",
          resource: "tasks",
          action: "manage",
          requiredRole: "ADMIN",
        },
      ],
    },
    {
      title: "通知中心",
      href: "/notifications",
      icon: "notifications",
      resource: "notifications",
      action: "view",
      requiredPermissions: ["notification:read"],
    },
    {
      title: "系统设置",
      href: "/settings",
      icon: "settings",
      resource: "settings",
      action: "manage",
      requiredRole: "ADMIN",
      children: [
        {
          title: "用户管理",
          href: "/settings/users",
          icon: "users",
          resource: "users",
          action: "manage",
          requiredRole: "ADMIN",
        },
        {
          title: "角色管理",
          href: "/settings/roles",
          icon: "roles",
          resource: "roles",
          action: "manage",
          requiredRole: "ADMIN",
        },
        {
          title: "权限管理",
          href: "/settings/permissions",
          icon: "permissions",
          resource: "permissions",
          action: "manage",
          requiredRole: "ADMIN",
        },
        {
          title: "系统日志",
          href: "/admin/logs",
          icon: "logs",
          resource: "logs",
          action: "view",
          requiredRole: "ADMIN",
        },
      ],
    },
  ],
} as const