/**
 * 系统菜单配置
 * 这个文件定义了系统的菜单结构和默认权限
 */

import { Menu } from '@/types/menu'

// 定义配置使用的MenuConfig接口
export interface MenuConfig {
  code: string;
  name: string;
  path: string;
  icon?: string;
  order: number;
  visible: boolean;
  parentId?: string;
  children?: MenuConfig[];
  id?: string; // id是可选的，因为在配置时可能不需要提供
}

// 系统菜单配置
export const systemMenus: MenuConfig[] = [
  {
    code: 'settings',
    name: '网站设置',
    path: '/settings',
    icon: 'Settings',
    order: 100,
    visible: true,
    children: [
      {
        code: 'basic-settings',
        name: '基本信息',
        path: '/settings?tab=basic',
        icon: 'InfoCircleOutlined',
        order: 110,
        visible: true,
      },
      {
        code: 'theme-settings',
        name: '主题设置',
        path: '/settings?tab=theme',
        icon: 'BgColorsOutlined',
        order: 120,
        visible: true,
      },
      {
        code: 'feature-settings',
        name: '功能开关',
        path: '/settings?tab=features',
        icon: 'ControlOutlined',
        order: 130,
        visible: true,
      },
      {
        code: 'security-settings',
        name: '安全设置',
        path: '/settings?tab=security',
        icon: 'SafetyOutlined',
        order: 140,
        visible: true,
      },
      {
        code: 'login-settings',
        name: '登录页设置',
        path: '/settings?tab=loginPage',
        icon: 'LoginOutlined',
        order: 150,
        visible: true,
      },
      {
        code: 'menu-settings',
        name: '菜单设置',
        path: '/settings?tab=menu',
        icon: 'menu',
        order: 160,
        visible: true,
      },
      {
        code: 'roles',
        name: '角色管理',
        path: '/settings/roles',
        icon: 'Shield',
        order: 170,
        visible: true,
      },
      {
        code: 'logs',
        name: '系统日志',
        path: '/settings/logs',
        icon: 'FileText',
        order: 180,
        visible: true,
      }
    ]
  },
  {
    code: 'customer-management',
    name: '客户管理',
    path: '/customer-management',
    icon: 'TeamOutlined',
    order: 50,
    visible: true,
    children: [
      {
        code: 'customer-accounts',
        name: '客户账户',
        path: '/customers',
        icon: 'Users',
        order: 51,
        visible: true,
      },
      {
        code: 'rates',
        name: '费率管理',
        path: '/rates',
        icon: 'DollarOutlined',
        order: 52,
        visible: true,
      }
    ]
  }
]

// 系统角色配置
export const systemRoles = {
  ADMIN: 'ADMIN', // 管理员角色代码
  USER: 'USER',   // 普通用户角色代码
}

// 获取所有菜单（扁平化）
export function getAllMenus(): MenuConfig[] {
  const allMenus: MenuConfig[] = []

  function flattenMenus(menus: MenuConfig[], parentId: string | null = null) {
    menus.forEach(menu => {
      const menuCopy = { ...menu }
      if (parentId) {
        menuCopy.parentId = parentId
      }

      const { children, ...menuWithoutChildren } = menuCopy
      allMenus.push(menuWithoutChildren)

      if (children && children.length > 0) {
        flattenMenus(children, menuWithoutChildren.code)
      }
    })
  }

  flattenMenus(systemMenus)
  return allMenus
}

// 获取角色默认菜单
export function getDefaultMenusForRole(roleCode: string): string[] {
  // 管理员可以访问所有菜单
  if (roleCode === systemRoles.ADMIN) {
    return getAllMenus().map(menu => menu.code)
  }

  // 普通用户只能访问部分菜单
  if (roleCode === systemRoles.USER) {
    return ['system', 'users'] // 示例：普通用户只能访问系统和用户管理
  }

  return [] // 其他角色默认没有菜单权限
}
