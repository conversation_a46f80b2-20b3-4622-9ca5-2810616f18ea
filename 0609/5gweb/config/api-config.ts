// 5G视频外呼API配置
export const VIDEO_CALL_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000",
  ORG_CODE: process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg",
  LOGIN_NAME: process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001",
  AES_KEY: process.env.NEXT_PUBLIC_VIDEO_CALL_AES_KEY || "f4d9145303db415a",
  AES_IV: process.env.NEXT_PUBLIC_VIDEO_CALL_AES_IV || "6778fd576ed3dfff",
  APP_ID: process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || "",
}

// API端点配置
export const API_ENDPOINTS = {
  // 外呼任务相关
  CALL_TASK: {
    CREATE: "/api/mediaDeliverPlatform/external/create",
    LIST: "/openapi/callout/taskList",
    DETAIL: "/openapi/callout/task",
    STATS: "/openapi/task/panels",
  },
  // 客户名单相关
  CUSTOMER: {
    CREATE_BATCH: "/openapi/customer/task/v2",
    IMPORT: "/openapi/customer/v2",
    LEGACY_IMPORT: "/cms/api/customer/external/create",
  },
  // 黑名单相关
  BLACKLIST: {
    GROUPS: "/openapi/blackList/groups",
    IMPORT: "/openapi/blackList/group/phones",
  },
  // 短信相关
  SMS: {
    VIDEO_SMS: "/api/videoSms/external/create",
  },
  // Bot相关
  BOT: {
    LIST: "/api/bots/external",
    DETAIL: "/api/bot/detail/external",
    INTENT_SETTINGS: "/openapi/videoBot/intent/settings",
    FLOW_EXCEL: "/openapi/videoBot/flow/exportExcelDoc",
    FLOW_CONTENT: "/openapi/videoBot/flow/contentInfo",
  },
}

