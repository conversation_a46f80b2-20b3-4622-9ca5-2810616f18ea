# 更新日志

## 2025-04-29
- 优化个人资料页面排版和布局
  - 采用网格布局显示用户信息标签，提升空间利用率
  - 使用柔和的背景色和边框替代原有的高饱和度颜色
  - 添加图标到标签中，增强可识别性和美观性
  - 根据认证状态智能调整标签布局，避免信息重复
  - 优化标签大小和间距，使整体布局更加协调美观

## 2025-04-28
- 完善个人资料页面信息显示
  - 在个人资料页面添加角色、用户类型和认证状态的显示
  - 优化标签样式，使用高对比度颜色提升可读性
  - 完善用户资料API，增加认证状态和类型字段
  - 优化个人信息展示布局，增强用户体验

## 2025-04-27
- 优化用户认证状态显示
  - 改进认证状态标签，显示认证类型和状态
  - 优化标签颜色和样式，增强可读性
  - 区分个人认证和企业认证的显示

## 2025-04-26
- 修复用户管理页面问题
  - 修复用户类型和角色显示不正确的问题
  - 优化用户状态标签的颜色和样式
  - 实现用户启用/禁用功能，包含禁用原因输入
  - 添加用户启用对话框，提升用户体验
  - 清理模拟数据和不必要的代码，优化性能

## 2025-04-25
- 移除用户管理页面折叠按钮
  - 从用户管理页面移除折叠按钮，使页面更加简洁
  - 修改DashboardHeader组件，移除SidebarToggle组件
  - 保持页面布局的一致性和美观性

## 2025-04-24
- 优化用户管理页面布局
  - 使用户管理页面内容居中显示，提升美观度
  - 优化DashboardShell组件，使所有使用该组件的页面内容居中
  - 为用户列表卡片添加渐变背景和阴影效果
  - 优化表格样式，增强视觉层次感
  - 优化搜索和筛选区域样式，增强用户体验

## 2025-04-23
- 彻底解决滚动条问题
  - 修改Tabs组件源码，添加overflow-hidden类，从根本上解决滚动条问题
  - 为body元素添加overflow-x-hidden类，防止水平滚动
  - 为所有TabsContent组件添加overflow-hidden类，确保内容不会溢出
  - 优化TabsList组件，将overflow-x-auto改为overflow-hidden

## 2025-04-22
- 修复个人中心页面问题
  - 修复修改昵称点击保存时的toFixed错误
  - 进一步优化页面布局，使内容更加居中
  - 彻底解决滚动条问题，优化页面显示
  - 调整内容布局，使各个选项卡页面更加协调

## 2025-04-21
- 优化个人中心页面界面
  - 移除个人中心页面标题旁边的折叠按钮，使页面更加简洁
  - 解决页面右侧出现滚动条的问题，优化页面布局
  - 自定义个人中心组件，提升页面性能和用户体验

## 2025-04-20
- 优化个人中心页面视觉效果
  - 为选项卡导航添加丰富的颜色，每个选项卡使用不同的主题色
  - 为选项卡添加渐变背景和边框效果，增强视觉层次
  - 优化个人中心背景，添加渐变效果和边框阴影
  - 为内容区域添加半透明白色背景和圆角效果
  - 为页面切换添加淡入动画效果，提升用户体验

## 2025-04-19
- 优化导出功能
  - 将登录历史页面的导出按钮改为绿色，增强视觉效果
  - 在余额记录页面添加导出功能，支持CSV和Excel格式
  - 统一导出按钮的样式和交互体验

## 2025-04-18
- 优化个人中心页面布局
  - 修复选项卡显示不完整的问题，确保所有选项卡能够完整显示
  - 优化选项卡内容区域，使其居中显示
  - 恢复邮箱修改功能，添加邮箱修改按钮和验证流程
  - 实现邮箱修改验证码发送和验证功能
  - 为所有选项卡内容添加最大宽度和居中对齐，使内容完整显示
  - 优化登录历史和余额记录页面的布局，增加最大宽度限制
  - 重新设计登录历史页面的搜索和筛选区域，使其更加有条理
  - 优化余额记录页面的筛选工具栏，增强视觉层次和用户体验

## 2025-04-17
- 优化个人资料页面
  - 将"姓名"字段改为"昵称"，更符合用户习惯
- 增强用户管理功能
  - 添加用户状态操作功能，支持启用和禁用用户账户
  - 禁用用户时需要填写原因，系统会通过邮件和应用内通知发送给用户
  - 添加用户状态日志表，记录用户状态变更历史
  - 优化用户状态显示，使用绿色表示启用状态，红色表示禁用状态
  - 管理员无法禁用自己的账户，确保系统安全
  - 遵循ABAC权限模型，只有拥有相应权限的管理员才能操作用户状态

## 2025-04-16
- 完全解决了邮箱验证码发送和验证问题
  - 修复了发送验证码时"未登录或会话已过期"的错误
  - 修复了验证邮箱时"未登录或会话已过期"的错误
  - 实现了多种认证方式，支持从请求头和Cookie中获取token
  - 增强了前端错误处理，提供更详细的错误信息
  - 优化了日志记录，便于调试和问题定位
- 使用 area-data 开源库替换原有的省市区数据
  - 添加了 area-data 库的适配器，将其数据结构转换为当前项目使用的格式
  - 保持了原有的 API 接口，不影响现有代码
  - 提供了更完整的省市区数据
- 优化用户管理功能
  - 将"客户账户"菜单名称改为"用户管理"
  - 移除了演示数据，使用空数组初始化
  - 更新了页面标题、描述和标签文本
  - 修复了路径不匹配问题，添加了重定向页面
  - 创建了添加用户页面，支持创建新用户
  - 添加了用户认证状态显示，包括个人认证和企业认证
  - 修复了sidebar组件的错误，添加了useSidebar函数
  - 添加了管理员用户类型，确保系统显示所有用户包括管理员
  - 更新了用户类型筛选选项，添加了管理员类型
  - 统一了页面中的术语，将"客户账户"改为"用户"
  - 在用户列表中添加了认证状态和角色列
  - 优化了用户数据结构，添加了认证状态和角色字段
  - 更新了导出功能，包含认证状态和角色信息
  - 将添加用户改为弹出对话框形式，减少页面跳转
  - 移除了添加用户页面中的详细地址字段
  - 添加了密码验证规则，确保与注册和修改密码页面一致
  - 优化了添加用户对话框中的密码强度检测和显示
  - 移除了密码中特殊字符的要求，与系统其他页面保持一致
  - 添加了密码显示/隐藏功能，提高用户体验
  - 修复了添加用户后用户列表不更新的问题
  - 优化了添加用户对话框和用户管理页面之间的数据传递
  - 将用户数据存储从本地存储改为数据库存储，提高数据安全性和可靠性
  - 添加了用户名和邮箱的唯一性检查，防止重复添加用户
  - 添加了用户添加成功后的提示信息
  - 修复了登录时"用户不存在"的问题，实现了数据库用户验证
  - 实现了密码加密存储，提高了系统安全性
  - 添加了密码工具函数，统一密码加密和验证逻辑
  - 修复了登录页面中的变量重复定义错误
  - 实现了管理员添加用户API，遵循ABAC权限模型
  - 修复了添加用户时的验证码问题，管理员无需验证码即可添加用户
  - 优化了错误处理和提示信息，提高用户体验
  - 在数据库模型中添加了用户余额和状态字段
  - 在个人资料页面中显示用户余额和状态
  - 实现了管理员充值功能，遵循ABAC权限模型
  - 添加了用户充值对话框，支持充值和扣费操作
  - 实现了用户余额管理API，支持查询和更新余额
  - 美化了个人资料中的余额和状态显示，使用彩色标签
  - 添加了余额交易记录选项卡，支持查询交易历史
  - 统一了用户列表和个人资料中的角色显示
  - 优化了用户类型显示，区分管理员/普通用户和个人/企业用户
  - 移除了用户列表中PDF格式导出选项

## 2025-04-15
- 全面修复了邮箱验证码获取问题
  - 修复了"未登录或会话已过期"的错误
  - 重构了认证逻辑，直接从请求中获取和验证令牌
  - 增强了错误处理逻辑，提供更详细的错误信息
  - 更新了验证码服务，添加对"email_change"类型的支持

## 2025-04-14
- 修复了邮箱验证码获取问题
  - 修复了"未登录或会话已过期"的错误
  - 使用 AuthService 替代 getServerSession 进行认证
  - 增强了错误处理逻辑，提供更详细的错误信息
- 修复了省份数据重复定义的问题
  - 移除了旧的 provinces 定义
  - 仅使用从 china-regions 导入的省市区数据
  - 修复了模块导入路径错误

## 2025-04-13
- 修复了邮箱验证码获取问题
  - 添加了邮箱修改验证码发送功能
  - 优化了验证码发送流程
- 实现了真实的省市区选择功能
  - 添加了中国省市区数据
  - 实现了级联选择功能
  - 当选择省份时自动加载对应的城市
  - 当选择城市时自动加载对应的区县
- 统一了按钮颜色为蓝色
  - 将保存更改按钮和更改密码按钮的颜色统一为蓝色
  - 与认证资料提交按钮保持一致的样式

## 2025-04-12
- 增加了认证类型变更功能
  - 实现了管理员可以变更用户的认证类型（个人认证和企业认证之间的切换）
  - 管理员变更用户认证类型时需要填写变更原因
  - 用户需要在规定时间内（3天）补充相关资料
  - 如果用户未在规定时间内补充资料，系统会自动恢复到原始认证状态
  - 系统会通过通知提醒用户补充资料
- 优化了认证资料页面
  - 当用户提交了个人认证资料时，企业认证选项卡不再显示"企业认证审核中"状态
  - 用户只能选择一种认证类型进行认证

## 2025-04-11
- 增加了用户认证资料功能
  - 实现了个人认证和企业认证功能
  - 支持上传身份证、营业执照等认证材料
  - 支持上传其他资质和附件
  - 显示认证状态，包括待审核、已认证、未通过等
- 优化了邮箱修改功能
  - 增加了邮箱验证码功能，需要验证码才能修改邮箱
  - 优化了邮件模板，使邮件主题和内容更加明确
- 完善了用户个人资料
  - 增加了所在省市区的选择功能
  - 增加了详细地址输入框
  - 增加了手机和微信号输入功能

## 2025-04-09
- 优化了用户界面和交互体验
  - 将用户菜单中的"账户设置"改为"修改密码"，并直接跳转到密码修改页面
  - 优化了用户页面的URL参数处理，支持直接跳转到指定选项卡
- 优化了登录历史页面的显示效果
  - 添加了登录失败原因显示，包括用户名错误和密码错误等具体原因
  - 优化了状态显示的样式，修复了变形问题
  - 改进了IP地址和位置信息的显示方式
  - 优化了表格布局，使其更加整齐美观
  - 改进了设备信息显示，实现自适应内容长度的展示方式
  - 优化了表格列宽比例，为设备信息列分配更多空间
  - 修复了日期选择器无法响应点击的问题，实现了日期范围筛选功能
  - 实现了日期选择器的中文化显示，包括中文星期和中文日期格式
  - 实现了登录历史导出功能，支持CSV和Excel格式，并包含完整的登录信息
  - 修复了Excel格式导出时出现乱码的问题，使用XML格式生成兼容的Excel文件

## 2025-04-09
- 修复了管理员批量标记通知已读功能，使其与单个标记已读和未读计数API保持一致
- 管理员现在可以查看和标记所有通知为已读，而不仅限于发送给自己的通知
- 修复了通知按钮组件中未读数量显示不一致的问题，统一使用自管理状态的通知按钮组件
- 修复了通知列表API，使管理员可以看到所有通知（包括全局通知和指定用户通知）

## 2025-04-10
- 修复了通知中心的逻辑问题
  - 管理员可以查看所有通知（包括全局通知和指定用户通知）
  - 管理员的未读通知计数和标记已读功能只包括全局通知和发送给管理员的通知
  - 普通用户只能查看和标记发送给自己的通知为已读（包括全局通知和指定用户通知）
- 修复了未读通知计数API，使其与标记已读功能保持一致
- 修复了标记所有通知为已读功能，使其与通知列表显示保持一致
- 修复了管理员通知角标显示问题，现在只显示全局通知和发送给管理员的通知的未读数量
- 修复了通知统计页面中通知类型分布图和优先级分布图的显示问题，即使没有数据也能显示友好的提示信息

## 2025-04-08
- 系统初始版本发布
